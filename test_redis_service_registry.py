#!/usr/bin/env python3
"""
Test script for Redis Service Registry

This script tests the Redis-backed service registry implementation
with your Redis instance.
"""

import asyncio
import json
import time
from datetime import datetime, timezone
from typing import Dict, Any

from core.infrastructure.redis_service_registry import RedisServiceRegistry


class MockService:
    """Mock service for testing"""

    def __init__(self, name: str, port: int):
        self.name = name
        self.port = port
        self.url = f"http://localhost:{port}"
        self.version = "1.0.0"
        self.is_running = False

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "url": self.url,
            "version": self.version,
            "health_endpoint": "/health",
            "metadata": {
                "port": self.port,
                "test_service": True
            }
        }


async def test_redis_service_registry():
    """Test Redis service registry functionality"""

    # Redis connection from your AWS MemoryDB instance
    redis_url = "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"

    print("🔧 Testing Redis Service Registry")
    print("=" * 50)

    # Initialize registry
    print("\n1. Initializing Redis Service Registry...")
    registry = RedisServiceRegistry(
        redis_url=redis_url,
        key_prefix="test_service_registry:",
        default_ttl=60,  # 1 minute for testing
        health_check_interval=10  # 10 seconds for testing
    )

    try:
        # Test 1: Service Registration
        print("\n2. Testing Service Registration...")
        mock_services = [
            MockService("translation-service", 8001),
            MockService("auth-service", 8002),
            MockService("notification-service", 8003)
        ]

        registered_services = []
        for service in mock_services:
            service_data = service.to_dict()
            result = await registry.register_service(
                name=service_data["name"],
                url=service_data["url"],
                version=service_data["version"],
                health_endpoint=service_data["health_endpoint"],
                metadata=service_data["metadata"]
            )

            if result["success"]:
                print(f"   ✅ Registered: {service.name}")
                print(f"      API Key: {result['api_key'][:20]}...")
                print(f"      TTL: {result['ttl']} seconds")
                registered_services.append(service.name)
            else:
                print(f"   ❌ Failed to register {service.name}: {result['error']}")

        # Test 2: List Services
        print(f"\n3. Testing Service Discovery...")
        services = await registry.list_services()
        print(f"   Found {len(services)} services:")
        for service in services:
            print(f"   • {service['name']} ({service['version']}) - {service['url']}")
            print(f"     Registered: {service['registered_at']}")
            print(f"     Healthy: {'✅' if service['is_healthy'] else '❌'}")

        # Test 3: Get Specific Service
        print(f"\n4. Testing Service Lookup...")
        if registered_services:
            service_name = registered_services[0]
            service = await registry.get_service(service_name)
            if service:
                print(f"   ✅ Found service: {service_name}")
                print(f"      URL: {service['url']}")
                print(f"      Version: {service['version']}")
                print(f"      API Key: {service['api_key'][:20]}...")
                print(f"      Metadata: {service['metadata']}")
            else:
                print(f"   ❌ Service not found: {service_name}")

        # Test 4: Heartbeat
        print(f"\n5. Testing Heartbeat...")
        if registered_services:
            service_name = registered_services[0]
            result = await registry.heartbeat(service_name)
            if result["success"]:
                print(f"   ✅ Heartbeat successful for {service_name}")
                print(f"      Next heartbeat: {result['next_heartbeat']}")
            else:
                print(f"   ❌ Heartbeat failed: {result['error']}")

        # Test 5: Service Statistics
        print(f"\n6. Testing Service Statistics...")
        stats = await registry.get_service_stats()
        print(f"   Total services: {stats['total_services']}")
        print(f"   Healthy services: {stats['healthy_services']}")
        print(f"   Unhealthy services: {stats['unhealthy_services']}")
        print(f"   Health percentage: {stats['health_percentage']:.1f}%")

        # Test 6: Distributed Locking
        print(f"\n7. Testing Distributed Locking...")
        if registered_services:
            service_name = registered_services[0]

            # Try to register the same service again (should be blocked by lock)
            result = await registry.register_service(
                name=service_name,
                url="http://localhost:9999",
                version="2.0.0"
            )

            if not result["success"] and "lock" in result["error"].lower():
                print(f"   ✅ Distributed locking working correctly")
                print(f"      Error: {result['error']}")
            else:
                print(f"   ⚠️  Unexpected result: {result}")

        # Test 7: Health Monitoring (start background task)
        print(f"\n8. Testing Health Monitoring...")
        await registry.start_monitoring()
        print(f"   ✅ Health monitoring started")
        print(f"   ⏱️  Waiting 15 seconds to observe health checks...")

        # Wait and check health updates
        await asyncio.sleep(15)

        services_after_monitoring = await registry.list_services()
        print(f"   Services after monitoring:")
        for service in services_after_monitoring:
            last_check = service.get('last_health_check')
            if last_check:
                print(f"   • {service['name']}: Last check at {last_check}")
            else:
                print(f"   • {service['name']}: No health check yet")

        # Test 8: Service Cleanup
        print(f"\n9. Testing Service Cleanup...")
        cleanup_result = await registry.cleanup_expired_services()
        if cleanup_result["success"]:
            print(f"   ✅ Cleanup completed")
            print(f"      Expired services cleaned: {cleanup_result['expired_services_cleaned']}")
        else:
            print(f"   ❌ Cleanup failed: {cleanup_result['error']}")

        # Test 9: Service Unregistration
        print(f"\n10. Testing Service Unregistration...")
        for service_name in registered_services:
            result = await registry.unregister_service(service_name)
            if result["success"]:
                print(f"   ✅ Unregistered: {service_name}")
            else:
                print(f"   ❌ Failed to unregister {service_name}: {result['error']}")

        # Final verification
        print(f"\n11. Final Verification...")
        final_services = await registry.list_services()
        print(f"   Services remaining: {len(final_services)}")

        if len(final_services) == 0:
            print(f"   ✅ All test services cleaned up successfully")
        else:
            print(f"   ⚠️  Some services still remain:")
            for service in final_services:
                print(f"      • {service['name']}")

        print(f"\n🎉 Redis Service Registry Test Complete!")
        print("=" * 50)
        print("✅ All core functionality tested successfully")
        print("✅ Distributed locking working")
        print("✅ Health monitoring operational")
        print("✅ TTL and cleanup working")
        print("✅ Service discovery functional")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Cleanup
        await registry.stop_monitoring()
        await registry.dispose()
        print(f"\n🧹 Registry resources cleaned up")


async def test_redis_connection():
    """Test basic Redis connectivity"""
    redis_url = "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redis-cloud.com:10401"

    print("🔍 Testing Redis Connection")
    print("=" * 30)

    try:
        import redis.asyncio as aioredis

        redis_client = await aioredis.from_url(redis_url, encoding="utf-8", decode_responses=True)

        # Test basic operations
        await redis_client.set("test_key", "test_value", ex=10)
        value = await redis_client.get("test_key")

        if value == "test_value":
            print("✅ Redis connection successful")
            print(f"   URL: {redis_url}")
            print(f"   Test key/value operation: OK")
        else:
            print("❌ Redis test failed - unexpected value")

        # Cleanup
        await redis_client.delete("test_key")
        await redis_client.close()

    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

    return True


async def main():
    """Main test function"""
    print("🚀 Redis Service Registry Test Suite")
    print("=" * 60)

    # Test Redis connection first
    if await test_redis_connection():
        print()
        await test_redis_service_registry()
    else:
        print("❌ Cannot proceed with service registry tests - Redis connection failed")


if __name__ == "__main__":
    asyncio.run(main())
