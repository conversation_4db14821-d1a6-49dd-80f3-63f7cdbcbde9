﻿#!/usr/bin/env python3
# monitor.py - Terminal monitoring tool for FastAPI Core Framework

import asyncio
import httpx
import rich
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text
from rich.box import ROUNDED
from datetime import datetime
import argparse
import os
from typing import Dict, Any, List


class ServiceMonitor:
    """Terminal monitoring tool for microservices"""
    
    def __init__(self, core_url: str, api_key: str):
        self.core_url = core_url
        self.api_key = api_key
        self.console = Console()
        self.services_data = {}
        self.metrics_data = {}
        self.health_data = {}
        
    def create_header(self) -> Panel:
        """Create header panel"""
        header_text = Text()
        header_text.append("🚀 FastAPI Core Framework Monitor\n", style="bold cyan")
        header_text.append(f"Core API: {self.core_url}\n", style="dim")
        header_text.append(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
        
        return Panel(header_text, box=ROUNDED, style="cyan")
    
    def create_services_table(self) -> Table:
        """Create services status table"""
        table = Table(title="📡 Services Status", box=ROUNDED)
        
        table.add_column("Service", style="cyan", no_wrap=True)
        table.add_column("Version", style="magenta")
        table.add_column("URL", style="blue")
        table.add_column("Status", justify="center")
        table.add_column("Response Time", justify="right")
        table.add_column("Last Check", style="dim")
        
        for service_name, service_info in self.services_data.items():
            # Get health status for this service
            health = self.health_data.get('details', [])
            service_health = next((h for h in health if h.get('service') == service_name), {})
            
            status = service_health.get('status', 'unknown')
            status_emoji = {
                'healthy': '✅',
                'unhealthy': '❌',
                'degraded': '⚠️',
                'unknown': '❓'
            }.get(status, '❓')
            
            response_time = service_health.get('response_time', 0)
            if response_time:
                response_time_str = f"{response_time*1000:.0f}ms"
            else:
                response_time_str = "-"
            
            last_check = service_health.get('last_check', '')
            if last_check:
                last_check_time = datetime.fromisoformat(last_check.replace('Z', '+00:00'))
                last_check_str = last_check_time.strftime('%H:%M:%S')
            else:
                last_check_str = "-"
            
            table.add_row(
                service_name,
                service_info.get('version', '-'),
                str(service_info.get('url', '-')),
                f"{status_emoji} {status}",
                response_time_str,
                last_check_str
            )
        
        if not self.services_data:
            table.add_row("No services registered", "-", "-", "-", "-", "-")
        
        return table
    
    def create_health_panel(self) -> Panel:
        """Create overall health panel"""
        health_text = Text()
        
        overall_status = self.health_data.get('status', 'unknown')
        status_emoji = {
            'healthy': '✅',
            'unhealthy': '❌',
            'degraded': '⚠️',
            'unknown': '❓'
        }.get(overall_status, '❓')
        
        health_text.append(f"{status_emoji} Overall Status: ", style="bold")
        health_text.append(f"{overall_status.upper()}\n\n", style=f"bold {self.get_status_color(overall_status)}")
        
        services_info = self.health_data.get('services', {})
        health_text.append(f"Total Services: {services_info.get('total', 0)}\n")
        health_text.append(f"✅ Healthy: {services_info.get('healthy', 0)}\n", style="green")
        health_text.append(f"❌ Unhealthy: {services_info.get('unhealthy', 0)}\n", style="red")
        
        return Panel(health_text, title="🏥 System Health", box=ROUNDED)
    
    def create_metrics_panel(self) -> Panel:
        """Create metrics panel"""
        metrics_text = Text()
        
        if self.metrics_data:
            uptime = self.metrics_data.get('uptime_seconds', 0)
            hours = int(uptime // 3600)
            minutes = int((uptime % 3600) // 60)
            seconds = int(uptime % 60)
            
            metrics_text.append(f"⏱️  Uptime: {hours}h {minutes}m {seconds}s\n\n", style="bold")
            
            # Show some key counters
            counters = self.metrics_data.get('counters', {})
            if counters:
                metrics_text.append("📊 Request Counters:\n", style="bold")
                for key, value in list(counters.items())[:5]:  # Show top 5
                    metrics_text.append(f"  {key}: {value}\n")
            
            # Show gauges
            gauges = self.metrics_data.get('gauges', {})
            if gauges:
                metrics_text.append("\n📈 Gauges:\n", style="bold")
                for key, value in list(gauges.items())[:5]:
                    metrics_text.append(f"  {key}: {value:.2f}\n")
        else:
            metrics_text.append("No metrics available", style="dim")
        
        return Panel(metrics_text, title="📊 Metrics", box=ROUNDED)
    
    def create_ascii_art(self) -> Panel:
        """Create ASCII art visualization"""
        art = """
    ╔═══════════════════════════════════╗
    ║      🚀 FASTAPI CORE SYSTEM 🚀    ║
    ╠═══════════════════════════════════╣
    ║         ┌─────────────┐           ║
    ║         │  CORE API   │           ║
    ║         └──────┬──────┘           ║
    ║                │                  ║
    ║     ┌──────────┴──────────┐       ║
    ║     │                     │       ║
    ║ ┌───▼───┐           ┌────▼────┐  ║
    ║ │SERVICE│           │SERVICE  │  ║
    ║ │   1   │           │   2     │  ║
    ║ └───────┘           └─────────┘  ║
    ╚═══════════════════════════════════╝
        """
        return Panel(art, title="🎨 System Architecture", box=ROUNDED, style="green")
    
    def get_status_color(self, status: str) -> str:
        """Get color for status"""
        return {
            'healthy': 'green',
            'unhealthy': 'red',
            'degraded': 'yellow',
            'unknown': 'dim'
        }.get(status, 'white')
    
    async def fetch_data(self):
        """Fetch all data from core API"""
        headers = {"X-API-Key": self.api_key}
        
        async with httpx.AsyncClient() as client:
            try:
                # Fetch services
                services_response = await client.get(
                    f"{self.core_url}/api/v1/services",
                    headers=headers
                )
                if services_response.status_code == 200:
                    services = services_response.json().get('services', [])
                    self.services_data = {s['name']: s for s in services}
                
                # Fetch health
                health_response = await client.get(
                    f"{self.core_url}/api/v1/health"
                )
                if health_response.status_code == 200:
                    self.health_data = health_response.json()
                
                # Fetch metrics
                metrics_response = await client.get(
                    f"{self.core_url}/api/v1/metrics"
                )
                if metrics_response.status_code == 200:
                    self.metrics_data = metrics_response.json()
                    
            except Exception as e:
                self.console.print(f"[red]Error fetching data: {e}[/red]")
    
    def create_layout(self) -> Layout:
        """Create the main layout"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=1)
        )
        
        layout["left"].split_column(
            Layout(name="services", ratio=2),
            Layout(name="metrics", ratio=1)
        )
        
        # Update content
        layout["header"].update(self.create_header())
        layout["services"].update(self.create_services_table())
        layout["right"].update(self.create_health_panel())
        layout["metrics"].update(self.create_metrics_panel())
        layout["footer"].update(self.create_ascii_art())
        
        return layout
    
    async def run(self, refresh_interval: int = 5):
        """Run the monitor"""
        with Live(self.create_layout(), refresh_per_second=1, console=self.console) as live:
            while True:
                await self.fetch_data()
                live.update(self.create_layout())
                await asyncio.sleep(refresh_interval)


def main():
    parser = argparse.ArgumentParser(description="Monitor FastAPI Core Framework")
    parser.add_argument(
        "--url",
        default=os.getenv("CORE_API_URL", "http://localhost:8000"),
        help="Core API URL"
    )
    parser.add_argument(
        "--api-key",
        default=os.getenv("CORE_API_KEY", ""),
        help="API Key for authentication"
    )
    parser.add_argument(
        "--refresh",
        type=int,
        default=5,
        help="Refresh interval in seconds"
    )
    
    args = parser.parse_args()
    
    if not args.api_key:
        console = Console()
        console.print("[red]Error: API key is required. Use --api-key or set CORE_API_KEY environment variable[/red]")
        return
    
    monitor = ServiceMonitor(args.url, args.api_key)
    
    try:
        asyncio.run(monitor.run(args.refresh))
    except KeyboardInterrupt:
        console = Console()
        console.print("\n[yellow]Monitor stopped by user[/yellow]")


if __name__ == "__main__":
    main()