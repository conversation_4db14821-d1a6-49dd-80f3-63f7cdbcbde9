﻿#example of .env
# Cloudflare Account Settings
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_API_TOKEN=
CLOUDFLARE_STORE_ID=

# Secret Management Configuration
SECRET_PROVIDER=auto
SECRET_CACHE_TTL=300
FALLBACK_TO_ENV=true
# Note: Will auto-detect Cloudflare when Secrets Store becomes available

# Application Secrets (these will be migrated to Cloudflare Secrets Store)
DATABASE_PASSWORD=
JWT_SECRET_KEY=
API_KEY=

# Other Application Settings
APP_NAME=FastAPI Core Framework
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# Database Configuration (non-secret parts)
DATABASE_HOST=
DATABASE_PORT=
DATABASE_NAME=
DATABASE_USER=
# DATABASE_PASSWORD will be stored in Cloudflare Secrets Store

# Database Configuration (Supabase)
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
SUPABASE_DB_URI=

# Redis Configuration - Redis Cloud instance
REDIS_URL=
REDIS_HOST=
REDIS_PORT=
REDIS_DB=
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_API_KEY=

# Monitoring and Logging
LOG_LEVEL=INFO
ENABLE_METRICS=true
METRICS_PORT=

# CORS Settings
CORS_ORIGINS=
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Security Settings
ENABLE_HTTPS_REDIRECT=true
ENABLE_SECURITY_HEADERS=true

#example of .env