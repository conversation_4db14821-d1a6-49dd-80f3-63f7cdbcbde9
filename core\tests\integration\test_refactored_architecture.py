"""
Integration Tests for Refactored Architecture

This module tests the refactored core system components:
- Modular infrastructure organization
- Factory functions for cache and registry
- Improved error handling and logging
- Backward compatibility
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, patch
from typing import Optional

from core.main import create_app, run_server
from core.config.settings import create_settings
from core.application.factory import ApplicationFactory


class TestRefactoredArchitecture:
    """Test suite for refactored architecture components"""

    def test_main_create_app_success(self):
        """Test successful application creation"""
        # Test with default settings
        app = create_app()
        assert app is not None
        assert app.title is not None
        assert hasattr(app, 'state')

    def test_main_create_app_with_custom_settings(self):
        """Test application creation with custom settings"""
        settings = create_settings()
        app = create_app(settings)
        assert app is not None
        assert app.title == settings.APP_NAME

    def test_main_create_app_error_handling(self):
        """Test error handling in application creation"""
        with patch('core.application.factory.create_application') as mock_create:
            mock_create.side_effect = Exception("Test error")
            
            with pytest.raises(RuntimeError, match="Application creation failed"):
                create_app()

    @pytest.mark.asyncio
    async def test_cache_factory_memory(self):
        """Test cache factory with memory cache"""
        try:
            from core.infrastructure.cache import create_cache
            
            cache = create_cache(cache_type="memory", max_size=100, default_ttl=60)
            assert cache is not None
            
            # Test basic cache operations
            await cache.set("test_key", "test_value", ttl=30)
            value = await cache.get("test_key")
            assert value == "test_value"
            
            await cache.delete("test_key")
            value = await cache.get("test_key")
            assert value is None
            
        except ImportError:
            pytest.skip("Cache module not available")

    @pytest.mark.asyncio
    async def test_cache_factory_redis_fallback(self):
        """Test cache factory with Redis fallback to memory"""
        try:
            from core.infrastructure.cache import create_cache
            
            # Test with invalid Redis URL - should fallback gracefully
            with pytest.raises((ValueError, RuntimeError)):
                create_cache(cache_type="redis", redis_url=None)
                
        except ImportError:
            pytest.skip("Cache module not available")

    def test_service_registry_factory_memory(self):
        """Test service registry factory with memory registry"""
        try:
            from core.infrastructure.registry import create_service_registry
            
            registry = create_service_registry(registry_type="memory")
            assert registry is not None
            
        except ImportError:
            pytest.skip("Registry module not available")

    def test_service_registry_factory_redis_validation(self):
        """Test service registry factory Redis validation"""
        try:
            from core.infrastructure.registry import create_service_registry
            
            # Test with missing Redis URL
            with pytest.raises((ValueError, RuntimeError)):
                create_service_registry(registry_type="redis", redis_url=None)
                
        except ImportError:
            pytest.skip("Registry module not available")

    def test_application_factory_cache_configuration(self):
        """Test application factory cache configuration"""
        settings = create_settings()
        factory = ApplicationFactory(settings)
        
        # Test that factory initializes without errors
        assert factory.settings == settings
        assert factory.container is not None
        assert factory.plugin_manager is not None

    def test_application_factory_modular_fallback(self):
        """Test application factory fallback to legacy implementations"""
        settings = create_settings()
        factory = ApplicationFactory(settings)
        
        # Mock the modular imports to fail
        with patch('core.infrastructure.cache.create_cache') as mock_create_cache:
            mock_create_cache.side_effect = ImportError("Module not found")
            
            # Should not raise an error, should fallback to legacy
            try:
                factory._configure_services()
            except Exception as e:
                # Some errors are expected due to missing dependencies in test environment
                assert "not available" in str(e) or "could not be resolved" in str(e)

    def test_structured_logging_configuration(self):
        """Test that structured logging is properly configured"""
        # Check that logger is configured
        logger = logging.getLogger('core.main')
        assert logger is not None
        
        # Check that handlers are configured
        root_logger = logging.getLogger()
        assert len(root_logger.handlers) > 0

    @pytest.mark.asyncio
    async def test_infrastructure_module_imports(self):
        """Test that infrastructure modules can be imported"""
        # Test cache module
        try:
            from core.infrastructure.cache import ICache, create_cache
            assert ICache is not None
            assert create_cache is not None
        except ImportError:
            pytest.skip("Cache module not fully available")
        
        # Test registry module
        try:
            from core.infrastructure.registry import IServiceRegistry, create_service_registry
            assert IServiceRegistry is not None
            assert create_service_registry is not None
        except ImportError:
            pytest.skip("Registry module not fully available")

    def test_backward_compatibility_imports(self):
        """Test that backward compatibility imports still work"""
        # Test legacy cache imports
        try:
            from core.infrastructure.cache import RedisCache, MemoryCache
            # These should be available even if None
            assert RedisCache is not None or RedisCache is None
            assert MemoryCache is not None or MemoryCache is None
        except ImportError:
            pytest.skip("Legacy cache imports not available")
        
        # Test legacy registry imports
        try:
            from core.infrastructure.registry import ServiceRegistryImpl
            assert ServiceRegistryImpl is not None or ServiceRegistryImpl is None
        except ImportError:
            pytest.skip("Legacy registry imports not available")

    def test_error_handling_patterns(self):
        """Test consistent error handling patterns"""
        # Test cache factory error handling
        try:
            from core.infrastructure.cache import create_cache
            
            with pytest.raises(ValueError):
                create_cache(cache_type="invalid_type")
                
        except ImportError:
            pytest.skip("Cache module not available")
        
        # Test registry factory error handling
        try:
            from core.infrastructure.registry import create_service_registry
            
            with pytest.raises(ValueError):
                create_service_registry(registry_type="invalid_type")
                
        except ImportError:
            pytest.skip("Registry module not available")

    def test_supabase_integration_structure(self):
        """Test Supabase integration structure"""
        try:
            from core.infrastructure.supabase import UnifiedSupabaseClient
            assert UnifiedSupabaseClient is not None
        except ImportError:
            pytest.skip("Supabase module not available")

    @pytest.mark.asyncio
    async def test_production_readiness_features(self):
        """Test production readiness features"""
        settings = create_settings()
        
        # Test that Redis Cloud limits are properly configured
        if settings.cache.redis_url:
            try:
                from core.infrastructure.cache import create_cache
                
                cache = create_cache(
                    cache_type="redis",
                    redis_url=str(settings.cache.redis_url),
                    max_connections=25,  # Redis Cloud free tier limit
                    max_ops_per_second=80,  # Conservative limit
                    max_memory_mb=28  # Conservative limit
                )
                assert cache is not None
                
            except (ImportError, ValueError, RuntimeError):
                # Expected in test environment without Redis
                pass

    def test_type_safety_and_annotations(self):
        """Test that type annotations are properly used"""
        from core.main import create_app
        import inspect
        
        # Check that main functions have proper type annotations
        sig = inspect.signature(create_app)
        assert 'settings' in sig.parameters
        assert sig.return_annotation is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
