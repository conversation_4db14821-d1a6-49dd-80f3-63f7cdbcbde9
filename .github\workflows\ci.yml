﻿name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r core/requirements.txt
        pip install pytest flake8 black
    
    - name: Run tests
      run: pytest
    
    - name: Run linting
      run: |
        flake8 .
        black --check .
