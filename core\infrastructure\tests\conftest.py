"""
Infrastructure test configuration
"""
import pytest
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Import parent conftest
from core.tests.conftest import *


@pytest.fixture
def mock_httpx_client():
    """Mock httpx client for testing"""
    client = MagicMock()
    client.get = AsyncMock()
    client.post = AsyncMock()
    client.put = AsyncMock()
    client.delete = AsyncMock()
    client.aclose = AsyncMock()
    return client
