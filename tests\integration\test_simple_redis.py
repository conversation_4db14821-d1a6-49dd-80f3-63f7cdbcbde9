#!/usr/bin/env python3
"""
Simple Redis Integration Tests

This module contains basic integration tests for Redis connectivity.
"""

import asyncio
import os
import pytest


@pytest.mark.integration
@pytest.mark.asyncio
async def test_simple_redis_connection():
    """Test basic Redis connection"""
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        pytest.skip("No REDIS_URL found in environment")
    
    try:
        import redis.asyncio as aioredis
        
        redis_client = await aioredis.from_url(redis_url)
        
        # Test ping
        pong = await redis_client.ping()
        assert pong is True
        
        # Test basic operations
        await redis_client.set("test_key", "test_value", ex=10)
        value = await redis_client.get("test_key")
        assert value == "test_value"
        
        # Cleanup
        await redis_client.delete("test_key")
        await redis_client.close()
        
    except Exception as e:
        pytest.fail(f"Redis connection test failed: {e}")


@pytest.mark.integration
@pytest.mark.asyncio
async def test_redis_operations():
    """Test various Redis operations"""
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        pytest.skip("No REDIS_URL found in environment")
    
    try:
        import redis.asyncio as aioredis
        
        redis_client = await aioredis.from_url(redis_url)
        
        # Test string operations
        await redis_client.set("string_key", "string_value")
        value = await redis_client.get("string_key")
        assert value == "string_value"
        
        # Test expiration
        await redis_client.setex("expire_key", 1, "expire_value")
        value = await redis_client.get("expire_key")
        assert value == "expire_value"
        
        # Wait for expiration
        await asyncio.sleep(2)
        value = await redis_client.get("expire_key")
        assert value is None
        
        # Test exists
        await redis_client.set("exists_key", "exists_value")
        exists = await redis_client.exists("exists_key")
        assert exists == 1
        
        exists = await redis_client.exists("nonexistent_key")
        assert exists == 0
        
        # Cleanup
        await redis_client.delete("string_key", "exists_key")
        await redis_client.close()
        
    except Exception as e:
        pytest.fail(f"Redis operations test failed: {e}")


def main():
    """Main function for manual testing"""
    async def run_tests():
        print("🚀 Simple Redis Integration Tests")
        print("=" * 40)
        
        try:
            await test_simple_redis_connection()
            print("✅ Simple Redis connection test passed")
            
            await test_redis_operations()
            print("✅ Redis operations test passed")
            
            print("\n🎉 All simple Redis tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Tests failed: {e}")
            return False
    
    return asyncio.run(run_tests())


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
