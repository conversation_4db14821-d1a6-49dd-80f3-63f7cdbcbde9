# FastAPI Advanced Core Framework v2.0

## Overview

The FastAPI Advanced Core Framework is a modular, enterprise-grade framework built on top of FastAPI with comprehensive features for building scalable microservices. This version (2.0) introduces a completely redesigned modular architecture with dependency injection, plugin system, and clean architecture principles.

## 🏗️ Architecture

The framework follows a **Clean Architecture** pattern with clear separation of concerns:

```
core/
├── domain/                 # Domain Layer (Business Logic & Interfaces)
│   ├── interfaces.py      # Core business interfaces and protocols
│   ├── exceptions.py      # Domain-specific exceptions
│   └── __init__.py
├── infrastructure/         # Infrastructure Layer (External Concerns)
│   ├── container.py       # Dependency Injection Container
│   ├── cache.py          # Cache implementations (Redis, Memory)
│   ├── metrics.py        # Metrics collection (Prometheus)
│   ├── security.py       # Security implementations (JWT, etc.)
│   └── __init__.py
├── application/           # Application Layer (Use Cases & Orchestration)
│   ├── factory.py        # Application Factory
│   ├── plugins.py        # Plugin Management System
│   ├── services.py       # Application Services
│   └── __init__.py
├── presentation/          # Presentation Layer (API & Web Interface)
│   ├── api.py           # API Routes and Controllers
│   ├── middleware.py    # HTTP Middleware
│   ├── exceptions.py    # HTTP Exception Handlers
│   └── __init__.py
├── config/               # Configuration Layer
│   ├── settings.py      # Application Settings
│   ├── providers.py     # Configuration Providers
│   └── __init__.py
├── main.py              # Application Entry Point
└── config.py           # Backward Compatibility
```

## 🔧 Key Features

### 1. **Dependency Injection Container**
- Full async support with singleton, transient, and scoped lifetimes
- Constructor injection with type hints
- Factory pattern support
- Automatic dependency resolution

```python
from core.infrastructure.container import Container
from core.domain.interfaces import ICache

# Register services
container = Container()
container.register_singleton(ICache, RedisCache)

# Resolve services
cache = await container.get_service_async(ICache)
```

### 2. **Plugin System**
- Dynamic plugin loading and unloading
- Dependency resolution between plugins
- Plugin lifecycle management
- Service registration integration

```python
from core.application.plugins import BasePlugin

class MyPlugin(BasePlugin):
    @property
    def name(self) -> str:
        return "MyPlugin"
    
    async def on_load(self) -> None:
        print("Plugin loaded!")
    
    def register_services(self, container: Container) -> None:
        container.register_singleton(IMyService, MyService)
```

### 3. **Advanced Configuration**
- Hierarchical configuration with validation
- Environment-specific settings
- Secret management
- Hot reload support

```python
from core.config.settings import Settings, create_settings

settings = create_settings()
print(settings.app.app_name)  # Access nested configuration
print(settings.get_database_url())  # Helper methods
```

### 4. **Clean Architecture Interfaces**
- Protocol-based interfaces for loose coupling
- Runtime type checking with `@runtime_checkable`
- Clear contracts between layers

```python
from core.domain.interfaces import IService, IHealthCheck

class MyService(IService):
    @property
    def name(self) -> str:
        return "MyService"
    
    async def start(self) -> None:
        # Service startup logic
        pass
```

## 🚀 Getting Started

### 1. **Basic Usage**

```python
from core import ApplicationFactory, create_settings

# Create application
settings = create_settings()
factory = ApplicationFactory(settings)
app = factory.create_app()

# Run with uvicorn
import uvicorn
uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2. **Using Dependency Injection**

```python
from core.infrastructure.container import get_container
from core.domain.interfaces import ICache

# Get global container
container = get_container()

# Register custom services
container.register_singleton(IMyService, MyServiceImpl)

# Use in FastAPI dependencies
from fastapi import Depends

async def get_my_service(
    container: Container = Depends(get_container)
) -> IMyService:
    return await container.get_service_async(IMyService)

@router.get("/data")
async def get_data(service: IMyService = Depends(get_my_service)):
    return await service.get_data()
```

### 3. **Creating Plugins**

```python
# plugins/my_plugin.py
from core.application.plugins import BasePlugin
from core.infrastructure.container import Container

class DatabasePlugin(BasePlugin):
    @property
    def name(self) -> str:
        return "DatabasePlugin"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    async def on_load(self) -> None:
        print("Database plugin loaded")
    
    def register_services(self, container: Container) -> None:
        from core.domain.interfaces import IRepository
        container.register_singleton(IRepository, DatabaseRepository)
```

## 📦 Core Components

### Domain Layer
- **Interfaces**: Core business contracts and protocols
- **Exceptions**: Domain-specific error handling
- **Entities**: Business objects and value objects

### Infrastructure Layer
- **Container**: Dependency injection and service location
- **Cache**: Redis and in-memory caching implementations
- **Metrics**: Prometheus metrics collection
- **Security**: JWT authentication and authorization

### Application Layer
- **Factory**: Application bootstrap and configuration
- **Plugins**: Dynamic module loading and management
- **Services**: Business logic orchestration

### Presentation Layer
- **API**: RESTful API endpoints and controllers
- **Middleware**: HTTP middleware for cross-cutting concerns
- **Exceptions**: HTTP error handling and responses

## 🔗 Integration Examples

### Service Integration
```python
from core.domain.interfaces import IService, ServiceStatus

class DatabaseService(IService):
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self._status = ServiceStatus.UNKNOWN
    
    async def start(self) -> None:
        # Initialize database connection
        self._status = ServiceStatus.HEALTHY
    
    async def stop(self) -> None:
        # Close database connection
        self._status = ServiceStatus.UNKNOWN
    
    async def health_check(self) -> Dict[str, Any]:
        return {
            "status": self._status.value,
            "connection": self.connection_string,
            "timestamp": datetime.utcnow().isoformat()
        }
```

### Custom Middleware
```python
from core.presentation.middleware import BaseMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

class CustomAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        # Custom authentication logic
        response = await call_next(request)
        return response
```

## 🔧 Configuration

### Environment Variables
```bash
# Application
APP__APP_NAME="My Application"
APP__APP_VERSION="1.0.0"
APP__ENVIRONMENT="production"

# Database
DATABASE__DATABASE_URL="postgresql://user:pass@localhost/db"
DATABASE__POOL_SIZE=20

# Cache
CACHE__REDIS_URL="redis://localhost:6379/0"
CACHE__DEFAULT_CACHE_TTL=3600

# Security
SECURITY__SECRET_KEY="your-secret-key"
SECURITY__JWT_ALGORITHM="HS256"
```

### Configuration File (YAML)
```yaml
app:
  app_name: "My Application"
  environment: "production"

database:
  database_url: "postgresql://user:pass@localhost/db"
  pool_size: 20

security:
  cors_origins:
    - "https://example.com"
    - "https://app.example.com"
```

## 🧪 Testing

The modular architecture makes testing straightforward with dependency injection:

```python
import pytest
from core.infrastructure.container import Container
from core.domain.interfaces import ICache

@pytest.fixture
def container():
    container = Container()
    # Register test implementations
    container.register_singleton(ICache, MockCache)
    return container

async def test_service(container):
    service = await container.get_service_async(IMyService)
    result = await service.do_something()
    assert result is not None
```

## 📚 Migration from v1.x

### Key Changes
1. **Configuration**: Moved from flat `settings` to hierarchical `settings.app.*`
2. **Services**: Now use dependency injection instead of global instances
3. **Plugins**: New plugin system replaces old service registry
4. **Architecture**: Clean architecture with clear layer separation

### Migration Steps
1. Update configuration access patterns
2. Convert services to use dependency injection
3. Implement plugin interfaces for extensions
4. Update import statements to new module structure

## 🤝 Contributing

1. Follow the clean architecture principles
2. Implement proper interfaces in the domain layer
3. Use dependency injection for service dependencies
4. Add comprehensive tests for new features
5. Update documentation for API changes

## 📄 License

This framework is licensed under the MIT License. See LICENSE file for details.

---

**FastAPI Advanced Core Framework v2.0** - Building the future of Python microservices. 🚀 