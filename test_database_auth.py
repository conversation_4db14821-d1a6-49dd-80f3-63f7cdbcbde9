#!/usr/bin/env python3
"""
Test script for database authentication system

This script tests the new DatabaseSecurityProvider with real database operations.
"""

import asyncio
import os
from core.infrastructure.database import DatabaseInitializer
from core.infrastructure.security import DatabaseSecurityProvider
from core.infrastructure.user_store import DatabaseUserStore, DatabaseRoleStore


async def test_database_authentication():
    """Test the complete database authentication flow"""

    # Use Supabase PostgreSQL for testing
    # You'll need to set your Supabase credentials in environment variables
    import os

    # Supabase connection details for jinda_789-database-lanswap project
    supabase_host = "db.copabcxpvtiltgxuuemn.supabase.co"
    supabase_db = "postgres"
    supabase_user = os.getenv("SUPABASE_DB_USER", "postgres")
    supabase_password = os.getenv("SUPABASE_DB_PASSWORD")

    if not supabase_password:
        print("❌ Please set SUPABASE_DB_PASSWORD environment variable")
        print("   You can find this in your Supabase project settings > Database")
        return

    database_url = f"postgresql+asyncpg://{supabase_user}:{supabase_password}@{supabase_host}:5432/{supabase_db}"

    print("🧪 Testing Database Authentication System")
    print("=" * 50)

    # Initialize database
    print("\n1. Initializing database...")
    initializer = DatabaseInitializer(database_url)
    await initializer.initialize_database(drop_existing=True)

    # Seed default data
    print("\n2. Seeding default data...")
    seed_result = await initializer.seed_default_data(admin_password="admin123")

    # Create security provider
    print("\n3. Creating security provider...")
    user_store = DatabaseUserStore(database_url)
    role_store = DatabaseRoleStore(database_url)
    security = DatabaseSecurityProvider(
        user_store=user_store,
        role_store=role_store,
        secret_key="test-secret-key-12345",
        algorithm="HS256",
        access_token_expire_minutes=30
    )

    # Test authentication
    print("\n4. Testing authentication...")

    # Test admin login
    print("   Testing admin login...")
    admin_result = await security.authenticate({
        "username": "admin",
        "password": "admin123"
    })

    if admin_result:
        print(f"   ✅ Admin login successful: {admin_result['username']}")
        print(f"      Roles: {admin_result['roles']}")
        print(f"      Permissions: {admin_result['permissions'][:3]}...")  # Show first 3

        # Test JWT token creation and verification
        token = security.create_token(admin_result)
        print(f"   ✅ JWT token created: {token[:50]}...")

        verified_payload = security.verify_token(token)
        if verified_payload:
            print(f"   ✅ JWT token verified: {verified_payload.get('username', 'N/A')}")
        else:
            print("   ❌ JWT token verification failed")
    else:
        print("   ❌ Admin login failed")

    # Test regular user login
    print("\n   Testing regular user login...")
    user_result = await security.authenticate({
        "username": "testuser",
        "password": "testpass123"
    })

    if user_result:
        print(f"   ✅ User login successful: {user_result['username']}")
        print(f"      Roles: {user_result['roles']}")
        print(f"      Permissions: {user_result['permissions']}")
    else:
        print("   ❌ User login failed")

    # Test authorization
    print("\n5. Testing authorization...")
    if admin_result:
        # Test admin permissions
        can_delete_users = await security.authorize(admin_result, "users", "delete")
        can_read_services = await security.authorize(admin_result, "services", "read")
        print(f"   Admin can delete users: {'✅' if can_delete_users else '❌'}")
        print(f"   Admin can read services: {'✅' if can_read_services else '❌'}")

    if user_result:
        # Test user permissions
        can_delete_users = await security.authorize(user_result, "users", "delete")
        can_read_services = await security.authorize(user_result, "services", "read")
        print(f"   User can delete users: {'❌' if not can_delete_users else '✅'}")
        print(f"   User can read services: {'✅' if can_read_services else '❌'}")

    # Test invalid login
    print("\n6. Testing invalid credentials...")
    invalid_result = await security.authenticate({
        "username": "admin",
        "password": "wrongpassword"
    })
    print(f"   Invalid login result: {'❌ Correctly rejected' if not invalid_result else '✅ Incorrectly accepted'}")

    # Test user creation
    print("\n7. Testing user creation...")
    try:
        new_user = await security.create_user({
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpass123",
            "first_name": "New",
            "last_name": "User"
        })
        print(f"   ✅ New user created: {new_user.username}")

        # Test login with new user
        new_user_result = await security.authenticate({
            "username": "newuser",
            "password": "newpass123"
        })
        print(f"   ✅ New user login: {'Success' if new_user_result else 'Failed'}")

    except Exception as e:
        print(f"   ❌ User creation failed: {e}")

    # Verify database status
    print("\n8. Final database status...")
    status = await initializer.verify_setup()
    print(f"   Total users: {status['total_users']}")
    print(f"   Total roles: {status['total_roles']}")
    print(f"   Admin exists: {'✅' if status['admin_user_exists'] else '❌'}")

    print("\n🎉 Database authentication test completed!")
    print("\nTo use this system:")
    print("1. Set SUPABASE_DB_PASSWORD in your environment")
    print("2. Run the database initializer")
    print("3. Use DatabaseSecurityProvider in your application")
    print("\n📝 Note: Test tables remain in Supabase for inspection")
    print("   You can view them in your Supabase dashboard or drop them manually if needed")


if __name__ == "__main__":
    asyncio.run(test_database_authentication())
