import pytest

from core.infrastructure.security import JWTSecurityProvider
from core.domain.exceptions import AuthenticationError


@pytest.fixture
def security():
    return JWTSecurityProvider(secret_key="test-secret-key-for-testing")


@pytest.mark.asyncio
async def test_authenticate_valid_credentials(security):
    """Test authentication with valid credentials"""
    credentials = {"username": "admin", "password": "admin"}

    result = await security.authenticate(credentials)

    assert result is not None
    assert result["user_id"] == "admin"
    assert result["username"] == "admin"
    assert "admin" in result["roles"]
    assert "*" in result["permissions"]


@pytest.mark.asyncio
async def test_authenticate_invalid_credentials(security):
    """Test authentication with invalid credentials"""
    credentials = {"username": "admin", "password": "wrong"}

    result = await security.authenticate(credentials)

    assert result is None


@pytest.mark.asyncio
async def test_authenticate_missing_credentials(security):
    """Test authentication with missing credentials"""
    with pytest.raises(AuthenticationError) as excinfo:
        await security.authenticate({"username": "admin"})

    assert "Username and password required" in str(excinfo.value)


@pytest.mark.asyncio
async def test_authorize_admin_user(security):
    """Test authorization for admin user"""
    user = {"roles": ["admin"], "permissions": ["*"]}

    result = await security.authorize(user, "any_resource", "any_action")

    assert result is True


@pytest.mark.asyncio
async def test_authorize_wildcard_permission(security):
    """Test authorization with wildcard permission"""
    user = {"roles": [], "permissions": ["*"]}

    result = await security.authorize(user, "any_resource", "any_action")

    assert result is True


@pytest.mark.asyncio
async def test_authorize_specific_permission(security):
    """Test authorization with specific permission"""
    user = {"roles": [], "permissions": ["users:read"]}

    result = await security.authorize(user, "users", "read")

    assert result is True


@pytest.mark.asyncio
async def test_authorize_no_permission(security):
    """Test authorization without required permission"""
    user = {"roles": [], "permissions": ["users:read"]}

    result = await security.authorize(user, "users", "write")

    assert result is False


def test_password_hashing(security):
    """Test password hashing and verification"""
    password = "test_password_123"

    # Hash the password
    hashed = security.hash_password(password)

    # Check hash is different from original
    assert hashed != password
    assert len(hashed) > 20  # Should be a proper hash

    # Verify correct password
    assert security.verify_password(password, hashed) is True

    # Verify incorrect password
    assert security.verify_password("wrong_password", hashed) is False


def test_create_token(security):
    """Test creating JWT token"""
    # Create a token with test data
    data = {"sub": "user123", "role": "admin"}
    token = security.create_token(data)

    # Check token is a string
    assert isinstance(token, str)
    assert len(token) > 50  # JWT tokens are typically long

    # Verify the token can be decoded
    payload = security.verify_token(token)
    assert payload is not None
    assert payload["sub"] == "user123"
    assert payload["role"] == "admin"
    assert payload["type"] == "access"
    assert "exp" in payload
    assert "iat" in payload


def test_create_refresh_token(security):
    """Test creating refresh JWT token"""
    data = {"sub": "user123"}
    token = security.create_refresh_token(data)

    # Check token is a string
    assert isinstance(token, str)
    assert len(token) > 50

    # Verify the refresh token
    payload = security.verify_refresh_token(token)
    assert payload is not None
    assert payload["sub"] == "user123"
    assert payload["type"] == "refresh"


def test_verify_token_valid(security):
    """Test verifying a valid JWT token"""
    # Create a token first
    data = {"sub": "user123", "permissions": ["read", "write"]}
    token = security.create_token(data)

    # Verify the token
    payload = security.verify_token(token)

    # Check payload
    assert payload is not None
    assert payload["sub"] == "user123"
    assert payload["permissions"] == ["read", "write"]
    assert payload["type"] == "access"


def test_verify_token_invalid(security):
    """Test verifying an invalid JWT token"""
    # Test with completely invalid token
    with pytest.raises(AuthenticationError) as excinfo:
        security.verify_token("invalid.token.here")

    assert "Invalid token" in str(excinfo.value)


def test_verify_token_wrong_type(security):
    """Test verifying token with wrong type"""
    # Create a refresh token and try to verify as access token
    data = {"sub": "user123"}
    refresh_token = security.create_refresh_token(data)

    # Should raise error when verifying refresh token as access token
    with pytest.raises(AuthenticationError) as excinfo:
        security.verify_token(refresh_token)

    assert "Invalid token type" in str(excinfo.value)


def test_verify_refresh_token_wrong_type(security):
    """Test verifying access token as refresh token"""
    # Create an access token and try to verify as refresh token
    data = {"sub": "user123"}
    access_token = security.create_token(data)

    # Should raise error when verifying access token as refresh token
    with pytest.raises(AuthenticationError) as excinfo:
        security.verify_refresh_token(access_token)

    assert "Invalid token type" in str(excinfo.value)


def test_jwt_not_available():
    """Test behavior when JWT library is not available"""
    # Create a security provider and mock JWT unavailability
    security = JWTSecurityProvider(secret_key="test-key")
    security._jwt_available = False

    # Should raise error when trying to create token
    with pytest.raises(AuthenticationError) as excinfo:
        security.create_token({"sub": "test"})

    assert "JWT library not available" in str(excinfo.value)