"""
Configuration Layer

This layer contains:
- Settings and configuration management
- Environment-specific configurations
- Validation and secrets handling
- Configuration providers
"""

from core.config.settings import Settings, create_settings
from core.config.providers import (
    EnvironmentConfigProvider,
    FileConfigProvider,
    VaultSecretProvider,
    AWSSecretsManagerProvider,
)
from core.config.secret_providers import (
    SecretConfigProvider,
    AzureKeyVaultProvider,
    CloudflareKVSecretProvider,
    CloudflareSecretsStoreProvider,
    EncryptedFileSecretProvider,
)

__all__ = [
    "Settings",
    "create_settings",
    "EnvironmentConfigProvider",
    "FileConfigProvider",
    "SecretConfigProvider",
    "VaultSecretProvider",
    "AWSSecretsManagerProvider",
    "AzureKeyVaultProvider",
    "CloudflareKVSecretProvider",
    "CloudflareSecretsStoreProvider",
    "EncryptedFileSecretProvider",
]