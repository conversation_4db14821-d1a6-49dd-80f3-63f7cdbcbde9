"""
Additional Secret Providers

This module contains additional secret management providers including
Azure Key Vault, Cloudflare Secrets Store, and enhanced local development providers.
"""

import os
import json
import logging
import asyncio
from pathlib import Path
from typing import Any, Dict, Optional, List
from datetime import datetime, timedelta

from .providers import BaseSecretProvider, SecretValue, SecretMetadata, VaultSecretProvider


class AzureKeyVaultProvider(BaseSecretProvider):
    """Azure Key Vault secret provider"""

    def __init__(
        self,
        vault_url: str,
        credential=None,
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.vault_url = vault_url
        self.credential = credential

        # Try to import Azure SDK
        try:
            from azure.keyvault.secrets import SecretClient
            from azure.identity import DefaultAzureCredential
            from azure.core.exceptions import ResourceNotFoundError

            self._SecretClient = SecretClient
            self._DefaultAzureCredential = DefaultAzureCredential
            self._ResourceNotFoundError = ResourceNotFoundError
            self._azure_available = True
            self._client = None
        except ImportError:
            self._azure_available = False
            self.logger.warning("Azure SDK not available. Install with: pip install azure-keyvault-secrets azure-identity")

    def _get_client(self):
        """Get or create Azure Key Vault client"""
        if not self._azure_available:
            return None

        if self._client is None:
            try:
                credential = self.credential or self._DefaultAzureCredential()
                self._client = self._SecretClient(vault_url=self.vault_url, credential=credential)
            except Exception as e:
                self.logger.error(f"Failed to create Azure Key Vault client: {e}")
                return None

        return self._client

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from Azure Key Vault"""
        client = self._get_client()
        if not client:
            return None

        try:
            if version:
                secret = client.get_secret(key, version=version)
            else:
                secret = client.get_secret(key)

            secret_metadata = SecretMetadata(
                key=key,
                version=secret.properties.version,
                created_at=secret.properties.created_on,
                expires_at=secret.properties.expires_on,
                tags=secret.properties.tags or {}
            )

            return SecretValue(value=secret.value, metadata=secret_metadata)

        except self._ResourceNotFoundError:
            self.logger.warning(f"Secret {key} not found in Azure Key Vault")
            return None
        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key} from Azure Key Vault: {e}")
            return None

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in Azure Key Vault"""
        client = self._get_client()
        if not client:
            return False

        try:
            kwargs = {'name': key, 'value': value}

            if metadata:
                if metadata.expires_at:
                    kwargs['expires_on'] = metadata.expires_at
                if metadata.tags:
                    kwargs['tags'] = metadata.tags

            client.set_secret(**kwargs)
            return True

        except Exception as e:
            self.logger.error(f"Failed to set secret {key} in Azure Key Vault: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from Azure Key Vault"""
        client = self._get_client()
        if not client:
            return False

        try:
            client.begin_delete_secret(key).wait()
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete secret {key} from Azure Key Vault: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in Azure Key Vault"""
        client = self._get_client()
        if not client:
            return []

        try:
            secrets = []
            for secret_properties in client.list_properties_of_secrets():
                name = secret_properties.name
                if not prefix or name.startswith(prefix):
                    secrets.append(name)

            return secrets

        except Exception as e:
            self.logger.error(f"Failed to list secrets from Azure Key Vault: {e}")
            return []

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret (placeholder - requires custom logic)"""
        self.logger.warning(f"Secret rotation for {key} requires custom implementation")
        return False

    async def health_check(self) -> bool:
        """Check Azure Key Vault health"""
        client = self._get_client()
        if not client:
            return False

        try:
            # Simple health check - try to list secrets with limit
            list(client.list_properties_of_secrets(max_page_size=1))
            return True

        except Exception as e:
            self.logger.error(f"Azure Key Vault health check failed: {e}")
            return False


class CloudflareKVSecretProvider(BaseSecretProvider):
    """Cloudflare Workers KV secret provider (alternative to Secrets Store)"""

    def __init__(
        self,
        account_id: str,
        api_token: str,
        namespace_id: Optional[str] = None,
        namespace_name: str = "secrets",
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.account_id = account_id
        self.api_token = api_token
        self.namespace_id = namespace_id
        self.namespace_name = namespace_name
        self.base_url = "https://api.cloudflare.com/client/v4"

        # Try to import httpx for HTTP requests
        try:
            import httpx
            self._httpx = httpx
            self._http_available = True
        except ImportError:
            self._http_available = False
            self.logger.warning("httpx library not available. Install with: pip install httpx")

    async def _ensure_namespace(self) -> Optional[str]:
        """Ensure KV namespace exists and return its ID"""
        if self.namespace_id:
            return self.namespace_id

        if not self._http_available:
            return None

        try:
            # List existing namespaces
            url = f"{self.base_url}/accounts/{self.account_id}/storage/kv/namespaces"

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers())
                response.raise_for_status()

                data = response.json()
                if data.get('success'):
                    # Look for existing namespace
                    for namespace in data['result']:
                        if namespace['title'] == self.namespace_name:
                            self.namespace_id = namespace['id']
                            self.logger.info(f"Using existing KV namespace: {self.namespace_name}")
                            return self.namespace_id

                    # Create new namespace if not found
                    create_data = {"title": self.namespace_name}
                    response = await client.post(url, headers=self._get_headers(), json=create_data)
                    response.raise_for_status()

                    result = response.json()
                    if result.get('success'):
                        self.namespace_id = result['result']['id']
                        self.logger.info(f"Created new KV namespace: {self.namespace_name}")
                        return self.namespace_id

        except Exception as e:
            self.logger.error(f"Failed to ensure KV namespace: {e}")
            return None

        return None

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Cloudflare API requests"""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from Cloudflare Workers KV"""
        namespace_id = await self._ensure_namespace()
        if not namespace_id or not self._http_available:
            return None

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/storage/kv/namespaces/{namespace_id}/values/{key}"

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers())

                if response.status_code == 404:
                    self.logger.warning(f"Secret {key} not found in Cloudflare KV")
                    return None

                response.raise_for_status()

                # KV returns the raw value, we need to parse metadata separately
                value = response.text

                # Create basic metadata (KV doesn't have rich metadata like Secrets Store)
                metadata = SecretMetadata(
                    key=key,
                    version=None,  # KV doesn't have versions
                    created_at=None,  # KV doesn't track creation time
                    tags={}  # KV metadata is separate, keep simple for now
                )

                return SecretValue(value=value, metadata=metadata)

        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key} from Cloudflare KV: {e}")
            return None

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in Cloudflare Workers KV"""
        namespace_id = await self._ensure_namespace()
        if not namespace_id or not self._http_available:
            return False

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/storage/kv/namespaces/{namespace_id}/values/{key}"

            # Prepare metadata for KV
            kv_metadata = {}
            if metadata and metadata.tags:
                kv_metadata = metadata.tags

            async with self._httpx.AsyncClient() as client:
                # KV expects form data for value and metadata
                data = {"value": value}
                if kv_metadata:
                    data["metadata"] = json.dumps(kv_metadata)

                response = await client.put(url, headers={"Authorization": f"Bearer {self.api_token}"}, data=data)
                response.raise_for_status()

                result = response.json()
                return result.get('success', False)

        except Exception as e:
            self.logger.error(f"Failed to set secret {key} in Cloudflare KV: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from Cloudflare Workers KV"""
        namespace_id = await self._ensure_namespace()
        if not namespace_id or not self._http_available:
            return False

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/storage/kv/namespaces/{namespace_id}/values/{key}"

            async with self._httpx.AsyncClient() as client:
                response = await client.delete(url, headers=self._get_headers())

                if response.status_code == 404:
                    return True  # Already deleted

                response.raise_for_status()
                result = response.json()
                return result.get('success', False)

        except Exception as e:
            self.logger.error(f"Failed to delete secret {key} from Cloudflare KV: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in Cloudflare Workers KV"""
        namespace_id = await self._ensure_namespace()
        if not namespace_id or not self._http_available:
            return []

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/storage/kv/namespaces/{namespace_id}/keys"

            params = {}
            if prefix:
                params["prefix"] = prefix

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers(), params=params)
                response.raise_for_status()

                data = response.json()
                if data.get('success'):
                    return [item['name'] for item in data['result']]
                else:
                    self.logger.error(f"Cloudflare KV API error: {data.get('errors', [])}")
                    return []

        except Exception as e:
            self.logger.error(f"Failed to list secrets from Cloudflare KV: {e}")
            return []

    async def health_check(self) -> bool:
        """Check Cloudflare Workers KV health"""
        if not self._http_available:
            return False

        try:
            # Try to ensure namespace exists (this tests connectivity)
            namespace_id = await self._ensure_namespace()
            return namespace_id is not None

        except Exception as e:
            self.logger.error(f"Cloudflare KV health check failed: {e}")
            return False

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret (placeholder - requires custom logic)"""
        self.logger.warning(f"Secret rotation for {key} requires custom implementation")
        return False


class CloudflareSecretsStoreProvider(BaseSecretProvider):
    """Cloudflare Secrets Store provider (Beta - may not be available)"""

    def __init__(
        self,
        account_id: str,
        api_token: str,
        store_id: Optional[str] = None,
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.account_id = account_id
        self.api_token = api_token
        self.store_id = store_id
        self.base_url = "https://api.cloudflare.com/client/v4"

        # Try to import httpx for HTTP requests
        try:
            import httpx
            self._httpx = httpx
            self._http_available = True
        except ImportError:
            self._http_available = False
            self.logger.warning("httpx library not available. Install with: pip install httpx")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Cloudflare API requests"""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from Cloudflare Secrets Store"""
        if not self._http_available:
            return None

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/secrets-store/secrets/{key}"

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers())

                if response.status_code == 404:
                    self.logger.warning(f"Secret {key} not found in Cloudflare Secrets Store")
                    return None

                response.raise_for_status()
                data = response.json()

                if data.get('success'):
                    result = data['result']
                    secret_metadata = SecretMetadata(
                        key=key,
                        version=result.get('version'),
                        created_at=datetime.fromisoformat(result['created_at'].replace('Z', '+00:00')) if result.get('created_at') else None,
                        tags=result.get('tags', {})
                    )

                    return SecretValue(value=result['value'], metadata=secret_metadata)
                else:
                    self.logger.error(f"Cloudflare API error: {data.get('errors', [])}")
                    return None

        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key} from Cloudflare: {e}")
            return None

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in Cloudflare Secrets Store"""
        if not self._http_available:
            return False

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/secrets-store/secrets"

            payload = {
                "name": key,
                "value": value
            }

            if metadata and metadata.tags:
                payload["tags"] = metadata.tags

            async with self._httpx.AsyncClient() as client:
                response = await client.post(url, headers=self._get_headers(), json=payload)
                response.raise_for_status()

                data = response.json()
                return data.get('success', False)

        except Exception as e:
            self.logger.error(f"Failed to set secret {key} in Cloudflare: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from Cloudflare Secrets Store"""
        if not self._http_available:
            return False

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/secrets-store/secrets/{key}"

            async with self._httpx.AsyncClient() as client:
                response = await client.delete(url, headers=self._get_headers())

                if response.status_code == 404:
                    return True  # Already deleted

                response.raise_for_status()
                data = response.json()
                return data.get('success', False)

        except Exception as e:
            self.logger.error(f"Failed to delete secret {key} from Cloudflare: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in Cloudflare Secrets Store"""
        if not self._http_available:
            return []

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/secrets-store/secrets"

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers())
                response.raise_for_status()

                data = response.json()
                if data.get('success'):
                    secrets = []
                    for secret in data['result']:
                        name = secret['name']
                        if not prefix or name.startswith(prefix):
                            secrets.append(name)
                    return secrets
                else:
                    self.logger.error(f"Cloudflare API error: {data.get('errors', [])}")
                    return []

        except Exception as e:
            self.logger.error(f"Failed to list secrets from Cloudflare: {e}")
            return []

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret (placeholder - requires custom logic)"""
        self.logger.warning(f"Secret rotation for {key} requires custom implementation")
        return False

    async def health_check(self) -> bool:
        """Check Cloudflare Secrets Store health"""
        if not self._http_available:
            return False

        try:
            url = f"{self.base_url}/accounts/{self.account_id}/secrets-store/secrets"

            async with self._httpx.AsyncClient() as client:
                response = await client.get(url, headers=self._get_headers())
                response.raise_for_status()

                data = response.json()
                return data.get('success', False)

        except Exception as e:
            self.logger.error(f"Cloudflare health check failed: {e}")
            return False


class EncryptedFileSecretProvider(BaseSecretProvider):
    """Encrypted file-based secret provider for local development"""

    def __init__(
        self,
        file_path: str = "secrets.enc",
        encryption_key: Optional[str] = None,
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.file_path = Path(file_path)
        self.encryption_key = encryption_key
        self._secrets: Dict[str, Dict[str, Any]] = {}

        # Try to import cryptography
        try:
            from cryptography.fernet import Fernet
            self._Fernet = Fernet
            self._crypto_available = True
        except ImportError:
            self._crypto_available = False
            self.logger.warning("cryptography library not available. Install with: pip install cryptography")

        # Generate or load encryption key
        if self._crypto_available:
            if not self.encryption_key:
                key_file = self.file_path.with_suffix('.key')
                if key_file.exists():
                    with open(key_file, 'rb') as f:
                        self.encryption_key = f.read()
                else:
                    self.encryption_key = self._Fernet.generate_key()
                    with open(key_file, 'wb') as f:
                        f.write(self.encryption_key)
                    self.logger.warning(f"Generated new encryption key: {key_file}")

            self._cipher = self._Fernet(self.encryption_key)

        # Load existing secrets
        self._load_secrets()

        # Warn about development usage
        if not os.getenv('SUPPRESS_DEV_SECRET_WARNING'):
            self.logger.warning(
                "Using encrypted file-based secrets for development. "
                "This is NOT suitable for production. "
                "Set SUPPRESS_DEV_SECRET_WARNING=1 to suppress this warning."
            )

    def _load_secrets(self) -> None:
        """Load secrets from encrypted file"""
        if not self.file_path.exists() or not self._crypto_available:
            return

        try:
            with open(self.file_path, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = self._cipher.decrypt(encrypted_data)
            self._secrets = json.loads(decrypted_data.decode('utf-8'))

        except Exception as e:
            self.logger.error(f"Failed to load secrets from {self.file_path}: {e}")
            self._secrets = {}

    def _save_secrets(self) -> None:
        """Save secrets to encrypted file"""
        if not self._crypto_available:
            return

        try:
            # Create directory if it doesn't exist
            self.file_path.parent.mkdir(parents=True, exist_ok=True)

            # Encrypt and save
            data = json.dumps(self._secrets, indent=2).encode('utf-8')
            encrypted_data = self._cipher.encrypt(data)

            with open(self.file_path, 'wb') as f:
                f.write(encrypted_data)

        except Exception as e:
            self.logger.error(f"Failed to save secrets to {self.file_path}: {e}")

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from encrypted file"""
        secret_data = self._secrets.get(key)
        if not secret_data:
            return None

        # Handle versioning
        if version:
            versions = secret_data.get('versions', {})
            if version not in versions:
                return None
            value = versions[version]['value']
            created_at = datetime.fromisoformat(versions[version]['created_at'])
        else:
            value = secret_data.get('value')
            created_at = datetime.fromisoformat(secret_data.get('created_at', datetime.utcnow().isoformat()))

        if value is None:
            return None

        secret_metadata = SecretMetadata(
            key=key,
            version=version or secret_data.get('current_version', '1'),
            created_at=created_at,
            expires_at=datetime.fromisoformat(secret_data['expires_at']) if secret_data.get('expires_at') else None,
            tags=secret_data.get('tags', {})
        )

        return SecretValue(value=value, metadata=secret_metadata)

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in encrypted file"""
        try:
            now = datetime.utcnow()

            # Create or update secret
            if key not in self._secrets:
                self._secrets[key] = {
                    'value': value,
                    'created_at': now.isoformat(),
                    'current_version': '1',
                    'versions': {},
                    'tags': {}
                }
            else:
                # Archive current version
                current_version = self._secrets[key].get('current_version', '1')
                self._secrets[key]['versions'][current_version] = {
                    'value': self._secrets[key]['value'],
                    'created_at': self._secrets[key]['created_at']
                }

                # Update to new version
                new_version = str(int(current_version) + 1)
                self._secrets[key].update({
                    'value': value,
                    'created_at': now.isoformat(),
                    'current_version': new_version
                })

            # Clear cache for this key to force reload
            cache_key = key
            if hasattr(self, '_cache') and cache_key in self._cache:
                del self._cache[cache_key]
            if hasattr(self, '_cache_timestamps') and cache_key in self._cache_timestamps:
                del self._cache_timestamps[cache_key]

            # Update metadata
            if metadata:
                if metadata.expires_at:
                    self._secrets[key]['expires_at'] = metadata.expires_at.isoformat()
                if metadata.tags:
                    self._secrets[key]['tags'].update(metadata.tags)

            self._save_secrets()
            return True

        except Exception as e:
            self.logger.error(f"Failed to set secret {key}: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from encrypted file"""
        try:
            if key in self._secrets:
                del self._secrets[key]
                self._save_secrets()
                # Clear from cache as well
                if hasattr(self, '_cache') and key in self._cache:
                    del self._cache[key]
                if hasattr(self, '_cache_timestamps') and key in self._cache_timestamps:
                    del self._cache_timestamps[key]
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete secret {key}: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in encrypted file"""
        secrets = list(self._secrets.keys())
        if prefix:
            secrets = [s for s in secrets if s.startswith(prefix)]
        return secrets

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret (placeholder - requires custom logic)"""
        self.logger.warning(f"Secret rotation for {key} requires custom implementation")
        return False

    async def health_check(self) -> bool:
        """Check encrypted file provider health"""
        return self._crypto_available and (self.file_path.exists() or self.file_path.parent.exists())


class SecretConfigProvider:
    """Enhanced secret configuration provider that can use multiple backends"""

    def __init__(
        self,
        provider_type: str = "auto",
        fallback_to_env: bool = True,
        **provider_kwargs
    ):
        self.provider_type = provider_type
        self.fallback_to_env = fallback_to_env
        self.provider_kwargs = provider_kwargs
        self.logger = logging.getLogger(self.__class__.__name__)
        self._provider: Optional[BaseSecretProvider] = None
        self._env_provider = None

        if self.fallback_to_env:
            from .providers import EnvironmentConfigProvider
            self._env_provider = EnvironmentConfigProvider()

        self._initialize_provider()

    def _initialize_provider(self) -> None:
        """Initialize the secret provider based on configuration"""
        try:
            if self.provider_type == "auto":
                self._provider = self._auto_detect_provider()
            elif self.provider_type == "vault":
                self._provider = VaultSecretProvider(**self.provider_kwargs)
            elif self.provider_type == "aws":
                from .providers import AWSSecretsManagerProvider
                self._provider = AWSSecretsManagerProvider(**self.provider_kwargs)
            elif self.provider_type == "azure":
                self._provider = AzureKeyVaultProvider(**self.provider_kwargs)
            elif self.provider_type == "cloudflare":
                self._provider = CloudflareSecretsStoreProvider(**self.provider_kwargs)
            elif self.provider_type == "file":
                self._provider = EncryptedFileSecretProvider(**self.provider_kwargs)
            else:
                self.logger.error(f"Unknown provider type: {self.provider_type}")
                self._provider = None

        except Exception as e:
            self.logger.error(f"Failed to initialize secret provider {self.provider_type}: {e}")
            self._provider = None

    def _auto_detect_provider(self) -> Optional[BaseSecretProvider]:
        """Auto-detect the best available secret provider"""
        # Check for environment variables that indicate specific providers

        # Cloudflare Workers KV (prioritized as default - more widely available)
        if os.getenv('CLOUDFLARE_ACCOUNT_ID') and os.getenv('CLOUDFLARE_API_TOKEN'):
            try:
                self.logger.info("Using Cloudflare Workers KV as secret provider")
                return CloudflareKVSecretProvider(
                    account_id=os.getenv('CLOUDFLARE_ACCOUNT_ID'),
                    api_token=os.getenv('CLOUDFLARE_API_TOKEN'),
                    namespace_name=os.getenv('CLOUDFLARE_KV_NAMESPACE', 'secrets')
                )
            except Exception as e:
                self.logger.warning(f"Failed to initialize Cloudflare KV provider: {e}")

                # Fallback to Secrets Store if KV fails
                try:
                    self.logger.info("Falling back to Cloudflare Secrets Store")
                    return CloudflareSecretsStoreProvider(
                        account_id=os.getenv('CLOUDFLARE_ACCOUNT_ID'),
                        api_token=os.getenv('CLOUDFLARE_API_TOKEN'),
                        store_id=os.getenv('CLOUDFLARE_STORE_ID')
                    )
                except Exception as e2:
                    self.logger.warning(f"Failed to initialize Cloudflare Secrets Store: {e2}")

        # HashiCorp Vault
        if os.getenv('VAULT_ADDR') and (os.getenv('VAULT_TOKEN') or
                                       (os.getenv('VAULT_ROLE_ID') and os.getenv('VAULT_SECRET_ID'))):
            try:
                self.logger.info("Using HashiCorp Vault as secret provider")
                return VaultSecretProvider(
                    vault_url=os.getenv('VAULT_ADDR'),
                    vault_token=os.getenv('VAULT_TOKEN'),
                    vault_role_id=os.getenv('VAULT_ROLE_ID'),
                    vault_secret_id=os.getenv('VAULT_SECRET_ID'),
                    mount_point=os.getenv('VAULT_MOUNT_POINT', 'secret'),
                    kv_version=int(os.getenv('VAULT_KV_VERSION', '2'))
                )
            except Exception as e:
                self.logger.warning(f"Failed to initialize Vault provider: {e}")

        # AWS Secrets Manager
        if os.getenv('AWS_REGION') and (os.getenv('AWS_ACCESS_KEY_ID') or
                                       os.getenv('AWS_PROFILE') or
                                       os.getenv('AWS_ROLE_ARN')):
            try:
                self.logger.info("Using AWS Secrets Manager as secret provider")
                from .providers import AWSSecretsManagerProvider
                return AWSSecretsManagerProvider(
                    region_name=os.getenv('AWS_REGION', 'us-east-1'),
                    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                    aws_session_token=os.getenv('AWS_SESSION_TOKEN')
                )
            except Exception as e:
                self.logger.warning(f"Failed to initialize AWS provider: {e}")

        # Azure Key Vault
        if os.getenv('AZURE_KEY_VAULT_URL'):
            try:
                self.logger.info("Using Azure Key Vault as secret provider")
                return AzureKeyVaultProvider(
                    vault_url=os.getenv('AZURE_KEY_VAULT_URL')
                )
            except Exception as e:
                self.logger.warning(f"Failed to initialize Azure provider: {e}")

        # Fallback to encrypted file for development
        self.logger.warning("No external secret provider configured, using encrypted file for development")
        self.logger.info("💡 Tip: Set CLOUDFLARE_ACCOUNT_ID and CLOUDFLARE_API_TOKEN to use Cloudflare Secrets Store")
        return EncryptedFileSecretProvider()

    async def get_secret(self, key: str, default: Any = None) -> Any:
        """Get a secret value"""
        if self._provider:
            try:
                secret = await self._provider.get_secret(key)
                if secret:
                    return secret.value
            except Exception as e:
                self.logger.error(f"Failed to get secret {key} from provider: {e}")

        # Fallback to environment variables
        if self.fallback_to_env and self._env_provider:
            env_value = self._env_provider.get(key)
            if env_value is not None:
                self.logger.info(f"Using environment variable for secret {key}")
                return env_value

        return default

    async def set_secret(self, key: str, value: str, **metadata) -> bool:
        """Set a secret value"""
        if not self._provider:
            self.logger.error("No secret provider available")
            return False

        try:
            secret_metadata = SecretMetadata(key=key, **metadata) if metadata else None
            return await self._provider.set_secret(key, value, secret_metadata)
        except Exception as e:
            self.logger.error(f"Failed to set secret {key}: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret"""
        if not self._provider:
            self.logger.error("No secret provider available")
            return False

        try:
            return await self._provider.delete_secret(key)
        except Exception as e:
            self.logger.error(f"Failed to delete secret {key}: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List available secrets"""
        if not self._provider:
            return []

        try:
            return await self._provider.list_secrets(prefix)
        except Exception as e:
            self.logger.error(f"Failed to list secrets: {e}")
            return []

    async def health_check(self) -> bool:
        """Check if the secret provider is healthy"""
        if not self._provider:
            return False

        try:
            return await self._provider.health_check()
        except Exception as e:
            self.logger.error(f"Secret provider health check failed: {e}")
            return False

    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current provider"""
        # Try to get health status without blocking
        healthy = False
        if self._provider:
            try:
                # Try to get the current event loop
                asyncio.get_running_loop()
                # If we're in an event loop, we can't use asyncio.run()
                # Just return basic info without health check
                healthy = "unknown (call health_check() separately)"
            except RuntimeError:
                # No running event loop, safe to use asyncio.run()
                try:
                    healthy = asyncio.run(self.health_check())
                except Exception:
                    healthy = False

        return {
            "provider_type": self.provider_type,
            "provider_class": self._provider.__class__.__name__ if self._provider else None,
            "fallback_to_env": self.fallback_to_env,
            "healthy": healthy
        }
