"""
Supabase Storage Operations

This module provides Supabase Storage functionality for file management:
- File upload/download
- Bucket management
- Public URL generation
- File metadata and permissions
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone
import mimetypes
import os

from .client import UnifiedSupabaseClient

logger = logging.getLogger(__name__)


class SupabaseStorage:
    """
    Supabase Storage service for file management
    
    Features:
    - File upload/download with progress tracking
    - Bucket management and organization
    - Public/private file access control
    - File metadata and versioning
    - CDN integration for fast delivery
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize storage service"""
        if self._initialized:
            return

        await self.supabase.initialize()
        
        if not self.supabase.storage_available:
            logger.warning("Supabase Storage is not available")
            return

        await self._setup_default_buckets()
        self._initialized = True
        logger.info("Supabase Storage service initialized")

    async def _setup_default_buckets(self) -> None:
        """Setup default storage buckets"""
        default_buckets = [
            {
                "name": "avatars",
                "public": True,
                "file_size_limit": 5 * 1024 * 1024,  # 5MB
                "allowed_mime_types": ["image/jpeg", "image/png", "image/gif", "image/webp"]
            },
            {
                "name": "documents",
                "public": False,
                "file_size_limit": 50 * 1024 * 1024,  # 50MB
                "allowed_mime_types": ["application/pdf", "text/plain", "application/msword"]
            },
            {
                "name": "uploads",
                "public": False,
                "file_size_limit": 100 * 1024 * 1024,  # 100MB
                "allowed_mime_types": None  # Allow all types
            }
        ]

        for bucket_config in default_buckets:
            await self.create_bucket_if_not_exists(bucket_config)

    # Bucket Management
    async def create_bucket_if_not_exists(self, bucket_config: Dict[str, Any]) -> bool:
        """Create bucket if it doesn't exist"""
        try:
            bucket_name = bucket_config["name"]
            
            # Check if bucket exists
            if await self.bucket_exists(bucket_name):
                logger.debug(f"Bucket {bucket_name} already exists")
                return True

            # Create bucket
            success = await self.create_bucket(
                bucket_name,
                public=bucket_config.get("public", False),
                file_size_limit=bucket_config.get("file_size_limit"),
                allowed_mime_types=bucket_config.get("allowed_mime_types")
            )

            if success:
                logger.info(f"Created bucket: {bucket_name}")
            
            return success

        except Exception as e:
            logger.error(f"Failed to create bucket {bucket_config.get('name')}: {e}")
            return False

    async def create_bucket(self, name: str, public: bool = False, 
                           file_size_limit: Optional[int] = None,
                           allowed_mime_types: Optional[List[str]] = None) -> bool:
        """Create new storage bucket"""
        try:
            if not self.supabase._storage_client:
                raise RuntimeError("Storage client not initialized")

            # Create bucket
            response = self.supabase._storage_client.create_bucket(name, {
                "public": public,
                "file_size_limit": file_size_limit,
                "allowed_mime_types": allowed_mime_types
            })

            logger.info(f"Created bucket: {name}")
            return True

        except Exception as e:
            logger.error(f"Failed to create bucket {name}: {e}")
            return False

    async def bucket_exists(self, name: str) -> bool:
        """Check if bucket exists"""
        try:
            if not self.supabase._storage_client:
                return False

            buckets = self.supabase._storage_client.list_buckets()
            return any(bucket.name == name for bucket in buckets)

        except Exception as e:
            logger.error(f"Failed to check bucket existence {name}: {e}")
            return False

    async def list_buckets(self) -> List[Dict[str, Any]]:
        """List all storage buckets"""
        try:
            if not self.supabase._storage_client:
                return []

            buckets = self.supabase._storage_client.list_buckets()
            return [
                {
                    "name": bucket.name,
                    "id": bucket.id,
                    "public": bucket.public,
                    "created_at": bucket.created_at,
                    "updated_at": bucket.updated_at
                }
                for bucket in buckets
            ]

        except Exception as e:
            logger.error(f"Failed to list buckets: {e}")
            return []

    # File Operations
    async def upload_file(self, bucket: str, path: str, file_data: Union[bytes, str], 
                         content_type: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Upload file to storage"""
        try:
            if not self._initialized:
                await self.initialize()

            # Determine content type if not provided
            if not content_type:
                content_type, _ = mimetypes.guess_type(path)
                if not content_type:
                    content_type = "application/octet-stream"

            # Prepare file data
            if isinstance(file_data, str):
                file_data = file_data.encode('utf-8')

            # Upload options
            options = {
                "content-type": content_type,
                "cache-control": "3600",  # 1 hour cache
                "upsert": True  # Allow overwriting
            }

            if metadata:
                options.update(metadata)

            # Upload file
            response = self.supabase.upload_file(bucket, path, file_data, options)

            if response["success"]:
                # Get public URL if bucket is public
                public_url = None
                if await self._is_bucket_public(bucket):
                    public_url = self.supabase.get_public_url(bucket, path)

                return {
                    "success": True,
                    "path": path,
                    "bucket": bucket,
                    "public_url": public_url,
                    "size": len(file_data),
                    "content_type": content_type,
                    "uploaded_at": datetime.now(timezone.utc).isoformat()
                }
            else:
                return response

        except Exception as e:
            logger.error(f"Failed to upload file {path} to {bucket}: {e}")
            return {"success": False, "error": str(e)}

    async def download_file(self, bucket: str, path: str) -> Optional[bytes]:
        """Download file from storage"""
        try:
            if not self._initialized:
                await self.initialize()

            file_data = self.supabase.download_file(bucket, path)
            
            if file_data:
                logger.debug(f"Downloaded file: {bucket}/{path}")
            
            return file_data

        except Exception as e:
            logger.error(f"Failed to download file {path} from {bucket}: {e}")
            return None

    async def delete_file(self, bucket: str, path: str) -> bool:
        """Delete file from storage"""
        try:
            if not self._initialized:
                await self.initialize()

            success = self.supabase.delete_file(bucket, path)
            
            if success:
                logger.info(f"Deleted file: {bucket}/{path}")
            
            return success

        except Exception as e:
            logger.error(f"Failed to delete file {path} from {bucket}: {e}")
            return False

    async def get_public_url(self, bucket: str, path: str) -> Optional[str]:
        """Get public URL for file"""
        try:
            if not self._initialized:
                await self.initialize()

            return self.supabase.get_public_url(bucket, path)

        except Exception as e:
            logger.error(f"Failed to get public URL for {bucket}/{path}: {e}")
            return None

    async def get_signed_url(self, bucket: str, path: str, expires_in: int = 3600) -> Optional[str]:
        """Get signed URL for private file access"""
        try:
            if not self.supabase._storage_client:
                return None

            response = self.supabase._storage_client.from_(bucket).create_signed_url(path, expires_in)
            return response.get("signedURL")

        except Exception as e:
            logger.error(f"Failed to get signed URL for {bucket}/{path}: {e}")
            return None

    async def list_files(self, bucket: str, path: str = "", limit: int = 100) -> List[Dict[str, Any]]:
        """List files in bucket/path"""
        try:
            if not self.supabase._storage_client:
                return []

            files = self.supabase._storage_client.from_(bucket).list(path, {
                "limit": limit,
                "sortBy": {"column": "name", "order": "asc"}
            })

            return [
                {
                    "name": file.name,
                    "id": file.id,
                    "size": file.metadata.get("size"),
                    "content_type": file.metadata.get("mimetype"),
                    "created_at": file.created_at,
                    "updated_at": file.updated_at,
                    "last_accessed_at": file.last_accessed_at
                }
                for file in files
            ]

        except Exception as e:
            logger.error(f"Failed to list files in {bucket}/{path}: {e}")
            return []

    async def get_file_info(self, bucket: str, path: str) -> Optional[Dict[str, Any]]:
        """Get file metadata and information"""
        try:
            files = await self.list_files(bucket, os.path.dirname(path))
            filename = os.path.basename(path)
            
            for file_info in files:
                if file_info["name"] == filename:
                    return file_info
            
            return None

        except Exception as e:
            logger.error(f"Failed to get file info for {bucket}/{path}: {e}")
            return None

    # Helper Methods
    async def _is_bucket_public(self, bucket_name: str) -> bool:
        """Check if bucket is public"""
        try:
            buckets = await self.list_buckets()
            for bucket in buckets:
                if bucket["name"] == bucket_name:
                    return bucket.get("public", False)
            return False

        except Exception:
            return False

    async def get_storage_usage(self) -> Dict[str, Any]:
        """Get storage usage statistics"""
        try:
            buckets = await self.list_buckets()
            total_files = 0
            total_size = 0

            for bucket in buckets:
                files = await self.list_files(bucket["name"])
                bucket_files = len(files)
                bucket_size = sum(file.get("size", 0) for file in files)
                
                total_files += bucket_files
                total_size += bucket_size

            return {
                "total_buckets": len(buckets),
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "buckets": buckets
            }

        except Exception as e:
            logger.error(f"Failed to get storage usage: {e}")
            return {
                "total_buckets": 0,
                "total_files": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "buckets": []
            }
