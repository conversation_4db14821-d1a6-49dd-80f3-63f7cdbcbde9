﻿.PHONY: help build up down logs monitor test lint clean

help:
	@echo "Available commands:"
	@echo "  make build    - Build all containers"
	@echo "  make up       - Start all services"
	@echo "  make down     - Stop all services"
	@echo "  make logs     - View logs"
	@echo "  make monitor  - Run terminal monitor"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make clean    - Clean up"

build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f

monitor:
	@./scripts/monitor.sh --docker

monitor-local:
	@python monitor/monitor.py --url $(CORE_API_URL) --api-key $(CORE_API_KEY)

test:
	@docker-compose run --rm core pytest
	@docker-compose run --rm translation-service pytest

lint:
	@docker-compose run --rm core flake8 .
	@docker-compose run --rm core black --check .

clean:
	@docker-compose down -v
	@find . -type d -name __pycache__ -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete

dev-setup:
	@./scripts/setup.sh
	@cp .env.example .env
	@echo "Please edit .env with your configuration"
