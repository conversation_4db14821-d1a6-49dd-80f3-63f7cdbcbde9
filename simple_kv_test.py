#!/usr/bin/env python3
"""
Simple Cloudflare KV Test
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_kv_direct():
    """Test KV directly with httpx"""
    print("🔍 Testing Cloudflare KV Direct Access")
    
    # Load environment
    try:
        from dotenv import load_dotenv
        load_dotenv(override=True)
    except ImportError:
        pass
    
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')
    
    print(f"Account ID: {account_id}")
    print(f"API Token: {api_token[:10]}...")
    
    try:
        import httpx
        
        # Test list namespaces
        url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/storage/kv/namespaces"
        headers = {"Authorization": f"Bearer {api_token}"}
        
        async with httpx.AsyncClient() as client:
            print(f"\n📡 GET {url}")
            response = await client.get(url, headers=headers)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Found {len(data['result'])} namespaces")
                
                # Create a test namespace
                create_data = {"title": "secrets-test"}
                print(f"\n📡 POST {url} (creating namespace)")
                create_response = await client.post(url, headers=headers, json=create_data)
                print(f"Create Status: {create_response.status_code}")
                
                if create_response.status_code == 200:
                    create_result = create_response.json()
                    if create_result.get('success'):
                        namespace_id = create_result['result']['id']
                        print(f"✅ Created namespace: {namespace_id}")
                        
                        # Test setting a value
                        test_key = "test-secret"
                        test_value = "test-value-123"
                        
                        value_url = f"{url}/{namespace_id}/values/{test_key}"
                        print(f"\n📡 PUT {value_url}")
                        
                        # KV expects form data
                        put_response = await client.put(
                            value_url, 
                            headers={"Authorization": f"Bearer {api_token}"}, 
                            data={"value": test_value}
                        )
                        print(f"Put Status: {put_response.status_code}")
                        
                        if put_response.status_code == 200:
                            print("✅ Value set successfully!")
                            
                            # Test getting the value
                            print(f"\n📡 GET {value_url}")
                            get_response = await client.get(value_url, headers=headers)
                            print(f"Get Status: {get_response.status_code}")
                            
                            if get_response.status_code == 200:
                                retrieved_value = get_response.text
                                print(f"✅ Retrieved value: {retrieved_value}")
                                
                                if retrieved_value == test_value:
                                    print("🎉 SUCCESS! KV is working perfectly!")
                                else:
                                    print(f"❌ Value mismatch: expected {test_value}, got {retrieved_value}")
                            else:
                                print(f"❌ Failed to get value: {get_response.text}")
                        else:
                            print(f"❌ Failed to set value: {put_response.text}")
                    else:
                        print(f"❌ Failed to create namespace: {create_result}")
                else:
                    print(f"❌ Failed to create namespace: {create_response.text}")
            else:
                print(f"❌ Failed to list namespaces: {response.text}")
                
    except Exception as e:
        print(f"❌ Error: {e}")


async def test_kv_provider():
    """Test our KV provider"""
    print("\n🔍 Testing CloudflareKVSecretProvider")
    
    try:
        from core.config.secret_providers import CloudflareKVSecretProvider
        
        account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
        api_token = os.getenv('CLOUDFLARE_API_TOKEN')
        
        provider = CloudflareKVSecretProvider(
            account_id=account_id,
            api_token=api_token,
            namespace_name="secrets-test"
        )
        
        print("✅ Provider created")
        
        # Test health check
        healthy = await provider.health_check()
        print(f"Health check: {healthy}")
        
        if healthy:
            print("🎉 KV Provider is working!")
        else:
            print("❌ KV Provider health check failed")
            
    except Exception as e:
        print(f"❌ Provider test failed: {e}")


async def main():
    print("🚀 Simple Cloudflare KV Test\n")
    
    await test_kv_direct()
    await test_kv_provider()


if __name__ == "__main__":
    asyncio.run(main())
