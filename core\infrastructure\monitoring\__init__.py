"""
Monitoring & Health Systems Sub-Module

This module provides monitoring and health check implementations:
- Health check implementations
- Metrics collection and reporting
- Scoring system implementations
- Performance monitoring

Migrated from: core.infrastructure.health, metrics, scoring
"""

from typing import Optional, Any

# Import modular implementations first, then fallback to legacy
try:
    from .health import SystemHealthCheck, HealthChecker
except ImportError:
    SystemHealthCheck = None
    HealthChecker = None

try:
    from .metrics import PrometheusMetricsCollector
except ImportError:
    try:
        from ..metrics import PrometheusMetricsCollector
    except ImportError:
        PrometheusMetricsCollector = None

try:
    from .scoring import MetricsAggregator, ScoringEngine
except ImportError:
    try:
        from ..scoring import MetricsAggregator, ScoringEngine
    except ImportError:
        MetricsAggregator = None
        ScoringEngine = None

# Alias for backward compatibility
ScoringSystem = ScoringEngine

__all__ = [
    # Health checks
    "SystemHealthCheck",
    "HealthChecker",

    # Metrics
    "PrometheusMetricsCollector",

    # Scoring
    "MetricsAggregator",
    "ScoringEngine",
    "ScoringSystem",
]
