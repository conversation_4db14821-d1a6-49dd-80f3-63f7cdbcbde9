import pytest
import time
from datetime import datetime
from unittest.mock import patch

from core.infrastructure.metrics import InMemoryMetricsCollector as MetricsCollector


@pytest.fixture
def metrics_collector():
    return MetricsCollector()


def test_increment_counter(metrics_collector):
    """Test incrementing a counter"""
    # Increment counter without labels
    metrics_collector.increment_counter("requests")
    assert metrics_collector.counters["requests"] == 1

    # Increment again
    metrics_collector.increment_counter("requests")
    assert metrics_collector.counters["requests"] == 2

    # Increment with custom value
    metrics_collector.increment_counter("requests", 5)
    assert metrics_collector.counters["requests"] == 7

    # Increment with labels
    metrics_collector.increment_counter("requests", labels={"endpoint": "/api"})
    assert metrics_collector.counters["requests{endpoint=/api}"] == 1


def test_set_gauge(metrics_collector):
    """Test setting a gauge"""
    # Set gauge without labels
    metrics_collector.set_gauge("memory_usage", 1024)
    assert metrics_collector.gauges["memory_usage"] == 1024

    # Update gauge
    metrics_collector.set_gauge("memory_usage", 2048)
    assert metrics_collector.gauges["memory_usage"] == 2048

    # Set gauge with labels
    metrics_collector.set_gauge("cpu_usage", 50.5, labels={"core": "0"})
    assert metrics_collector.gauges["cpu_usage{core=0}"] == 50.5


def test_record_histogram(metrics_collector):
    """Test recording histogram values"""
    # Record value without labels
    metrics_collector.record_histogram("response_time", 0.05)
    assert len(metrics_collector.metrics["response_time"]) == 1
    assert metrics_collector.metrics["response_time"][0]["value"] == 0.05

    # Record another value
    metrics_collector.record_histogram("response_time", 0.1)
    assert len(metrics_collector.metrics["response_time"]) == 2

    # Record with labels
    metrics_collector.record_histogram("response_time", 0.2, labels={"endpoint": "/api"})
    assert len(metrics_collector.metrics["response_time{endpoint=/api}"]) == 1


def test_make_key(metrics_collector):
    """Test making metric keys with labels"""
    # Key without labels
    key = metrics_collector._make_key("requests")
    assert key == "requests"

    # Key with single label
    key = metrics_collector._make_key("requests", {"endpoint": "/api"})
    assert key == "requests{endpoint=/api}"

    # Key with multiple labels (should be sorted)
    key = metrics_collector._make_key("requests", {"method": "GET", "endpoint": "/api"})
    assert key == "requests{endpoint=/api,method=GET}"


def test_get_metrics(metrics_collector):
    """Test getting all metrics"""
    # Add some test metrics
    metrics_collector.increment_counter("requests", 10)
    metrics_collector.set_gauge("memory_usage", 1024)
    metrics_collector.record_histogram("response_time", 0.1)
    metrics_collector.record_histogram("response_time", 0.3)

    # Mock time for consistent uptime
    with patch('time.time') as mock_time:
        # Mock start time as 100 seconds ago
        metrics_collector._start_time = 1000
        mock_time.return_value = 1100  # Current time

        metrics = metrics_collector.get_metrics()

        # Check uptime
        assert metrics["uptime_seconds"] == 100

        # Check counters
        assert "requests" in metrics["counters"]
        assert metrics["counters"]["requests"] == 10

        # Check gauges
        assert "memory_usage" in metrics["gauges"]
        assert metrics["gauges"]["memory_usage"] == 1024

        # Check histograms
        assert "response_time" in metrics["histograms"]
        assert metrics["histograms"]["response_time"]["count"] == 2
        assert metrics["histograms"]["response_time"]["sum"] == 0.4
        assert metrics["histograms"]["response_time"]["avg"] == 0.2