"""
Authentication & Authorization Sub-Module

This module provides authentication and authorization providers:
- Security providers and implementations
- Authentication utilities and helpers
- Authorization and permission management

Migrated from: core.infrastructure.security
"""

# Import from parent directory for backward compatibility
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from .security import JWTSecurityProvider, DatabaseSecurityProvider
except ImportError:
    try:
        from ..security import JWTSecurityProvider, DatabaseSecurityProvider
    except ImportError:
        JWTSecurityProvider = None
        DatabaseSecurityProvider = None

try:
    from .providers import SupabaseAuthProvider
except ImportError:
    SupabaseAuthProvider = None

__all__ = [
    "JWTSecurityProvider",
    "DatabaseSecurityProvider",
    "SupabaseAuthProvider",
]
