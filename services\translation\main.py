# services/translation/main.py
import os
import httpx
from fastapi import FastAPI
from pydantic import BaseModel
from contextlib import asynccontextmanager
from typing import Dict, List

from services import create_service_app

# Service configuration
SERVICE_NAME = "translation-service"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = int(os.getenv("SERVICE_PORT", "8001"))
CORE_API_URL = os.getenv("CORE_API_URL", "http://core-api:8000")
CORE_API_KEY = os.getenv("CORE_API_KEY", "")
SERVICE_API_KEY = os.getenv("SERVICE_API_KEY", "")


class TranslationRequest(BaseModel):
    text: str
    source_language: str = "auto"
    target_language: str


class TranslationResponse(BaseModel):
    translated_text: str
    source_language: str
    target_language: str


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Service lifespan management"""
    # Register with core API on startup
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{CORE_API_URL}/api/v1/services/register",
                json={
                    "name": SERVICE_NAME,
                    "url": f"http://{SERVICE_NAME}:{SERVICE_PORT}",
                    "version": SERVICE_VERSION,
                    "health_endpoint": "/health",
                    "restart_endpoint": "/admin/restart",
                    "metadata": {
                        "supported_languages": ["en", "es", "fr", "de", "zh", "ja"]
                    }
                },
                headers={"X-API-Key": CORE_API_KEY}
            )
            
            if response.status_code == 200:
                result = response.json()
                # Store the API key securely
                if "api_key" in result:
                    os.environ["SERVICE_API_KEY"] = result["api_key"]
                print(f"✅ Successfully registered {SERVICE_NAME} with Core API")
            else:
                print(f"❌ Failed to register with Core API: {response.text}")
    except Exception as e:
        print(f"❌ Error during service registration: {e}")
    
    yield
    
    # Unregister on shutdown
    try:
        async with httpx.AsyncClient() as client:
            await client.delete(
                f"{CORE_API_URL}/api/v1/services/{SERVICE_NAME}",
                headers={"X-API-Key": CORE_API_KEY}
            )
            print(f"👋 Unregistered {SERVICE_NAME} from Core API")
    except Exception as e:
        print(f"❌ Error during service unregistration: {e}")


# Create the application
app = create_service_app(
    title="Translation Service",
    version=SERVICE_VERSION,
    description="Service for translating text between languages",
    admin_enabled=True
)

# Set the lifespan
app.router.lifespan_context = lifespan


@app.post("/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """Translate text between languages"""
    # Simplified implementation - in a real service this would call a translation API
    translated = f"[{request.target_language}] {request.text}"
    
    return TranslationResponse(
        translated_text=translated,
        source_language=request.source_language,
        target_language=request.target_language
    )


@app.get("/languages")
async def list_supported_languages():
    """List all supported languages"""
    languages = {
        "en": "English",
        "es": "Spanish",
        "fr": "French",
        "de": "German",
        "zh": "Chinese",
        "ja": "Japanese"
    }
    return {"languages": languages}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=SERVICE_PORT,
        reload=os.getenv("DEBUG", "").lower() == "true"
    )