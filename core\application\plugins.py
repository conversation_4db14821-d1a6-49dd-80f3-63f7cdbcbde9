"""
Plugin Management System

This module provides a comprehensive plugin management system that allows
for dynamic loading, configuration, and lifecycle management of plugins.
"""

import asyncio
import importlib
import inspect
import logging
from typing import Dict, List, Optional, Type, Any
from pathlib import Path

from core.domain.interfaces import IPlugin, PluginStatus
from core.domain.exceptions import PluginError
from core.infrastructure.container import Container


logger = logging.getLogger(__name__)


class PluginDescriptor:
    """Describes a plugin and its metadata"""

    def __init__(
        self,
        name: str,
        version: str,
        plugin_class: Type[IPlugin],
        module_path: str,
        dependencies: Optional[List[str]] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        self.name = name
        self.version = version
        self.plugin_class = plugin_class
        self.module_path = module_path
        self.dependencies = dependencies or []
        self.config = config or {}
        self.instance: Optional[IPlugin] = None
        self.status = PluginStatus.LOADED


class PluginManager:
    """Manages the lifecycle of plugins"""

    def __init__(self, container: Container):
        self.container = container
        self.plugins: Dict[str, PluginDescriptor] = {}
        self._load_order: List[str] = []
        self._plugin_directories: List[Path] = [
            Path("plugins"),
            Path("core/plugins"),
            Path("services/*/plugins"),
        ]

    async def load_plugins(self) -> None:
        """Load all available plugins"""
        logger.info("🔌 Loading plugins...")

        # Discover plugins
        await self._discover_plugins()

        # Resolve dependencies
        self._resolve_dependencies()

        # Load plugins in dependency order
        for plugin_name in self._load_order:
            await self._load_plugin(plugin_name)

        logger.info(f"✅ Loaded {len(self.plugins)} plugins")

    async def unload_plugins(self) -> None:
        """Unload all plugins in reverse order"""
        logger.info("🔌 Unloading plugins...")

        # Unload in reverse order
        for plugin_name in reversed(self._load_order):
            await self._unload_plugin(plugin_name)

        self.plugins.clear()
        self._load_order.clear()

        logger.info("✅ All plugins unloaded")

    async def reload_plugin(self, plugin_name: str) -> None:
        """Reload a specific plugin"""
        if plugin_name not in self.plugins:
            raise PluginError(plugin_name, "reload", "Plugin not found")

        await self._unload_plugin(plugin_name)
        await self._load_plugin(plugin_name)

    def get_plugin(self, plugin_name: str) -> Optional[IPlugin]:
        """Get a plugin instance by name"""
        descriptor = self.plugins.get(plugin_name)
        return descriptor.instance if descriptor else None

    def get_plugins_by_status(self, status: PluginStatus) -> List[IPlugin]:
        """Get all plugins with a specific status"""
        return [
            descriptor.instance
            for descriptor in self.plugins.values()
            if descriptor.status == status and descriptor.instance
        ]

    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """List all plugins with their metadata"""
        return {
            name: {
                "name": descriptor.name,
                "version": descriptor.version,
                "status": descriptor.status.value,
                "dependencies": descriptor.dependencies,
                "module_path": descriptor.module_path,
            }
            for name, descriptor in self.plugins.items()
        }

    async def _discover_plugins(self) -> None:
        """Discover plugins in configured directories"""
        for directory in self._plugin_directories:
            if directory.exists():
                await self._scan_directory(directory)

    async def _scan_directory(self, directory: Path) -> None:
        """Scan a directory for plugin modules"""
        for file_path in directory.rglob("*.py"):
            if file_path.name.startswith("_"):
                continue

            try:
                await self._load_plugin_module(file_path)
            except Exception as e:
                logger.warning(f"Failed to scan plugin {file_path}: {e}")

    async def _load_plugin_module(self, file_path: Path) -> None:
        """Load a plugin module and register the plugin"""
        # Convert file path to module path
        module_path = str(file_path.with_suffix("")).replace("/", ".").replace("\\", ".")

        try:
            # Import the module
            module = importlib.import_module(module_path)

            # Look for plugin classes
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (hasattr(obj, "__bases__") and
                    any(hasattr(base, "name") and hasattr(base, "version") for base in obj.__bases__)):

                    # Check if it implements IPlugin
                    if self._implements_plugin_interface(obj):
                        await self._register_plugin(obj, module_path)

        except Exception as e:
            logger.warning(f"Failed to load plugin module {module_path}: {e}")

    def _implements_plugin_interface(self, plugin_class: Type) -> bool:
        """Check if a class implements the IPlugin interface"""
        required_methods = ["name", "version", "dependencies", "load", "unload", "configure", "status"]
        return all(hasattr(plugin_class, method) for method in required_methods)

    async def _register_plugin(self, plugin_class: Type, module_path: str) -> None:
        """Register a plugin class"""
        try:
            # Create a temporary instance to get metadata
            temp_instance = plugin_class()

            descriptor = PluginDescriptor(
                name=temp_instance.name,
                version=temp_instance.version,
                plugin_class=plugin_class,
                module_path=module_path,
                dependencies=temp_instance.dependencies,
            )

            self.plugins[descriptor.name] = descriptor
            logger.debug(f"Registered plugin: {descriptor.name} v{descriptor.version}")

        except Exception as e:
            logger.error(f"Failed to register plugin {plugin_class.__name__}: {e}")

    def _resolve_dependencies(self) -> None:
        """Resolve plugin dependencies and determine load order"""
        # Topological sort for dependency resolution
        visited = set()
        temp_visited = set()
        self._load_order = []

        def visit(plugin_name: str):
            if plugin_name in temp_visited:
                raise PluginError(plugin_name, "dependency_resolution", "Circular dependency detected")

            if plugin_name in visited:
                return

            temp_visited.add(plugin_name)

            if plugin_name in self.plugins:
                for dependency in self.plugins[plugin_name].dependencies:
                    if dependency not in self.plugins:
                        raise PluginError(
                            plugin_name,
                            "dependency_resolution",
                            f"Missing dependency: {dependency}"
                        )
                    visit(dependency)

            temp_visited.remove(plugin_name)
            visited.add(plugin_name)
            self._load_order.append(plugin_name)

        for plugin_name in self.plugins:
            if plugin_name not in visited:
                visit(plugin_name)

    async def _load_plugin(self, plugin_name: str) -> None:
        """Load and initialize a specific plugin"""
        descriptor = self.plugins[plugin_name]

        try:
            # Create plugin instance
            descriptor.instance = descriptor.plugin_class()

            # Configure the plugin
            await descriptor.instance.configure(descriptor.config)

            # Load the plugin
            await descriptor.instance.load()

            # Register plugin services in the container if needed
            self._register_plugin_services(descriptor)

            descriptor.status = PluginStatus.ACTIVE
            logger.info(f"✅ Loaded plugin: {plugin_name} v{descriptor.version}")

        except Exception as e:
            descriptor.status = PluginStatus.ERROR
            logger.error(f"❌ Failed to load plugin {plugin_name}: {e}")
            raise PluginError(plugin_name, "load", str(e))

    async def _unload_plugin(self, plugin_name: str) -> None:
        """Unload a specific plugin"""
        descriptor = self.plugins[plugin_name]

        if descriptor.instance and descriptor.status == PluginStatus.ACTIVE:
            try:
                await descriptor.instance.unload()
                descriptor.status = PluginStatus.INACTIVE
                logger.info(f"✅ Unloaded plugin: {plugin_name}")
            except Exception as e:
                logger.error(f"❌ Failed to unload plugin {plugin_name}: {e}")
                descriptor.status = PluginStatus.ERROR

    def _register_plugin_services(self, descriptor: PluginDescriptor) -> None:
        """Register plugin services in the DI container"""
        # Plugins can register their own services by implementing a register_services method
        if hasattr(descriptor.instance, "register_services"):
            try:
                descriptor.instance.register_services(self.container)
            except Exception as e:
                logger.warning(f"Failed to register services for plugin {descriptor.name}: {e}")


class BasePlugin:
    """Base class for plugins"""

    def __init__(self):
        self._status = PluginStatus.LOADED
        self._config: Dict[str, Any] = {}

    @property
    def name(self) -> str:
        """Plugin name"""
        return self.__class__.__name__

    @property
    def version(self) -> str:
        """Plugin version"""
        return "1.0.0"

    @property
    def dependencies(self) -> List[str]:
        """Plugin dependencies"""
        return []

    @property
    def status(self) -> PluginStatus:
        """Current plugin status"""
        return self._status

    async def load(self) -> None:
        """Load the plugin"""
        self._status = PluginStatus.ACTIVE
        await self.on_load()

    async def unload(self) -> None:
        """Unload the plugin"""
        await self.on_unload()
        self._status = PluginStatus.INACTIVE

    async def configure(self, config: Dict[str, Any]) -> None:
        """Configure the plugin"""
        self._config = config
        await self.on_configure(config)

    async def on_load(self) -> None:
        """Override this method to implement plugin loading logic"""
        pass

    async def on_unload(self) -> None:
        """Override this method to implement plugin unloading logic"""
        pass

    async def on_configure(self, config: Dict[str, Any]) -> None:
        """Override this method to implement plugin configuration logic"""
        pass

    def register_services(self, container: Container) -> None:
        """Override this method to register services in the DI container"""
        pass