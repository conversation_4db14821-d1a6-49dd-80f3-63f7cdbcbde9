"""
Core API Router

This module provides the main API endpoints for the core system,
including service registration, health checks, and service management.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, HttpUrl

from core.domain.interfaces import IServiceRegistry, IMetricsCollector, IHealthCheck
from core.infrastructure.container import Container


router = APIRouter(
    prefix="/api/v1",
    tags=["core"],
    responses={404: {"description": "Not found"}}
)


class ServiceRegistrationRequest(BaseModel):
    """Request model for service registration"""
    name: str
    url: HttpUrl
    version: str
    health_endpoint: str = "/health"
    api_key: Optional[str] = None
    metadata: Dict[str, Any] = {}
    restart_endpoint: str = "/admin/restart"


class ServiceCallRequest(BaseModel):
    """Request model for calling services"""
    service: str
    endpoint: str
    method: str = "GET"
    data: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None


class HealthResponse(BaseModel):
    """Response model for health checks"""
    status: str
    timestamp: str
    version: str
    uptime: float


class MetricsResponse(BaseModel):
    """Response model for metrics"""
    metrics: Dict[str, Any]
    timestamp: str


def get_container(request: Request) -> Container:
    """Get the dependency injection container from app state"""
    return request.app.state.container


async def get_service_registry(container: Container = Depends(get_container)) -> IServiceRegistry:
    """Get the service registry dependency"""
    return await container.get_service_async(IServiceRegistry)


async def get_metrics_collector(container: Container = Depends(get_container)) -> IMetricsCollector:
    """Get the metrics collector dependency"""
    return await container.get_service_async(IMetricsCollector)


async def get_health_checker(container: Container = Depends(get_container)) -> IHealthCheck:
    """Get the health checker dependency"""
    return await container.get_service_async(IHealthCheck)


@router.get("/health", response_model=HealthResponse)
async def health_check(
    health_checker: IHealthCheck = Depends(get_health_checker)
) -> HealthResponse:
    """Core system health check"""
    health_data = await health_checker.check_health()
    return HealthResponse(**health_data)


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics(
    metrics_collector: IMetricsCollector = Depends(get_metrics_collector)
) -> MetricsResponse:
    """Get system metrics"""
    metrics_data = await metrics_collector.get_metrics()
    return MetricsResponse(
        metrics=metrics_data,
        timestamp=metrics_data.get("timestamp", "")
    )


@router.post("/services/register")
async def register_service(
    service_data: ServiceRegistrationRequest,
    service_registry: IServiceRegistry = Depends(get_service_registry)
) -> Dict[str, Any]:
    """Register a new service"""
    try:
        result = await service_registry.register_service(
            name=service_data.name,
            url=str(service_data.url),
            version=service_data.version,
            health_endpoint=service_data.health_endpoint,
            metadata=service_data.metadata
        )
        
        if result.get("success"):
            return {
                "status": "registered",
                "service": service_data.name,
                "api_key": result.get("api_key")
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to register service")
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service registration failed: {str(e)}"
        )


@router.delete("/services/{service_name}")
async def unregister_service(
    service_name: str,
    service_registry: IServiceRegistry = Depends(get_service_registry)
) -> Dict[str, str]:
    """Unregister a service"""
    try:
        result = await service_registry.unregister_service(service_name)
        
        if result.get("success"):
            return {"status": "unregistered", "service": service_name}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result.get("error", "Service not found")
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service unregistration failed: {str(e)}"
        )


@router.post("/services/{service_name}/restart")
async def restart_service(
    service_name: str,
    service_registry: IServiceRegistry = Depends(get_service_registry),
    metrics_collector: IMetricsCollector = Depends(get_metrics_collector)
) -> Dict[str, Any]:
    """Restart a registered service"""
    try:
        result = await service_registry.restart_service(service_name)
        
        if result.get("success"):
            # Track the restart in metrics
            metrics_collector.increment_counter(
                "service_restarts",
                tags={"service": service_name}
            )
            return result
        else:
            error = result.get("error", "Failed to restart service")
            if "not found" in error.lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=error
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=error
                )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service restart failed: {str(e)}"
        )


@router.get("/services")
async def list_services(
    only_healthy: bool = False,
    service_registry: IServiceRegistry = Depends(get_service_registry)
) -> Dict[str, List[Dict[str, Any]]]:
    """List all registered services"""
    try:
        services = await service_registry.list_services(only_healthy=only_healthy)
        return {"services": services}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list services: {str(e)}"
        )


@router.post("/services/call")
async def call_service(
    call_request: ServiceCallRequest,
    service_registry: IServiceRegistry = Depends(get_service_registry)
) -> Dict[str, Any]:
    """Call a registered service"""
    try:
        response = await service_registry.call_service(
            service_name=call_request.service,
            endpoint=call_request.endpoint,
            method=call_request.method,
            data=call_request.data,
            headers=call_request.headers or {}
        )
        
        if response:
            return {
                "status_code": response.get("status_code"),
                "content": response.get("content"),
                "headers": response.get("headers", {})
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service unavailable"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service call failed: {str(e)}"
        ) 