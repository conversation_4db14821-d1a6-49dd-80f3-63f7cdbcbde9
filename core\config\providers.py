"""
Configuration Providers

This module provides different sources for configuration values
including environment variables, files, and external secret stores.
"""

import os
import json
import logging
import asyncio
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Optional, List, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass

from core.domain.interfaces import IConfigurationProvider


@dataclass
class SecretMetadata:
    """Metadata for a secret"""
    key: str
    version: Optional[str] = None
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    rotation_enabled: bool = False
    tags: Optional[Dict[str, str]] = None


@dataclass
class SecretValue:
    """A secret value with metadata"""
    value: str
    metadata: SecretMetadata

    def is_expired(self) -> bool:
        """Check if the secret is expired"""
        if not self.metadata.expires_at:
            return False

        # Handle timezone-aware vs timezone-naive datetime comparison
        now = datetime.now(timezone.utc)
        expires_at = self.metadata.expires_at

        # If expires_at is timezone-naive, assume it's UTC
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)

        return now > expires_at


class ISecretProvider(ABC):
    """Interface for secret providers"""

    @abstractmethod
    async def get_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Get a secret by key"""
        pass

    @abstractmethod
    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret"""
        pass

    @abstractmethod
    async def delete_secret(self, key: str) -> bool:
        """Delete a secret"""
        pass

    @abstractmethod
    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List available secret keys"""
        pass

    @abstractmethod
    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret"""
        pass

    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the secret provider is healthy"""
        pass


class BaseSecretProvider(ISecretProvider):
    """Base class for secret providers with common functionality"""

    def __init__(self, cache_ttl: int = 300):
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, SecretValue] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self.logger = logging.getLogger(self.__class__.__name__)

    def _is_cache_valid(self, key: str) -> bool:
        """Check if cached value is still valid"""
        if key not in self._cache_timestamps:
            return False

        cache_time = self._cache_timestamps[key]
        return datetime.utcnow() - cache_time < timedelta(seconds=self.cache_ttl)

    def _cache_secret(self, key: str, secret: SecretValue) -> None:
        """Cache a secret value"""
        self._cache[key] = secret
        self._cache_timestamps[key] = datetime.utcnow()

    def _get_cached_secret(self, key: str) -> Optional[SecretValue]:
        """Get secret from cache if valid"""
        if self._is_cache_valid(key):
            cached_secret = self._cache.get(key)
            if cached_secret and not cached_secret.is_expired():
                return cached_secret

        # Remove expired or invalid cache entries
        if key in self._cache:
            del self._cache[key]
        if key in self._cache_timestamps:
            del self._cache_timestamps[key]

        return None

    async def get_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Get secret with caching"""
        cache_key = f"{key}:{version}" if version else key

        # Try cache first
        cached = self._get_cached_secret(cache_key)
        if cached:
            return cached

        # Fetch from provider
        try:
            secret = await self._fetch_secret(key, version)
            if secret:
                self._cache_secret(cache_key, secret)
            return secret
        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key}: {e}")
            return None

    @abstractmethod
    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from the actual provider (to be implemented by subclasses)"""
        pass


class EnvironmentConfigProvider(IConfigurationProvider):
    """Configuration provider that reads from environment variables"""

    def __init__(self, prefix: str = ""):
        self.prefix = prefix
        self._cache: Dict[str, Any] = {}
        self.reload()

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value from environment"""
        env_key = f"{self.prefix}{key}".upper() if self.prefix else key.upper()
        return self._cache.get(env_key, default)

    def set(self, key: str, value: Any) -> None:
        """Set environment variable (for current process only)"""
        env_key = f"{self.prefix}{key}".upper() if self.prefix else key.upper()
        os.environ[env_key] = str(value)
        self._cache[env_key] = value

    def reload(self) -> None:
        """Reload configuration from environment"""
        self._cache.clear()
        for key, value in os.environ.items():
            if not self.prefix or key.startswith(self.prefix.upper()):
                # Try to parse as JSON first, then as string
                try:
                    self._cache[key] = json.loads(value)
                except (json.JSONDecodeError, ValueError):
                    self._cache[key] = value


class FileConfigProvider(IConfigurationProvider):
    """Configuration provider that reads from a file"""

    def __init__(self, file_path: str, file_format: str = "auto"):
        self.file_path = Path(file_path)
        self.file_format = file_format
        self._config: Dict[str, Any] = {}
        if self.file_path.exists():
            self.reload()

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value from file"""
        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set(self, key: str, value: Any) -> None:
        """Set configuration value (in memory only)"""
        keys = key.split('.')
        config = self._config

        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def reload(self) -> None:
        """Reload configuration from file"""
        if not self.file_path.exists():
            self._config = {}
            return

        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Determine file format
            format_type = self.file_format
            if format_type == "auto":
                if self.file_path.suffix.lower() == '.json':
                    format_type = "json"
                elif self.file_path.suffix.lower() in ['.yml', '.yaml']:
                    format_type = "yaml"
                else:
                    format_type = "json"  # Default to JSON

            # Parse based on format
            if format_type == "json":
                self._config = json.loads(content)
            elif format_type == "yaml":
                try:
                    import yaml
                    self._config = yaml.safe_load(content)
                except ImportError:
                    raise ImportError("PyYAML required for YAML configuration files")
            else:
                raise ValueError(f"Unsupported configuration file format: {format_type}")

        except Exception as e:
            print(f"Warning: Failed to load configuration from {self.file_path}: {e}")
            self._config = {}

    def save(self) -> None:
        """Save configuration to file"""
        try:
            # Determine file format
            format_type = self.file_format
            if format_type == "auto":
                if self.file_path.suffix.lower() == '.json':
                    format_type = "json"
                elif self.file_path.suffix.lower() in ['.yml', '.yaml']:
                    format_type = "yaml"
                else:
                    format_type = "json"

            # Create directory if it doesn't exist
            self.file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write based on format
            with open(self.file_path, 'w', encoding='utf-8') as f:
                if format_type == "json":
                    json.dump(self._config, f, indent=2)
                elif format_type == "yaml":
                    try:
                        import yaml
                        yaml.safe_dump(self._config, f, default_flow_style=False)
                    except ImportError:
                        raise ImportError("PyYAML required for YAML configuration files")

        except Exception as e:
            print(f"Error: Failed to save configuration to {self.file_path}: {e}")


class VaultSecretProvider(BaseSecretProvider):
    """HashiCorp Vault secret provider"""

    def __init__(
        self,
        vault_url: str,
        vault_token: Optional[str] = None,
        vault_role_id: Optional[str] = None,
        vault_secret_id: Optional[str] = None,
        mount_point: str = "secret",
        kv_version: int = 2,
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.vault_url = vault_url.rstrip('/')
        self.vault_token = vault_token
        self.vault_role_id = vault_role_id
        self.vault_secret_id = vault_secret_id
        self.mount_point = mount_point
        self.kv_version = kv_version
        self._authenticated = False

        # Try to import hvac (HashiCorp Vault client)
        try:
            import hvac
            self._hvac = hvac
            self._vault_available = True
        except ImportError:
            self._vault_available = False
            self.logger.warning("hvac library not available. Install with: pip install hvac")

    async def _authenticate(self) -> bool:
        """Authenticate with Vault"""
        if not self._vault_available:
            return False

        try:
            client = self._hvac.Client(url=self.vault_url)

            if self.vault_token:
                client.token = self.vault_token
            elif self.vault_role_id and self.vault_secret_id:
                # AppRole authentication
                auth_response = client.auth.approle.login(
                    role_id=self.vault_role_id,
                    secret_id=self.vault_secret_id
                )
                client.token = auth_response['auth']['client_token']
            else:
                self.logger.error("No authentication method provided for Vault")
                return False

            # Test authentication
            if client.is_authenticated():
                self._client = client
                self._authenticated = True
                return True
            else:
                self.logger.error("Vault authentication failed")
                return False

        except Exception as e:
            self.logger.error(f"Vault authentication error: {e}")
            return False

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from Vault"""
        if not self._authenticated and not await self._authenticate():
            return None

        try:
            if self.kv_version == 2:
                # KV v2 engine
                path = f"{self.mount_point}/data/{key}"
                response = self._client.secrets.kv.v2.read_secret_version(
                    path=key,
                    mount_point=self.mount_point,
                    version=version
                )
                secret_data = response['data']['data']
                metadata = response['data']['metadata']

                # Extract the actual secret value (assuming single value or 'value' key)
                if 'value' in secret_data:
                    value = secret_data['value']
                elif len(secret_data) == 1:
                    value = list(secret_data.values())[0]
                else:
                    # Multiple values, return as JSON
                    value = json.dumps(secret_data)

                secret_metadata = SecretMetadata(
                    key=key,
                    version=str(metadata.get('version')),
                    created_at=datetime.fromisoformat(metadata['created_time'].replace('Z', '+00:00')) if metadata.get('created_time') else None,
                    tags=metadata.get('custom_metadata', {})
                )

            else:
                # KV v1 engine
                path = f"{self.mount_point}/{key}"
                response = self._client.secrets.kv.v1.read_secret(
                    path=key,
                    mount_point=self.mount_point
                )
                secret_data = response['data']

                if 'value' in secret_data:
                    value = secret_data['value']
                elif len(secret_data) == 1:
                    value = list(secret_data.values())[0]
                else:
                    value = json.dumps(secret_data)

                secret_metadata = SecretMetadata(key=key)

            return SecretValue(value=str(value), metadata=secret_metadata)

        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key} from Vault: {e}")
            return None

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in Vault"""
        if not self._authenticated and not await self._authenticate():
            return False

        try:
            secret_data = {'value': value}

            if self.kv_version == 2:
                self._client.secrets.kv.v2.create_or_update_secret(
                    path=key,
                    secret=secret_data,
                    mount_point=self.mount_point
                )
            else:
                self._client.secrets.kv.v1.create_or_update_secret(
                    path=key,
                    secret=secret_data,
                    mount_point=self.mount_point
                )

            return True

        except Exception as e:
            self.logger.error(f"Failed to set secret {key} in Vault: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from Vault"""
        if not self._authenticated and not await self._authenticate():
            return False

        try:
            if self.kv_version == 2:
                self._client.secrets.kv.v2.delete_metadata_and_all_versions(
                    path=key,
                    mount_point=self.mount_point
                )
            else:
                self._client.secrets.kv.v1.delete_secret(
                    path=key,
                    mount_point=self.mount_point
                )

            return True

        except Exception as e:
            self.logger.error(f"Failed to delete secret {key} from Vault: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in Vault"""
        if not self._authenticated and not await self._authenticate():
            return []

        try:
            if self.kv_version == 2:
                response = self._client.secrets.kv.v2.list_secrets(
                    path=prefix or "",
                    mount_point=self.mount_point
                )
            else:
                response = self._client.secrets.kv.v1.list_secrets(
                    path=prefix or "",
                    mount_point=self.mount_point
                )

            return response['data']['keys']

        except Exception as e:
            self.logger.error(f"Failed to list secrets from Vault: {e}")
            return []

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret (placeholder - requires custom logic)"""
        self.logger.warning(f"Secret rotation for {key} requires custom implementation")
        return False

    async def health_check(self) -> bool:
        """Check Vault health"""
        if not self._vault_available:
            return False

        try:
            if not self._authenticated and not await self._authenticate():
                return False

            # Simple health check - try to read sys/health
            response = self._client.sys.read_health_status()
            return response.get('initialized', False) and not response.get('sealed', True)

        except Exception as e:
            self.logger.error(f"Vault health check failed: {e}")
            return False


class AWSSecretsManagerProvider(BaseSecretProvider):
    """AWS Secrets Manager secret provider"""

    def __init__(
        self,
        region_name: str = "us-east-1",
        aws_access_key_id: Optional[str] = None,
        aws_secret_access_key: Optional[str] = None,
        aws_session_token: Optional[str] = None,
        cache_ttl: int = 300
    ):
        super().__init__(cache_ttl)
        self.region_name = region_name
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.aws_session_token = aws_session_token

        # Try to import boto3
        try:
            import boto3
            from botocore.exceptions import ClientError, NoCredentialsError
            self._boto3 = boto3
            self._ClientError = ClientError
            self._NoCredentialsError = NoCredentialsError
            self._aws_available = True
            self._client = None
        except ImportError:
            self._aws_available = False
            self.logger.warning("boto3 library not available. Install with: pip install boto3")

    def _get_client(self):
        """Get or create AWS Secrets Manager client"""
        if not self._aws_available:
            return None

        if self._client is None:
            try:
                session_kwargs = {'region_name': self.region_name}

                if self.aws_access_key_id and self.aws_secret_access_key:
                    session_kwargs.update({
                        'aws_access_key_id': self.aws_access_key_id,
                        'aws_secret_access_key': self.aws_secret_access_key
                    })
                    if self.aws_session_token:
                        session_kwargs['aws_session_token'] = self.aws_session_token

                session = self._boto3.Session(**session_kwargs)
                self._client = session.client('secretsmanager')

            except Exception as e:
                self.logger.error(f"Failed to create AWS Secrets Manager client: {e}")
                return None

        return self._client

    async def _fetch_secret(self, key: str, version: Optional[str] = None) -> Optional[SecretValue]:
        """Fetch secret from AWS Secrets Manager"""
        client = self._get_client()
        if not client:
            return None

        try:
            kwargs = {'SecretId': key}
            if version:
                kwargs['VersionId'] = version

            response = client.get_secret_value(**kwargs)

            # Extract secret value
            if 'SecretString' in response:
                value = response['SecretString']
            elif 'SecretBinary' in response:
                value = response['SecretBinary'].decode('utf-8')
            else:
                self.logger.error(f"No secret value found for {key}")
                return None

            # Create metadata
            secret_metadata = SecretMetadata(
                key=key,
                version=response.get('VersionId'),
                created_at=response.get('CreatedDate'),
                tags={tag['Key']: tag['Value'] for tag in response.get('Tags', [])}
            )

            return SecretValue(value=value, metadata=secret_metadata)

        except self._ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ResourceNotFoundException':
                self.logger.warning(f"Secret {key} not found in AWS Secrets Manager")
            else:
                self.logger.error(f"AWS Secrets Manager error for {key}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to fetch secret {key} from AWS Secrets Manager: {e}")
            return None

    async def set_secret(self, key: str, value: str, metadata: Optional[SecretMetadata] = None) -> bool:
        """Set a secret in AWS Secrets Manager"""
        client = self._get_client()
        if not client:
            return False

        try:
            # Try to update existing secret first
            try:
                client.update_secret(
                    SecretId=key,
                    SecretString=value
                )
                return True
            except self._ClientError as e:
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                    # Secret doesn't exist, create it
                    kwargs = {
                        'Name': key,
                        'SecretString': value
                    }

                    if metadata and metadata.tags:
                        kwargs['Tags'] = [
                            {'Key': k, 'Value': v} for k, v in metadata.tags.items()
                        ]

                    client.create_secret(**kwargs)
                    return True
                else:
                    raise

        except Exception as e:
            self.logger.error(f"Failed to set secret {key} in AWS Secrets Manager: {e}")
            return False

    async def delete_secret(self, key: str) -> bool:
        """Delete a secret from AWS Secrets Manager"""
        client = self._get_client()
        if not client:
            return False

        try:
            client.delete_secret(
                SecretId=key,
                ForceDeleteWithoutRecovery=True
            )
            return True

        except self._ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                self.logger.warning(f"Secret {key} not found for deletion")
                return True  # Already deleted
            else:
                self.logger.error(f"Failed to delete secret {key}: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Failed to delete secret {key} from AWS Secrets Manager: {e}")
            return False

    async def list_secrets(self, prefix: Optional[str] = None) -> List[str]:
        """List secrets in AWS Secrets Manager"""
        client = self._get_client()
        if not client:
            return []

        try:
            secrets = []
            paginator = client.get_paginator('list_secrets')

            for page in paginator.paginate():
                for secret in page['SecretList']:
                    name = secret['Name']
                    if not prefix or name.startswith(prefix):
                        secrets.append(name)

            return secrets

        except Exception as e:
            self.logger.error(f"Failed to list secrets from AWS Secrets Manager: {e}")
            return []

    async def rotate_secret(self, key: str) -> bool:
        """Rotate a secret in AWS Secrets Manager"""
        client = self._get_client()
        if not client:
            return False

        try:
            client.rotate_secret(SecretId=key)
            return True

        except Exception as e:
            self.logger.error(f"Failed to rotate secret {key}: {e}")
            return False

    async def health_check(self) -> bool:
        """Check AWS Secrets Manager health"""
        client = self._get_client()
        if not client:
            return False

        try:
            # Simple health check - try to list secrets with limit 1
            client.list_secrets(MaxResults=1)
            return True

        except Exception as e:
            self.logger.error(f"AWS Secrets Manager health check failed: {e}")
            return False


class CompositeConfigProvider(IConfigurationProvider):
    """Configuration provider that combines multiple providers with priority"""

    def __init__(self, providers: list):
        self.providers = providers  # First provider has highest priority

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value from providers in priority order"""
        for provider in self.providers:
            value = provider.get(key, None)
            if value is not None:
                return value
        return default

    def set(self, key: str, value: Any) -> None:
        """Set configuration value in the first provider"""
        if self.providers:
            self.providers[0].set(key, value)

    def reload(self) -> None:
        """Reload all providers"""
        for provider in self.providers:
            provider.reload()

    def add_provider(self, provider: IConfigurationProvider, priority: int = -1) -> None:
        """Add a configuration provider"""
        if priority < 0 or priority >= len(self.providers):
            self.providers.append(provider)
        else:
            self.providers.insert(priority, provider)

    def remove_provider(self, provider: IConfigurationProvider) -> None:
        """Remove a configuration provider"""
        if provider in self.providers:
            self.providers.remove(provider)