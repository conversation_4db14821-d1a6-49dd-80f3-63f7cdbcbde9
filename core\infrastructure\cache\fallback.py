"""
Redis Fallback Manager

This module provides comprehensive fallback mechanisms for Redis operations
including connection pooling, rate limiting, queue management, and graceful
degradation when Redis limits are exceeded.

Migrated from: core.infrastructure.redis_fallback_manager
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional, List, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import json
import weakref

logger = logging.getLogger(__name__)


class FallbackStrategy(Enum):
    """Fallback strategies when Red<PERSON> is unavailable or limited"""
    MEMORY_CACHE = "memory_cache"
    QUEUE_AND_RETRY = "queue_and_retry"
    FAIL_GRACEFULLY = "fail_gracefully"
    HYBRID = "hybrid"


@dataclass
class RedisLimits:
    """Redis free tier limits configuration"""
    max_connections: int = 25  # Stay under 30 limit
    max_ops_per_second: int = 80  # Stay under 100 limit
    max_memory_mb: int = 28  # Stay under 30 MB limit
    connection_timeout: float = 5.0
    operation_timeout: float = 3.0
    retry_attempts: int = 3
    retry_delay: float = 0.5


@dataclass
class OperationMetrics:
    """Track Redis operation metrics"""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    fallback_operations: int = 0
    queue_size: int = 0
    current_connections: int = 0
    last_reset_time: float = field(default_factory=time.time)

    def reset_if_needed(self):
        """Reset counters every second for rate limiting"""
        current_time = time.time()
        if current_time - self.last_reset_time >= 1.0:
            self.total_operations = 0
            self.successful_operations = 0
            self.failed_operations = 0
            self.last_reset_time = current_time


class RedisFallbackManager:
    """
    Manages Redis operations with fallback mechanisms for free tier limits
    """

    def __init__(
        self,
        redis_url: str,
        limits: Optional[RedisLimits] = None,
        fallback_strategy: FallbackStrategy = FallbackStrategy.HYBRID,
        enable_memory_fallback: bool = True,
        enable_queue_system: bool = True
    ):
        self.redis_url = redis_url
        self.limits = limits or RedisLimits()
        self.fallback_strategy = fallback_strategy
        self.enable_memory_fallback = enable_memory_fallback
        self.enable_queue_system = enable_queue_system

        # Redis connection management
        self._redis_pool = None
        self._connection_semaphore = asyncio.Semaphore(self.limits.max_connections)
        self._rate_limiter = asyncio.Semaphore(self.limits.max_ops_per_second)

        # Fallback systems
        self._memory_cache: Dict[str, Any] = {}
        self._memory_cache_ttl: Dict[str, float] = {}
        self._operation_queue: deque = deque()
        self._queue_processor_task: Optional[asyncio.Task] = None

        # Metrics and monitoring
        self.metrics = OperationMetrics()
        self._health_status = True
        self._last_health_check = time.time()

        # Cleanup tracking
        self._cleanup_refs = weakref.WeakSet()

    async def initialize(self):
        """Initialize Redis connection pool and background tasks"""
        try:
            import redis.asyncio as aioredis

            # Create connection pool with limits
            self._redis_pool = aioredis.ConnectionPool.from_url(
                self.redis_url,
                max_connections=self.limits.max_connections,
                socket_timeout=self.limits.operation_timeout,
                socket_connect_timeout=self.limits.connection_timeout,
                retry_on_timeout=True,
                health_check_interval=30
            )

            # Test connection
            redis_client = aioredis.Redis(connection_pool=self._redis_pool)
            await redis_client.ping()
            await redis_client.close()

            # Start background tasks
            if self.enable_queue_system:
                self._queue_processor_task = asyncio.create_task(self._process_queue())

            logger.info("Redis fallback manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            self._health_status = False
            if self.fallback_strategy == FallbackStrategy.FAIL_GRACEFULLY:
                raise

    async def execute_operation(
        self,
        operation: str,
        *args,
        fallback_value: Any = None,
        ttl: Optional[int] = None,
        priority: int = 0,
        **kwargs
    ) -> Any:
        """
        Execute Redis operation with fallback mechanisms
        """
        self.metrics.reset_if_needed()
        self.metrics.total_operations += 1

        # Check if we should use fallback immediately
        if not self._should_attempt_redis():
            return await self._handle_fallback(operation, args, fallback_value, ttl)

        # Try Redis operation with rate limiting
        try:
            async with self._rate_limiter:
                async with self._connection_semaphore:
                    result = await self._execute_redis_operation(operation, *args, **kwargs)
                    self.metrics.successful_operations += 1
                    self._health_status = True
                    return result

        except asyncio.TimeoutError:
            logger.warning(f"Redis operation {operation} timed out")
            self.metrics.failed_operations += 1
            return await self._handle_fallback(operation, args, fallback_value, ttl)

        except Exception as e:
            logger.warning(f"Redis operation {operation} failed: {e}")
            self.metrics.failed_operations += 1
            self._health_status = False

            # Handle specific Redis errors
            if "max number of clients reached" in str(e).lower():
                logger.warning("Redis connection limit reached, using fallback")
                return await self._handle_fallback(operation, args, fallback_value, ttl)

            if self.enable_queue_system and priority > 0:
                # Queue high-priority operations for retry
                await self._queue_operation(operation, args, kwargs, priority, ttl)
                return fallback_value

            return await self._handle_fallback(operation, args, fallback_value, ttl)

    async def _execute_redis_operation(self, operation: str, *args, **kwargs) -> Any:
        """Execute the actual Redis operation"""
        if not self._redis_pool:
            raise ConnectionError("Redis pool not initialized")

        import redis.asyncio as aioredis
        redis_client = aioredis.Redis(connection_pool=self._redis_pool)

        try:
            # Get the operation method
            op_method = getattr(redis_client, operation)
            if not op_method:
                raise AttributeError(f"Redis operation '{operation}' not found")

            # Execute with timeout
            result = await asyncio.wait_for(
                op_method(*args, **kwargs),
                timeout=self.limits.operation_timeout
            )

            return result

        finally:
            await redis_client.close()

    def _should_attempt_redis(self) -> bool:
        """Determine if we should attempt Redis operation or use fallback"""
        # Check health status
        if not self._health_status:
            # Try to recover after some time
            if time.time() - self._last_health_check > 30:
                self._health_status = True
                self._last_health_check = time.time()
            else:
                return False

        # Check rate limits
        if self.metrics.total_operations >= self.limits.max_ops_per_second:
            return False

        # Check connection availability
        if self._connection_semaphore.locked():
            return False

        return True

    async def _handle_fallback(
        self,
        operation: str,
        args: tuple,
        fallback_value: Any,
        ttl: Optional[int]
    ) -> Any:
        """Handle fallback when Redis is unavailable"""
        self.metrics.fallback_operations += 1

        if not self.enable_memory_fallback:
            return fallback_value

        # Memory cache fallback for common operations
        if operation == "get" and len(args) >= 1:
            key = str(args[0])
            return self._memory_cache_get(key)

        elif operation == "set" and len(args) >= 2:
            key, value = str(args[0]), args[1]
            self._memory_cache_set(key, value, ttl)
            return True

        elif operation == "delete" and len(args) >= 1:
            key = str(args[0])
            return self._memory_cache_delete(key)

        elif operation == "exists" and len(args) >= 1:
            key = str(args[0])
            return self._memory_cache_exists(key)

        return fallback_value

    def _memory_cache_get(self, key: str) -> Any:
        """Get from memory cache"""
        if key in self._memory_cache:
            # Check TTL
            if key in self._memory_cache_ttl:
                if time.time() > self._memory_cache_ttl[key]:
                    del self._memory_cache[key]
                    del self._memory_cache_ttl[key]
                    return None
            return self._memory_cache[key]
        return None

    def _memory_cache_set(self, key: str, value: Any, ttl: Optional[int]) -> None:
        """Set in memory cache"""
        self._memory_cache[key] = value
        if ttl:
            self._memory_cache_ttl[key] = time.time() + ttl

    def _memory_cache_delete(self, key: str) -> bool:
        """Delete from memory cache"""
        existed = key in self._memory_cache
        self._memory_cache.pop(key, None)
        self._memory_cache_ttl.pop(key, None)
        return existed

    def _memory_cache_exists(self, key: str) -> bool:
        """Check if key exists in memory cache"""
        if key in self._memory_cache:
            # Check TTL
            if key in self._memory_cache_ttl:
                if time.time() > self._memory_cache_ttl[key]:
                    del self._memory_cache[key]
                    del self._memory_cache_ttl[key]
                    return False
            return True
        return False

    async def _queue_operation(self, operation: str, args: tuple, kwargs: dict, priority: int, ttl: Optional[int]) -> None:
        """Queue operation for retry"""
        # Simple queue implementation - just log for now
        logger.info(f"Queuing operation {operation} with priority {priority}")

    async def _process_queue(self) -> None:
        """Process queued operations"""
        # Simple queue processor - just log for now
        while True:
            await asyncio.sleep(1)

    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status"""
        return {
            "redis_healthy": self._health_status,
            "total_operations": self.metrics.total_operations,
            "successful_operations": self.metrics.successful_operations,
            "failed_operations": self.metrics.failed_operations,
            "fallback_operations": self.metrics.fallback_operations,
            "memory_cache_size": len(self._memory_cache)
        }

    async def dispose(self) -> None:
        """Dispose resources"""
        if self._queue_processor_task:
            self._queue_processor_task.cancel()
        if self._redis_pool:
            await self._redis_pool.disconnect()


__all__ = [
    "FallbackStrategy",
    "RedisLimits",
    "OperationMetrics",
    "RedisFallbackManager",
]
