"""
Supabase Real-time Service

This module provides real-time capabilities using Supabase Realtime:
- Live database changes
- Real-time notifications
- User presence tracking
- Live metrics and monitoring
- Collaborative features

Migrated from: core.infrastructure.supabase_realtime
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Callable, Set
from datetime import datetime, timezone
from enum import Enum

from .client import UnifiedSupabaseClient

logger = logging.getLogger(__name__)


class RealtimeEventType(str, Enum):
    """Real-time event types"""
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    ALL = "*"


class PresenceState(str, Enum):
    """User presence states"""
    ONLINE = "online"
    AWAY = "away"
    OFFLINE = "offline"


class SupabaseRealtimeService:
    """
    Comprehensive real-time service using Supabase Realtime

    Features:
    - Live database changes
    - User presence tracking
    - Real-time notifications
    - Live metrics updates
    - Collaborative editing support
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client
        self._subscriptions: Dict[str, Any] = {}
        self._presence_channels: Dict[str, Any] = {}
        self._event_handlers: Dict[str, List[Callable]] = {}
        self._user_presence: Dict[str, Dict[str, Any]] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize real-time service"""
        if self._initialized:
            return

        # Check if realtime is enabled (fallback to True if setting doesn't exist)
        realtime_enabled = getattr(self.supabase.settings, 'supabase_realtime_enabled', True)
        if not realtime_enabled:
            logger.warning("Supabase Realtime is disabled")
            return

        try:
            # Initialize real-time connection
            await self.supabase.initialize()
            self._initialized = True
            logger.info("Supabase Realtime service initialized")

        except Exception as e:
            logger.error(f"Failed to initialize Realtime service: {e}")
            raise

    # Database Change Subscriptions
    async def subscribe_to_table_changes(
        self,
        table_name: str,
        callback: Callable[[Dict[str, Any]], None],
        event_type: RealtimeEventType = RealtimeEventType.ALL,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> str:
        """Subscribe to real-time table changes"""
        if not self._initialized:
            await self.initialize()

        subscription_id = f"{table_name}:{event_type.value}:{id(callback)}"

        try:
            # Create enhanced callback that includes metadata
            def enhanced_callback(payload):
                enhanced_payload = {
                    "table": table_name,
                    "event_type": payload.get("eventType"),
                    "new": payload.get("new"),
                    "old": payload.get("old"),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "subscription_id": subscription_id
                }

                # Apply filters if specified
                if filter_conditions and not self._matches_filter(enhanced_payload, filter_conditions):
                    return

                callback(enhanced_payload)

            # Subscribe to table changes
            subscription = self.supabase.subscribe_to_table(
                table_name=table_name,
                callback=enhanced_callback,
                event=event_type.value
            )

            self._subscriptions[subscription_id] = subscription

            # Store event handler
            if subscription_id not in self._event_handlers:
                self._event_handlers[subscription_id] = []
            self._event_handlers[subscription_id].append(callback)

            logger.info(f"Subscribed to {table_name} changes: {subscription_id}")
            return subscription_id

        except Exception as e:
            logger.error(f"Failed to subscribe to {table_name} changes: {e}")
            raise

    async def unsubscribe_from_table_changes(self, subscription_id: str) -> bool:
        """Unsubscribe from table changes"""
        try:
            if subscription_id in self._subscriptions:
                # Extract table name and event type from subscription_id
                parts = subscription_id.split(":", 2)
                if len(parts) >= 2:
                    table_name, event_type = parts[0], parts[1]
                    self.supabase.unsubscribe_from_table(table_name, event_type)

                del self._subscriptions[subscription_id]

                if subscription_id in self._event_handlers:
                    del self._event_handlers[subscription_id]

                logger.info(f"Unsubscribed from changes: {subscription_id}")
                return True

        except Exception as e:
            logger.error(f"Failed to unsubscribe from {subscription_id}: {e}")

        return False

    # User Presence Tracking
    async def track_user_presence(
        self,
        user_id: str,
        channel_name: str = "global",
        initial_state: PresenceState = PresenceState.ONLINE,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track user presence in a channel"""
        if not self._initialized:
            await self.initialize()

        presence_id = f"presence:{channel_name}:{user_id}"

        try:
            # Create presence data
            presence_data = {
                "user_id": user_id,
                "state": initial_state.value,
                "last_seen": datetime.now(timezone.utc).isoformat(),
                "metadata": metadata or {}
            }

            # Join presence channel
            channel = self.supabase._realtime_client.channel(f"presence:{channel_name}")

            # Track presence
            channel.on_presence_sync(self._handle_presence_sync)
            channel.on_presence_join(self._handle_presence_join)
            channel.on_presence_leave(self._handle_presence_leave)

            # Subscribe and track
            subscription = channel.subscribe()
            channel.track(presence_data)

            self._presence_channels[presence_id] = {
                "channel": channel,
                "subscription": subscription,
                "user_id": user_id,
                "channel_name": channel_name
            }

            logger.info(f"Tracking presence for user {user_id} in {channel_name}")
            return presence_id

        except Exception as e:
            logger.error(f"Failed to track presence for user {user_id}: {e}")
            raise

    async def update_user_presence(
        self,
        presence_id: str,
        state: PresenceState,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update user presence state"""
        try:
            if presence_id not in self._presence_channels:
                return False

            presence_info = self._presence_channels[presence_id]
            channel = presence_info["channel"]

            # Update presence data
            presence_data = {
                "user_id": presence_info["user_id"],
                "state": state.value,
                "last_seen": datetime.now(timezone.utc).isoformat(),
                "metadata": metadata or {}
            }

            channel.track(presence_data)
            logger.info(f"Updated presence: {presence_id} to {state.value}")
            return True

        except Exception as e:
            logger.error(f"Failed to update presence {presence_id}: {e}")
            return False

    async def stop_tracking_presence(self, presence_id: str) -> bool:
        """Stop tracking user presence"""
        try:
            if presence_id not in self._presence_channels:
                return False

            presence_info = self._presence_channels[presence_id]
            channel = presence_info["channel"]
            subscription = presence_info["subscription"]

            # Untrack and unsubscribe
            channel.untrack()
            subscription.unsubscribe()

            del self._presence_channels[presence_id]
            logger.info(f"Stopped tracking presence: {presence_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to stop tracking presence {presence_id}: {e}")
            return False

    async def get_channel_presence(self, channel_name: str) -> List[Dict[str, Any]]:
        """Get all users present in a channel"""
        try:
            # Find presence channel
            presence_id = f"presence:{channel_name}"
            matching_channels = [
                info for pid, info in self._presence_channels.items()
                if info["channel_name"] == channel_name
            ]

            if not matching_channels:
                return []

            # Get presence state from first matching channel
            channel = matching_channels[0]["channel"]
            presence_state = channel.presence_state()

            # Convert to list format
            users = []
            for user_id, presence_list in presence_state.items():
                if presence_list:
                    latest_presence = presence_list[-1]  # Get latest presence
                    users.append({
                        "user_id": user_id,
                        "state": latest_presence.get("state"),
                        "last_seen": latest_presence.get("last_seen"),
                        "metadata": latest_presence.get("metadata", {})
                    })

            return users

        except Exception as e:
            logger.error(f"Failed to get presence for channel {channel_name}: {e}")
            return []

    # Real-time Notifications
    async def send_notification(
        self,
        channel_name: str,
        notification_type: str,
        data: Dict[str, Any],
        target_users: Optional[List[str]] = None
    ) -> bool:
        """Send real-time notification to channel"""
        try:
            notification_payload = {
                "type": notification_type,
                "data": data,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "target_users": target_users
            }

            # Send via broadcast
            channel = self.supabase._realtime_client.channel(f"notifications:{channel_name}")
            channel.send("notification", notification_payload)

            logger.info(f"Sent notification to {channel_name}: {notification_type}")
            return True

        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return False

    async def subscribe_to_notifications(
        self,
        channel_name: str,
        callback: Callable[[Dict[str, Any]], None],
        user_id: Optional[str] = None
    ) -> str:
        """Subscribe to real-time notifications"""
        subscription_id = f"notifications:{channel_name}:{id(callback)}"

        try:
            def notification_callback(payload):
                # Filter by target users if specified
                target_users = payload.get("target_users")
                if target_users and user_id and user_id not in target_users:
                    return

                callback(payload)

            channel = self.supabase._realtime_client.channel(f"notifications:{channel_name}")
            channel.on("notification", notification_callback)
            subscription = channel.subscribe()

            self._subscriptions[subscription_id] = subscription
            logger.info(f"Subscribed to notifications: {subscription_id}")
            return subscription_id

        except Exception as e:
            logger.error(f"Failed to subscribe to notifications: {e}")
            raise

    # Live Metrics
    async def broadcast_metrics(self, metrics_data: Dict[str, Any]) -> bool:
        """Broadcast live metrics data"""
        try:
            metrics_payload = {
                "metrics": metrics_data,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            channel = self.supabase._realtime_client.channel("live_metrics")
            channel.send("metrics_update", metrics_payload)

            return True

        except Exception as e:
            logger.error(f"Failed to broadcast metrics: {e}")
            return False

    async def subscribe_to_live_metrics(
        self,
        callback: Callable[[Dict[str, Any]], None]
    ) -> str:
        """Subscribe to live metrics updates"""
        subscription_id = f"live_metrics:{id(callback)}"

        try:
            channel = self.supabase._realtime_client.channel("live_metrics")
            channel.on("metrics_update", callback)
            subscription = channel.subscribe()

            self._subscriptions[subscription_id] = subscription
            logger.info(f"Subscribed to live metrics: {subscription_id}")
            return subscription_id

        except Exception as e:
            logger.error(f"Failed to subscribe to live metrics: {e}")
            raise

    # Helper Methods
    def _matches_filter(self, payload: Dict[str, Any], filter_conditions: Dict[str, Any]) -> bool:
        """Check if payload matches filter conditions"""
        try:
            new_record = payload.get("new", {})
            old_record = payload.get("old", {})

            for field, expected_value in filter_conditions.items():
                # Check new record first, then old record
                actual_value = new_record.get(field) or old_record.get(field)
                if actual_value != expected_value:
                    return False

            return True

        except Exception:
            return False

    def _handle_presence_sync(self, payload):
        """Handle presence sync events"""
        logger.debug(f"Presence sync: {payload}")

    def _handle_presence_join(self, payload):
        """Handle user joining presence"""
        logger.info(f"User joined presence: {payload}")

    def _handle_presence_leave(self, payload):
        """Handle user leaving presence"""
        logger.info(f"User left presence: {payload}")

    # Status and Cleanup
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get information about active subscriptions"""
        return {
            "total_subscriptions": len(self._subscriptions),
            "presence_channels": len(self._presence_channels),
            "event_handlers": len(self._event_handlers),
            "subscriptions": list(self._subscriptions.keys()),
            "presence_channels": list(self._presence_channels.keys())
        }

    async def dispose(self) -> None:
        """Clean up all real-time resources"""
        # Unsubscribe from all subscriptions
        for subscription_id in list(self._subscriptions.keys()):
            await self.unsubscribe_from_table_changes(subscription_id)

        # Stop all presence tracking
        for presence_id in list(self._presence_channels.keys()):
            await self.stop_tracking_presence(presence_id)

        self._subscriptions.clear()
        self._presence_channels.clear()
        self._event_handlers.clear()
        self._user_presence.clear()
        self._initialized = False

        logger.info("Supabase Realtime service disposed")
