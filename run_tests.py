#!/usr/bin/env python3
"""
Test runner script with convenient commands
"""
import subprocess
import sys
from pathlib import Path


def run_command(cmd):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    if len(sys.argv) < 2:
        print("Usage: python run_tests.py <command>")
        print("Commands:")
        print("  all          - Run all tests")
        print("  core         - Run core tests only")
        print("  infrastructure - Run infrastructure tests")
        print("  application  - Run application tests")
        print("  services     - Run service tests")
        print("  system       - Run system tests")
        print("  coverage     - Run tests with coverage report")
        print("  fast         - Run fast tests only")
        return
    
    command = sys.argv[1]
    
    if command == "all":
        run_command(["pytest"])
    elif command == "core":
        run_command(["pytest", "core/tests/"])
    elif command == "infrastructure":
        run_command(["pytest", "core/infrastructure/tests/"])
    elif command == "application":
        run_command(["pytest", "core/application/tests/"])
    elif command == "services":
        run_command(["pytest", "services/"])
    elif command == "system":
        run_command(["pytest", "tests/system/"])
    elif command == "coverage":
        run_command(["pytest", "--cov-report=html", "--cov-report=term"])
    elif command == "fast":
        run_command(["pytest", "-m", "not slow"])
    else:
        print(f"Unknown command: {command}")


if __name__ == "__main__":
    main()
