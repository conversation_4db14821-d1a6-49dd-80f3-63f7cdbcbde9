#!/usr/bin/env python3
"""
Cloudflare Workers KV Integration Tests

This module contains integration tests for the CloudflareKVSecretProvider implementation.
"""

import asyncio
import os
import pytest
from pathlib import Path

from core.config.secret_providers import SecretConfigProvider, CloudflareKVSecretProvider


@pytest.mark.integration
@pytest.mark.asyncio
async def test_cloudflare_kv_auto_detection():
    """Test Cloudflare Workers KV auto-detection"""
    # Check credentials
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')

    if not account_id or not api_token:
        pytest.skip("Missing Cloudflare credentials")

    # Test auto-detection (should use KV)
    provider = SecretConfigProvider(provider_type="auto")
    info = provider.get_provider_info()

    assert info['provider_type'] is not None
    assert info['provider_class'] is not None
    
    # If KV is available, it should be preferred
    if info['provider_class'] == 'CloudflareKVSecretProvider':
        assert info['provider_type'] == 'cloudflare_kv'


@pytest.mark.integration
@pytest.mark.asyncio
async def test_cloudflare_kv_direct():
    """Test direct KV provider functionality"""
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')

    if not account_id or not api_token:
        pytest.skip("Missing Cloudflare credentials")

    kv_provider = CloudflareKVSecretProvider(
        account_id=account_id,
        api_token=api_token,
        namespace_name="secrets-test"
    )

    # Test health check
    healthy = await kv_provider.health_check()
    assert healthy is True


@pytest.mark.integration
@pytest.mark.asyncio
async def test_kv_operations():
    """Test basic KV operations"""
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')

    if not account_id or not api_token:
        pytest.skip("Missing Cloudflare credentials")

    provider = CloudflareKVSecretProvider(
        account_id=account_id,
        api_token=api_token,
        namespace_name="secrets-test"
    )

    test_key = "test-secret-kv"
    test_value = "test-value-123-kv"

    try:
        # Test set secret
        success = await provider.set_secret(test_key, test_value)
        assert success is True

        # Test get secret
        secret = await provider.get_secret(test_key)
        assert secret is not None
        assert secret.value == test_value

        # Test list secrets
        secrets = await provider.list_secrets()
        assert isinstance(secrets, list)
        assert test_key in secrets

    finally:
        # Clean up
        await provider.delete_secret(test_key)


@pytest.mark.integration
@pytest.mark.asyncio
async def test_migration_scenario():
    """Test migration from environment variables to KV"""
    # Simulate migrating existing secrets
    env_secrets = {
        'DATABASE_PASSWORD': os.getenv('DATABASE_PASSWORD'),
        'JWT_SECRET_KEY': os.getenv('JWT_SECRET_KEY'),
        'API_KEY': os.getenv('API_KEY')
    }

    available_secrets = {k: v for k, v in env_secrets.items() if v}

    if not available_secrets:
        pytest.skip("No environment secrets to test migration")

    provider = SecretConfigProvider(provider_type="auto")

    # Test that we can still get secrets (should fall back to env)
    for key, expected_value in available_secrets.items():
        # Try normalized key format
        secret = await provider.get_secret(key.lower().replace('_', '-'))
        if not secret:
            # Try original key format
            secret = await provider.get_secret(key)
        
        # At least one format should work
        assert secret is not None, f"Could not access {key} via provider"


def main():
    """Main test function for manual execution"""
    async def run_tests():
        print("🚀 Cloudflare Workers KV Secret Provider Test")
        print("This test will verify the new KV-based secret storage.\n")

        # Check credentials
        account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
        api_token = os.getenv('CLOUDFLARE_API_TOKEN')

        if not account_id or not api_token:
            print("❌ Missing Cloudflare credentials")
            return False

        print(f"✅ Account ID: {account_id[:8]}...{account_id[-8:]}")
        print(f"✅ API Token: {api_token[:8]}...{api_token[-8:]}")

        try:
            # Run tests
            await test_cloudflare_kv_auto_detection()
            print("✅ Auto-detection test passed")
            
            await test_cloudflare_kv_direct()
            print("✅ Direct KV test passed")
            
            await test_kv_operations()
            print("✅ KV operations test passed")
            
            await test_migration_scenario()
            print("✅ Migration scenario test passed")

            print("\n🎉 SUCCESS! Cloudflare Workers KV integration is working!")
            print("\n📝 Benefits of KV over Secrets Store:")
            print("   ✅ Available now (no beta access needed)")
            print("   ✅ Global edge distribution")
            print("   ✅ Fast access (sub-millisecond)")
            print("   ✅ Encrypted at rest")
            print("   ✅ Easy to use and manage")

            print("\n🔧 Usage in your FastAPI app:")
            print("   provider = SecretConfigProvider()  # Auto-detects KV")
            print("   secret = await provider.get_secret('database-password')")

            return True

        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False

    return asyncio.run(run_tests())


if __name__ == "__main__":
    main()
