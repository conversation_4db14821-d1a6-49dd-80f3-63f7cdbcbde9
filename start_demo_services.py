#!/usr/bin/env python3
"""
Demo Service Startup Script

This script demonstrates how to start your core system and services for testing.
It simulates your real 5-10 services deployment.
"""

import asyncio
import subprocess
import time
import sys
import signal
import os
from pathlib import Path


class ServiceManager:
    """Manages demo services for testing"""
    
    def __init__(self):
        self.processes = []
        self.running = True
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\n🛑 Shutting down services...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def start_service(self, name: str, command: list, port: int):
        """Start a service process"""
        print(f"🚀 Starting {name} on port {port}...")
        try:
            # Set environment variables
            env = os.environ.copy()
            env['PORT'] = str(port)
            env['SERVICE_NAME'] = name
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                text=True
            )
            
            self.processes.append({
                'name': name,
                'process': process,
                'port': port,
                'command': command
            })
            
            print(f"   ✅ {name} started (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"   ❌ Failed to start {name}: {e}")
            return None
    
    def stop_all_services(self):
        """Stop all running services"""
        for service in self.processes:
            try:
                print(f"🛑 Stopping {service['name']}...")
                service['process'].terminate()
                service['process'].wait(timeout=5)
                print(f"   ✅ {service['name']} stopped")
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  Force killing {service['name']}...")
                service['process'].kill()
            except Exception as e:
                print(f"   ❌ Error stopping {service['name']}: {e}")
    
    def check_service_health(self, port: int):
        """Check if a service is responding"""
        import httpx
        try:
            response = httpx.get(f"http://localhost:{port}/health", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def wait_for_service(self, name: str, port: int, timeout: int = 30):
        """Wait for a service to become healthy"""
        print(f"⏳ Waiting for {name} to be ready...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.check_service_health(port):
                print(f"   ✅ {name} is ready!")
                return True
            time.sleep(1)
        
        print(f"   ❌ {name} failed to start within {timeout}s")
        return False
    
    def run_demo(self):
        """Run the demo services"""
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🎬 Starting Demo Services")
        print("=" * 50)
        
        # Start core API
        core_process = self.start_service(
            "Core API",
            [sys.executable, "-m", "core.main"],
            8000
        )
        
        if not core_process:
            print("❌ Failed to start core API. Exiting.")
            return
        
        # Wait for core API to be ready
        if not self.wait_for_service("Core API", 8000):
            self.stop_all_services()
            return
        
        # Start translation service (if it exists)
        translation_path = Path("services/translation/main.py")
        if translation_path.exists():
            self.start_service(
                "Translation Service",
                [sys.executable, "services/translation/main.py"],
                8001
            )
            self.wait_for_service("Translation Service", 8001, timeout=15)
        else:
            print("⚠️  Translation service not found, skipping...")
        
        # Start mock bot services for demo
        self.start_mock_services()
        
        print("\n🎉 All services started!")
        print("\nAvailable services:")
        for service in self.processes:
            status = "✅ Running" if service['process'].poll() is None else "❌ Stopped"
            print(f"  - {service['name']}: http://localhost:{service['port']} ({status})")
        
        print("\n📋 Try these commands:")
        print("  python test_cli_with_real_services.py")
        print("  python cli/service_monitor.py status")
        print("  python cli/service_monitor.py list")
        print("  python cli/service_monitor.py watch")
        
        print("\n⏳ Services running... Press Ctrl+C to stop")
        
        # Keep running until interrupted
        try:
            while self.running:
                # Check if any process has died
                for service in self.processes:
                    if service['process'].poll() is not None:
                        print(f"⚠️  {service['name']} has stopped unexpectedly")
                
                time.sleep(5)
        except KeyboardInterrupt:
            pass
        
        self.stop_all_services()
    
    def start_mock_services(self):
        """Start mock services to simulate your 5-10 services"""
        print("\n🤖 Starting mock services...")
        
        # Create simple mock services
        mock_services = [
            ("Notification Bot", 8002),
            ("Monitoring Bot", 8003),
            ("Auth Service", 8004),
            ("Data Processor", 8005),
        ]
        
        for name, port in mock_services:
            self.start_mock_service(name, port)
    
    def start_mock_service(self, name: str, port: int):
        """Start a simple mock service"""
        mock_service_code = f'''
import asyncio
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
import httpx
from datetime import datetime

app = FastAPI(title="{name}", version="1.0.0")

@app.get("/health")
async def health():
    return {{"status": "healthy", "service": "{name}", "timestamp": datetime.now().isoformat()}}

@app.get("/")
async def root():
    return {{"message": "Hello from {name}!", "service": "{name}"}}

@app.on_event("startup")
async def startup():
    # Register with core API
    try:
        async with httpx.AsyncClient() as client:
            registration_data = {{
                "name": "{name.lower().replace(' ', '-')}",
                "url": "http://localhost:{port}",
                "version": "1.0.0",
                "health_endpoint": "/health",
                "metadata": {{
                    "type": "{'bot' if 'Bot' in name else 'service'}",
                    "description": "{name} for demo",
                    "capabilities": ["demo", "testing"]
                }}
            }}
            
            response = await client.post(
                "http://localhost:8000/api/v1/services/register",
                json=registration_data
            )
            
            if response.status_code == 200:
                print(f"✅ {name} registered with core API")
            else:
                print(f"❌ {name} failed to register: {{response.status_code}}")
    except Exception as e:
        print(f"❌ {name} registration error: {{e}}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port={port}, log_level="info")
'''
        
        # Write mock service to temp file
        temp_file = f"temp_mock_{name.lower().replace(' ', '_')}.py"
        with open(temp_file, 'w') as f:
            f.write(mock_service_code)
        
        # Start the mock service
        process = self.start_service(name, [sys.executable, temp_file], port)
        
        if process:
            # Store temp file for cleanup
            for service in self.processes:
                if service['process'] == process:
                    service['temp_file'] = temp_file
                    break


def main():
    """Main entry point"""
    manager = ServiceManager()
    
    try:
        manager.run_demo()
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    finally:
        # Cleanup temp files
        for service in manager.processes:
            temp_file = service.get('temp_file')
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass


if __name__ == "__main__":
    main()
