﻿#!/bin/bash
set -e

echo "🚀 FastAPI Core Framework Setup"
echo "==============================="

# Check dependencies
command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed."; exit 1; }

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Build containers
docker-compose build

echo "✅ Setup complete!"
