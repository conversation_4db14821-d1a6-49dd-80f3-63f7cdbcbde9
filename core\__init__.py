﻿"""
FastAPI Advanced Core Framework

A modular, enterprise-grade FastAPI framework with:
- Dependency Injection (DI) Container
- Plugin Architecture
- Service Layer with Circuit Breaker, Retry, Caching
- Advanced Configuration Management
- Health Monitoring & Metrics
- Security & Middleware Layer
"""

from core.application.factory import ApplicationFactory
from core.config.settings import Settings, create_settings
from core.infrastructure.container import Container
from core.domain.interfaces import IService, IPlugin, IHealthCheck
from core.application.plugins import PluginManager

__version__ = "2.0.0"
__all__ = [
    "ApplicationFactory",
    "Container", 
    "Settings",
    "create_settings",
    "IService",
    "IPlugin", 
    "IHealthCheck",
    "PluginManager",
]
