#!/usr/bin/env python3
"""
Cleanup Old Test Files

This script safely removes old centralized test files after migration to module-level testing.
It preserves the new test structure and only removes the old files.
"""

import os
import shutil
from pathlib import Path
import sys


class TestCleanup:
    """Handles cleanup of old test files after migration"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.old_tests_dir = self.project_root / "tests"
        
        # Files that were migrated and can be safely removed
        self.migrated_files = [
            "test_api.py",
            "test_cli_monitoring.py", 
            "test_deployment_simulation.py",
            "test_integration.py",
            "test_main.py",
            "test_middleware.py",
            "test_monitoring.py",
            "test_real_deployment_scenario.py",
            "test_scoring_system.py",
            "test_security.py",
            "test_service_registry.py",
            "test_services.py",
            "test_system_health.py"
        ]
        
        # Additional files that can be removed
        self.cleanup_files = [
            "test_comprehensive_deployment.py",  # Duplicate/old version
            "test_100_percent_coverage.py",     # Generated test file
            "test_coverage_fixes.py",           # Generated test file
            "test_fixes_example.py",            # Generated test file
            "test_health.py",                   # Duplicate of test_system_health.py
        ]
        
        # Directories to preserve (new structure)
        self.preserve_dirs = [
            "system",
            "performance", 
            "acceptance",
            "integration"
        ]
    
    def analyze_cleanup(self):
        """Analyze what will be cleaned up"""
        print("🔍 Analyzing old test files for cleanup...")
        print("=" * 60)
        
        if not self.old_tests_dir.exists():
            print("❌ No tests directory found")
            return False
        
        # Check migrated files
        migrated_found = []
        for file_name in self.migrated_files:
            file_path = self.old_tests_dir / file_name
            if file_path.exists():
                migrated_found.append(file_name)
        
        # Check cleanup files
        cleanup_found = []
        for file_name in self.cleanup_files:
            file_path = self.old_tests_dir / file_name
            if file_path.exists():
                cleanup_found.append(file_name)
        
        # Check what will be preserved
        preserved = []
        for item in self.old_tests_dir.iterdir():
            if item.is_file():
                if item.name not in self.migrated_files and item.name not in self.cleanup_files:
                    preserved.append(item.name)
            elif item.is_dir():
                if item.name in self.preserve_dirs:
                    preserved.append(f"{item.name}/ (directory)")
        
        print(f"📁 Found {len(migrated_found)} migrated test files:")
        for file_name in migrated_found:
            print(f"   🗑️  {file_name} → Will be removed (migrated to module-level)")
        
        print(f"\n📁 Found {len(cleanup_found)} additional cleanup files:")
        for file_name in cleanup_found:
            print(f"   🗑️  {file_name} → Will be removed (duplicate/generated)")
        
        print(f"\n📁 Will preserve {len(preserved)} items:")
        for item in preserved:
            print(f"   ✅ {item} → Will be kept")
        
        # Check __pycache__
        pycache_dir = self.old_tests_dir / "__pycache__"
        if pycache_dir.exists():
            print(f"\n📁 Found __pycache__ directory:")
            print(f"   🗑️  __pycache__/ → Will be removed")
        
        total_to_remove = len(migrated_found) + len(cleanup_found)
        print(f"\n📊 Summary:")
        print(f"   🗑️  {total_to_remove} files to remove")
        print(f"   ✅ {len(preserved)} items to preserve")
        print(f"   🗑️  1 __pycache__ directory to remove")
        
        return total_to_remove > 0
    
    def backup_old_tests(self):
        """Create a backup of old tests before cleanup"""
        backup_dir = self.project_root / "tests_backup_old"
        
        if backup_dir.exists():
            print(f"⚠️  Backup directory already exists: {backup_dir}")
            response = input("Overwrite existing backup? (y/N): ").lower()
            if response != 'y':
                print("❌ Backup cancelled")
                return False
            shutil.rmtree(backup_dir)
        
        print(f"💾 Creating backup at: {backup_dir}")
        
        # Copy only the files we're about to remove
        backup_dir.mkdir()
        
        files_backed_up = 0
        for file_name in self.migrated_files + self.cleanup_files:
            file_path = self.old_tests_dir / file_name
            if file_path.exists():
                shutil.copy2(file_path, backup_dir / file_name)
                files_backed_up += 1
        
        print(f"   ✅ Backed up {files_backed_up} files")
        return True
    
    def remove_old_files(self):
        """Remove old test files"""
        print("\n🗑️  Removing old test files...")
        
        removed_count = 0
        
        # Remove migrated files
        for file_name in self.migrated_files:
            file_path = self.old_tests_dir / file_name
            if file_path.exists():
                try:
                    file_path.unlink()
                    print(f"   ✅ Removed {file_name}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ Failed to remove {file_name}: {e}")
        
        # Remove cleanup files
        for file_name in self.cleanup_files:
            file_path = self.old_tests_dir / file_name
            if file_path.exists():
                try:
                    file_path.unlink()
                    print(f"   ✅ Removed {file_name}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ Failed to remove {file_name}: {e}")
        
        # Remove __pycache__
        pycache_dir = self.old_tests_dir / "__pycache__"
        if pycache_dir.exists():
            try:
                shutil.rmtree(pycache_dir)
                print(f"   ✅ Removed __pycache__ directory")
            except Exception as e:
                print(f"   ❌ Failed to remove __pycache__: {e}")
        
        print(f"\n📊 Cleanup complete: {removed_count} files removed")
        return removed_count
    
    def verify_new_structure(self):
        """Verify the new test structure is intact"""
        print("\n🔍 Verifying new test structure...")
        
        # Check core test directories
        core_test_paths = [
            "core/tests",
            "core/tests/integration", 
            "core/tests/unit",
            "core/tests/e2e",
            "core/application/tests",
            "core/domain/tests",
            "core/infrastructure/tests",
            "core/presentation/tests"
        ]
        
        missing_paths = []
        for path_str in core_test_paths:
            path = self.project_root / path_str
            if not path.exists():
                missing_paths.append(path_str)
            else:
                print(f"   ✅ {path_str}")
        
        # Check system test directories
        system_test_paths = [
            "tests/system",
            "tests/performance", 
            "tests/acceptance"
        ]
        
        for path_str in system_test_paths:
            path = self.project_root / path_str
            if not path.exists():
                missing_paths.append(path_str)
            else:
                print(f"   ✅ {path_str}")
        
        if missing_paths:
            print(f"\n⚠️  Missing paths:")
            for path in missing_paths:
                print(f"   ❌ {path}")
            return False
        
        print(f"\n✅ New test structure is intact!")
        return True
    
    def cleanup(self, create_backup: bool = True):
        """Perform the complete cleanup"""
        print("🧹 Starting cleanup of old test files...")
        print("=" * 60)
        
        # Analyze what will be cleaned up
        if not self.analyze_cleanup():
            print("❌ No files to cleanup")
            return
        
        # Ask for confirmation
        print(f"\n⚠️  This will remove old test files from {self.old_tests_dir}")
        print("   The new module-level test structure will be preserved.")
        response = input("\nProceed with cleanup? (y/N): ").lower()
        
        if response != 'y':
            print("❌ Cleanup cancelled")
            return
        
        # Create backup if requested
        if create_backup:
            if not self.backup_old_tests():
                return
        
        # Remove old files
        removed_count = self.remove_old_files()
        
        # Verify new structure
        if self.verify_new_structure():
            print(f"\n🎉 Cleanup successful!")
            print(f"   🗑️  Removed {removed_count} old test files")
            print(f"   ✅ New module-level test structure preserved")
            if create_backup:
                print(f"   💾 Backup created at: tests_backup_old/")
            
            print(f"\n📋 Next steps:")
            print(f"   1. Run tests: python run_tests.py all")
            print(f"   2. Check coverage: python run_tests.py coverage")
            print(f"   3. Remove backup when satisfied: rm -rf tests_backup_old/")
        else:
            print(f"\n❌ Cleanup completed but new structure has issues")


def main():
    """Main entry point"""
    cleanup = TestCleanup()
    
    # Check if --no-backup flag is provided
    create_backup = "--no-backup" not in sys.argv
    
    try:
        cleanup.cleanup(create_backup=create_backup)
    except KeyboardInterrupt:
        print("\n❌ Cleanup cancelled by user")
    except Exception as e:
        print(f"\n❌ Cleanup error: {e}")


if __name__ == "__main__":
    main()
