"""
Application Services

This module contains application-level services that orchestrate
business logic and coordinate between different layers of the system.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

from core.domain.interfaces import (
    IService,
    IHealthCheck,
    IMetricsCollector,
    IServiceRegistry,
    ServiceStatus
)

logger = logging.getLogger(__name__)


class ApplicationService(IService):
    """Base application service"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self._name = name
        self._version = version
        self._status = ServiceStatus.UNKNOWN
        self._start_time: Optional[datetime] = None
    
    @property
    def name(self) -> str:
        return self._name
    
    @property
    def version(self) -> str:
        return self._version
    
    async def start(self) -> None:
        """Start the service"""
        logger.info(f"Starting service: {self.name}")
        self._start_time = datetime.now(timezone.utc)
        self._status = ServiceStatus.HEALTHY
        await self.on_start()
    
    async def stop(self) -> None:
        """Stop the service"""
        logger.info(f"Stopping service: {self.name}")
        await self.on_stop()
        self._status = ServiceStatus.UNKNOWN
        self._start_time = None
    
    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        uptime = 0
        if self._start_time:
            uptime = (datetime.now(timezone.utc) - self._start_time).total_seconds()
        
        return {
            "service": self.name,
            "status": self._status.value,
            "version": self.version,
            "uptime": uptime,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def on_start(self) -> None:
        """Override this method to implement service startup logic"""
        pass
    
    async def on_stop(self) -> None:
        """Override this method to implement service shutdown logic"""
        pass


class HealthCheckService(ApplicationService):
    """Service for coordinating health checks across the system"""
    
    def __init__(self, health_checker: IHealthCheck, service_registry: IServiceRegistry):
        super().__init__("HealthCheckService", "1.0.0")
        self.health_checker = health_checker
        self.service_registry = service_registry
        self._monitoring_task: Optional[asyncio.Task] = None
        self._check_interval = 30  # seconds
    
    async def on_start(self) -> None:
        """Start health monitoring"""
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
    
    async def on_stop(self) -> None:
        """Stop health monitoring"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while True:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self._check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(self._check_interval)
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on all services"""
        try:
            # Check core system health
            core_health = await self.health_checker.check_health()
            logger.debug(f"Core health: {core_health['status']}")
            
            # Check registered services
            services = await self.service_registry.list_services()
            for service in services:
                service_name = service.get("name")
                if service_name:
                    # TODO: Implement service-specific health checks
                    logger.debug(f"Checking health of service: {service_name}")
                    
        except Exception as e:
            logger.error(f"Error performing health checks: {e}")
    
    async def get_system_health_summary(self) -> Dict[str, Any]:
        """Get a summary of system health"""
        core_health = await self.health_checker.check_health()
        services = await self.service_registry.list_services()
        
        return {
            "core_status": core_health["status"],
            "total_services": len(services),
            "healthy_services": len([s for s in services if s.get("status") == "healthy"]),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime": core_health.get("uptime", 0)
        }


class MonitoringService(ApplicationService):
    """Service for collecting and aggregating monitoring data"""
    
    def __init__(self, metrics_collector: IMetricsCollector, service_registry: IServiceRegistry):
        super().__init__("MonitoringService", "1.0.0")
        self.metrics_collector = metrics_collector
        self.service_registry = service_registry
        self._collection_task: Optional[asyncio.Task] = None
        self._collection_interval = 60  # seconds
    
    async def on_start(self) -> None:
        """Start metrics collection"""
        self._collection_task = asyncio.create_task(self._collection_loop())
    
    async def on_stop(self) -> None:
        """Stop metrics collection"""
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
    
    async def _collection_loop(self) -> None:
        """Main metrics collection loop"""
        while True:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self._collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(self._collection_interval)
    
    async def _collect_system_metrics(self) -> None:
        """Collect system-wide metrics"""
        try:
            # Collect service count metrics
            services = await self.service_registry.list_services()
            self.metrics_collector.set_gauge("total_services", len(services))
            
            # Collect service status metrics
            healthy_count = len([s for s in services if s.get("status") == "healthy"])
            self.metrics_collector.set_gauge("healthy_services", healthy_count)
            
            # Record collection timestamp
            self.metrics_collector.increment_counter("metrics_collections_total")
            
            logger.debug(f"Collected metrics for {len(services)} services")
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            self.metrics_collector.increment_counter("metrics_collection_errors_total")
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of collected metrics"""
        metrics = await self.metrics_collector.get_metrics()
        
        return {
            "total_counters": len(metrics.get("counters", {})),
            "total_gauges": len(metrics.get("gauges", {})),
            "total_histograms": len(metrics.get("histograms", {})),
            "total_timers": len(metrics.get("timers", {})),
            "timestamp": metrics.get("timestamp"),
            "collection_status": "active" if self._collection_task and not self._collection_task.done() else "inactive"
        }
    
    async def export_metrics(self, format_type: str = "json") -> Dict[str, Any]:
        """Export metrics in specified format"""
        metrics = await self.metrics_collector.get_metrics()
        
        if format_type == "json":
            return metrics
        elif format_type == "prometheus":
            # TODO: Implement Prometheus format export
            return {"error": "Prometheus format not yet implemented"}
        else:
            raise ValueError(f"Unsupported format: {format_type}")


class ServiceOrchestrator:
    """Orchestrates multiple application services"""
    
    def __init__(self):
        self.services: Dict[str, ApplicationService] = {}
        self._startup_order: List[str] = []
    
    def register_service(self, service: ApplicationService, startup_order: int = 0) -> None:
        """Register an application service"""
        self.services[service.name] = service
        
        # Insert in startup order
        inserted = False
        for i, existing_name in enumerate(self._startup_order):
            if startup_order < getattr(self.services[existing_name], '_startup_order', 0):
                self._startup_order.insert(i, service.name)
                inserted = True
                break
        
        if not inserted:
            self._startup_order.append(service.name)
        
        # Store startup order for future reference
        service._startup_order = startup_order
    
    async def start_all(self) -> None:
        """Start all registered services in order"""
        logger.info("Starting all application services...")
        
        for service_name in self._startup_order:
            service = self.services[service_name]
            try:
                await service.start()
                logger.info(f"✅ Started service: {service_name}")
            except Exception as e:
                logger.error(f"❌ Failed to start service {service_name}: {e}")
                raise
        
        logger.info(f"✅ All {len(self.services)} services started")
    
    async def stop_all(self) -> None:
        """Stop all services in reverse order"""
        logger.info("Stopping all application services...")
        
        for service_name in reversed(self._startup_order):
            service = self.services[service_name]
            try:
                await service.stop()
                logger.info(f"✅ Stopped service: {service_name}")
            except Exception as e:
                logger.error(f"❌ Failed to stop service {service_name}: {e}")
        
        logger.info("✅ All services stopped")
    
    async def get_services_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all services"""
        status = {}
        
        for service_name, service in self.services.items():
            try:
                status[service_name] = await service.health_check()
            except Exception as e:
                status[service_name] = {
                    "service": service_name,
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
        
        return status
