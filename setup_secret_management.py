#!/usr/bin/env python3
"""
Secret Management Setup Script

This script helps configure and test secret management providers for the FastAPI Core Framework.
It supports HashiCorp Vault, AWS Secrets Manager, Azure Key Vault, Cloudflare Secrets Store,
and encrypted file-based secrets for development.
"""

import os
import sys
import asyncio
import getpass
from pathlib import Path
from typing import Dict, Any, Optional

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.config.secret_providers import (
    SecretConfigProvider,
    VaultSecretProvider,
    AzureKeyVaultProvider,
    CloudflareSecretsStoreProvider,
    EncryptedFileSecretProvider
)
from core.config.providers import AWSSecretsManagerProvider


def print_header(title: str) -> None:
    """Print a formatted header"""
    print(f"\n{'=' * 60}")
    print(f"  {title}")
    print(f"{'=' * 60}")


def print_section(title: str) -> None:
    """Print a formatted section header"""
    print(f"\n{'-' * 40}")
    print(f"  {title}")
    print(f"{'-' * 40}")


async def test_vault_provider() -> bool:
    """Test HashiCorp Vault provider"""
    print_section("HashiCorp Vault Configuration")
    
    vault_url = input("Enter Vault URL (e.g., https://vault.example.com): ").strip()
    if not vault_url:
        print("❌ Vault URL is required")
        return False
    
    auth_method = input("Authentication method (token/approle) [token]: ").strip() or "token"
    
    if auth_method == "token":
        vault_token = getpass.getpass("Enter Vault token: ")
        provider = VaultSecretProvider(
            vault_url=vault_url,
            vault_token=vault_token
        )
    elif auth_method == "approle":
        role_id = input("Enter Role ID: ").strip()
        secret_id = getpass.getpass("Enter Secret ID: ")
        provider = VaultSecretProvider(
            vault_url=vault_url,
            vault_role_id=role_id,
            vault_secret_id=secret_id
        )
    else:
        print("❌ Invalid authentication method")
        return False
    
    # Test connection
    print("\n🔍 Testing Vault connection...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ Vault connection successful!")
            
            # Test secret operations
            test_key = "test-secret"
            test_value = "test-value-123"
            
            print(f"\n🔍 Testing secret operations with key '{test_key}'...")
            
            # Set secret
            if await provider.set_secret(test_key, test_value):
                print("✅ Secret set successfully")
                
                # Get secret
                secret = await provider.get_secret(test_key)
                if secret and secret.value == test_value:
                    print("✅ Secret retrieved successfully")
                    
                    # Clean up
                    if await provider.delete_secret(test_key):
                        print("✅ Secret deleted successfully")
                    else:
                        print("⚠️  Failed to delete test secret")
                else:
                    print("❌ Failed to retrieve secret or value mismatch")
            else:
                print("❌ Failed to set secret")
            
            return True
        else:
            print("❌ Vault health check failed")
            return False
    except Exception as e:
        print(f"❌ Vault test failed: {e}")
        return False


async def test_aws_provider() -> bool:
    """Test AWS Secrets Manager provider"""
    print_section("AWS Secrets Manager Configuration")
    
    region = input("Enter AWS region [us-east-1]: ").strip() or "us-east-1"
    
    use_credentials = input("Use explicit credentials? (y/n) [n]: ").strip().lower() == 'y'
    
    if use_credentials:
        access_key = input("Enter AWS Access Key ID: ").strip()
        secret_key = getpass.getpass("Enter AWS Secret Access Key: ")
        provider = AWSSecretsManagerProvider(
            region_name=region,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )
    else:
        print("Using default AWS credentials (IAM role, profile, etc.)")
        provider = AWSSecretsManagerProvider(region_name=region)
    
    # Test connection
    print("\n🔍 Testing AWS Secrets Manager connection...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ AWS Secrets Manager connection successful!")
            
            # Test secret operations
            test_key = "test-secret-" + str(os.getpid())
            test_value = "test-value-123"
            
            print(f"\n🔍 Testing secret operations with key '{test_key}'...")
            
            # Set secret
            if await provider.set_secret(test_key, test_value):
                print("✅ Secret set successfully")
                
                # Get secret
                secret = await provider.get_secret(test_key)
                if secret and secret.value == test_value:
                    print("✅ Secret retrieved successfully")
                    
                    # Clean up
                    if await provider.delete_secret(test_key):
                        print("✅ Secret deleted successfully")
                    else:
                        print("⚠️  Failed to delete test secret")
                else:
                    print("❌ Failed to retrieve secret or value mismatch")
            else:
                print("❌ Failed to set secret")
            
            return True
        else:
            print("❌ AWS Secrets Manager health check failed")
            return False
    except Exception as e:
        print(f"❌ AWS test failed: {e}")
        return False


async def test_azure_provider() -> bool:
    """Test Azure Key Vault provider"""
    print_section("Azure Key Vault Configuration")
    
    vault_url = input("Enter Azure Key Vault URL (e.g., https://myvault.vault.azure.net/): ").strip()
    if not vault_url:
        print("❌ Azure Key Vault URL is required")
        return False
    
    print("Using default Azure credentials (managed identity, Azure CLI, etc.)")
    provider = AzureKeyVaultProvider(vault_url=vault_url)
    
    # Test connection
    print("\n🔍 Testing Azure Key Vault connection...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ Azure Key Vault connection successful!")
            
            # Test secret operations
            test_key = "test-secret"
            test_value = "test-value-123"
            
            print(f"\n🔍 Testing secret operations with key '{test_key}'...")
            
            # Set secret
            if await provider.set_secret(test_key, test_value):
                print("✅ Secret set successfully")
                
                # Get secret
                secret = await provider.get_secret(test_key)
                if secret and secret.value == test_value:
                    print("✅ Secret retrieved successfully")
                    
                    # Clean up
                    if await provider.delete_secret(test_key):
                        print("✅ Secret deleted successfully")
                    else:
                        print("⚠️  Failed to delete test secret")
                else:
                    print("❌ Failed to retrieve secret or value mismatch")
            else:
                print("❌ Failed to set secret")
            
            return True
        else:
            print("❌ Azure Key Vault health check failed")
            return False
    except Exception as e:
        print(f"❌ Azure test failed: {e}")
        return False


async def test_cloudflare_provider() -> bool:
    """Test Cloudflare Secrets Store provider"""
    print_section("Cloudflare Secrets Store Configuration")
    
    account_id = input("Enter Cloudflare Account ID: ").strip()
    if not account_id:
        print("❌ Cloudflare Account ID is required")
        return False
    
    api_token = getpass.getpass("Enter Cloudflare API Token: ")
    if not api_token:
        print("❌ Cloudflare API Token is required")
        return False
    
    store_id = input("Enter Secrets Store ID (optional): ").strip() or None
    
    provider = CloudflareSecretsStoreProvider(
        account_id=account_id,
        api_token=api_token,
        store_id=store_id
    )
    
    # Test connection
    print("\n🔍 Testing Cloudflare Secrets Store connection...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ Cloudflare Secrets Store connection successful!")
            
            # Test secret operations
            test_key = "test-secret"
            test_value = "test-value-123"
            
            print(f"\n🔍 Testing secret operations with key '{test_key}'...")
            
            # Set secret
            if await provider.set_secret(test_key, test_value):
                print("✅ Secret set successfully")
                
                # Get secret
                secret = await provider.get_secret(test_key)
                if secret and secret.value == test_value:
                    print("✅ Secret retrieved successfully")
                    
                    # Clean up
                    if await provider.delete_secret(test_key):
                        print("✅ Secret deleted successfully")
                    else:
                        print("⚠️  Failed to delete test secret")
                else:
                    print("❌ Failed to retrieve secret or value mismatch")
            else:
                print("❌ Failed to set secret")
            
            return True
        else:
            print("❌ Cloudflare Secrets Store health check failed")
            return False
    except Exception as e:
        print(f"❌ Cloudflare test failed: {e}")
        return False


async def test_file_provider() -> bool:
    """Test encrypted file provider"""
    print_section("Encrypted File Provider Configuration")
    
    file_path = input("Enter secrets file path [secrets.enc]: ").strip() or "secrets.enc"
    
    provider = EncryptedFileSecretProvider(file_path=file_path)
    
    # Test connection
    print("\n🔍 Testing encrypted file provider...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ Encrypted file provider ready!")
            
            # Test secret operations
            test_key = "test-secret"
            test_value = "test-value-123"
            
            print(f"\n🔍 Testing secret operations with key '{test_key}'...")
            
            # Set secret
            if await provider.set_secret(test_key, test_value):
                print("✅ Secret set successfully")
                
                # Get secret
                secret = await provider.get_secret(test_key)
                if secret and secret.value == test_value:
                    print("✅ Secret retrieved successfully")
                    
                    # Clean up
                    if await provider.delete_secret(test_key):
                        print("✅ Secret deleted successfully")
                    else:
                        print("⚠️  Failed to delete test secret")
                else:
                    print("❌ Failed to retrieve secret or value mismatch")
            else:
                print("❌ Failed to set secret")
            
            return True
        else:
            print("❌ Encrypted file provider health check failed")
            return False
    except Exception as e:
        print(f"❌ File provider test failed: {e}")
        return False


async def test_auto_detection() -> None:
    """Test auto-detection of secret providers"""
    print_section("Auto-Detection Test")
    
    provider = SecretConfigProvider(provider_type="auto")
    info = provider.get_provider_info()
    
    print(f"Provider Type: {info['provider_type']}")
    print(f"Provider Class: {info['provider_class']}")
    print(f"Fallback to Env: {info['fallback_to_env']}")
    print(f"Healthy: {info['healthy']}")
    
    if info['healthy']:
        print("✅ Auto-detected provider is working!")
    else:
        print("⚠️  Auto-detected provider may have issues")


def generate_env_example() -> None:
    """Generate environment variable examples"""
    print_section("Environment Variable Examples")
    
    env_examples = """
# HashiCorp Vault
VAULT_ADDR=https://vault.example.com
VAULT_TOKEN=your-vault-token
# OR for AppRole authentication:
# VAULT_ROLE_ID=your-role-id
# VAULT_SECRET_ID=your-secret-id
VAULT_MOUNT_POINT=secret
VAULT_KV_VERSION=2

# AWS Secrets Manager
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Azure Key Vault
AZURE_KEY_VAULT_URL=https://myvault.vault.azure.net/

# Cloudflare Secrets Store
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_STORE_ID=your-store-id

# Secret Management Configuration
SECRET_PROVIDER=auto  # auto, vault, aws, azure, cloudflare, file
SECRET_CACHE_TTL=300
FALLBACK_TO_ENV=true
SECRETS_FILE_PATH=secrets.enc
"""
    
    print(env_examples)
    
    # Optionally save to file
    save_to_file = input("\nSave to .env.secrets.example? (y/n) [y]: ").strip().lower()
    if save_to_file != 'n':
        with open('.env.secrets.example', 'w') as f:
            f.write(env_examples.strip())
        print("✅ Saved to .env.secrets.example")


async def main():
    """Main setup function"""
    print_header("Secret Management Setup")
    print("This script will help you configure and test secret management providers.")
    
    while True:
        print("\nAvailable options:")
        print("1. Test HashiCorp Vault")
        print("2. Test AWS Secrets Manager")
        print("3. Test Azure Key Vault")
        print("4. Test Cloudflare Secrets Store")
        print("5. Test Encrypted File Provider")
        print("6. Test Auto-Detection")
        print("7. Generate Environment Examples")
        print("8. Exit")
        
        choice = input("\nSelect an option (1-8): ").strip()
        
        if choice == '1':
            await test_vault_provider()
        elif choice == '2':
            await test_aws_provider()
        elif choice == '3':
            await test_azure_provider()
        elif choice == '4':
            await test_cloudflare_provider()
        elif choice == '5':
            await test_file_provider()
        elif choice == '6':
            await test_auto_detection()
        elif choice == '7':
            generate_env_example()
        elif choice == '8':
            print("\n👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please select 1-8.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
