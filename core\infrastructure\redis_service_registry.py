"""
Redis Service Registry Implementation

This module provides a Redis-backed service registry for distributed systems
with TTL, heartbeat, health monitoring, and distributed locking capabilities.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urljoin

import aiohttp
from pydantic import BaseModel

from core.domain.interfaces import IServiceRegistry
from core.domain.exceptions import ServiceRegistryError


class ServiceInfo(BaseModel):
    """Enhanced service information model for Redis storage"""
    name: str
    url: str
    version: str
    health_endpoint: str = "/health"
    api_key: Optional[str] = None
    metadata: Dict[str, Any] = {}
    registered_at: datetime
    last_heartbeat: datetime
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True
    restart_endpoint: str = "/admin/restart"
    ttl: int = 300  # Service TTL in seconds (5 minutes)
    health_check_interval: int = 30  # Health check interval in seconds

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RedisServiceRegistry(IServiceRegistry):
    """Redis-backed service registry with distributed capabilities"""

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "service_registry:",
        default_ttl: int = 300,
        health_check_interval: int = 30,
        lock_timeout: int = 10
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.health_check_interval = health_check_interval
        self.lock_timeout = lock_timeout
        self._redis = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._running = False

    async def _get_redis(self):
        """Get Redis connection with connection pooling"""
        if self._redis is None:
            try:
                import redis.asyncio as aioredis
                self._redis = await aioredis.from_url(
                    self.redis_url,
                    encoding="utf-8",
                    decode_responses=True,
                    max_connections=20
                )
            except ImportError:
                raise ServiceRegistryError("Redis package not installed. Install with: pip install redis")
            except Exception as e:
                raise ServiceRegistryError(f"Failed to connect to Redis: {str(e)}")
        return self._redis

    def _make_service_key(self, service_name: str) -> str:
        """Create Redis key for service"""
        return f"{self.key_prefix}services:{service_name}"

    def _make_lock_key(self, service_name: str) -> str:
        """Create Redis key for service lock"""
        return f"{self.key_prefix}locks:{service_name}"

    def _make_heartbeat_key(self, service_name: str) -> str:
        """Create Redis key for service heartbeat"""
        return f"{self.key_prefix}heartbeats:{service_name}"

    async def _acquire_lock(self, service_name: str, timeout: Optional[int] = None) -> Optional[str]:
        """Acquire distributed lock for service operations"""
        redis = await self._get_redis()
        lock_key = self._make_lock_key(service_name)
        lock_value = str(uuid.uuid4())
        timeout = timeout or self.lock_timeout

        # Try to acquire lock with timeout
        result = await redis.set(lock_key, lock_value, nx=True, ex=timeout)
        return lock_value if result else None

    async def _release_lock(self, service_name: str, lock_value: str) -> bool:
        """Release distributed lock"""
        redis = await self._get_redis()
        lock_key = self._make_lock_key(service_name)

        # Lua script to atomically check and delete lock
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        result = await redis.eval(lua_script, 1, lock_key, lock_value)
        return bool(result)

    async def start_monitoring(self) -> None:
        """Start health monitoring background task"""
        if not self._running:
            self._running = True
            self._health_monitor_task = asyncio.create_task(self._health_monitoring_loop())

    async def stop_monitoring(self) -> None:
        """Stop health monitoring background task"""
        self._running = False
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
            try:
                await self._health_monitor_task
            except asyncio.CancelledError:
                pass

    async def register_service(
        self,
        name: str,
        url: str,
        version: str,
        health_endpoint: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a new service with distributed locking"""
        try:
            # Acquire lock for service registration
            lock_value = await self._acquire_lock(name)
            if not lock_value:
                return {
                    "success": False,
                    "error": f"Could not acquire lock for service '{name}'. Another operation in progress."
                }

            try:
                redis = await self._get_redis()
                service_key = self._make_service_key(name)
                heartbeat_key = self._make_heartbeat_key(name)

                # Generate API key for the service
                api_key = f"api_key_{name}_{int(time.time())}_{uuid.uuid4().hex[:8]}"

                # Create service info
                now = datetime.now(timezone.utc)
                service_info = ServiceInfo(
                    name=name,
                    url=url,
                    version=version,
                    health_endpoint=health_endpoint,
                    api_key=api_key,
                    metadata=metadata or {},
                    registered_at=now,
                    last_heartbeat=now,
                    ttl=self.default_ttl
                )

                # Store service in Redis with TTL
                service_data = service_info.model_dump_json()
                await redis.setex(service_key, self.default_ttl, service_data)

                # Set heartbeat with TTL
                await redis.setex(heartbeat_key, self.default_ttl, now.isoformat())

                return {
                    "success": True,
                    "service": name,
                    "api_key": api_key,
                    "ttl": self.default_ttl,
                    "heartbeat_interval": self.health_check_interval
                }

            finally:
                # Always release the lock
                await self._release_lock(name, lock_value)

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to register service: {str(e)}"
            }

    async def unregister_service(self, service_name: str) -> Dict[str, Any]:
        """Unregister a service with distributed locking"""
        try:
            # Acquire lock for service unregistration
            lock_value = await self._acquire_lock(service_name)
            if not lock_value:
                return {
                    "success": False,
                    "error": f"Could not acquire lock for service '{service_name}'. Another operation in progress."
                }

            try:
                redis = await self._get_redis()
                service_key = self._make_service_key(service_name)
                heartbeat_key = self._make_heartbeat_key(service_name)

                # Check if service exists
                if not await redis.exists(service_key):
                    return {"success": False, "error": "Service not found"}

                # Remove service and heartbeat
                await redis.delete(service_key, heartbeat_key)

                return {"success": True, "service": service_name}

            finally:
                # Always release the lock
                await self._release_lock(service_name, lock_value)

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to unregister service: {str(e)}"
            }

    async def heartbeat(self, service_name: str) -> Dict[str, Any]:
        """Update service heartbeat to extend TTL"""
        try:
            redis = await self._get_redis()
            service_key = self._make_service_key(service_name)
            heartbeat_key = self._make_heartbeat_key(service_name)

            # Check if service exists
            service_data = await redis.get(service_key)
            if not service_data:
                return {"success": False, "error": "Service not registered"}

            # Parse service info and update heartbeat
            service_info = ServiceInfo.model_validate_json(service_data)
            now = datetime.now(timezone.utc)
            service_info.last_heartbeat = now

            # Update service with new heartbeat and extend TTL
            await redis.setex(service_key, service_info.ttl, service_info.model_dump_json())
            await redis.setex(heartbeat_key, service_info.ttl, now.isoformat())

            return {
                "success": True,
                "service": service_name,
                "next_heartbeat": (now + timedelta(seconds=service_info.health_check_interval)).isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to update heartbeat: {str(e)}"
            }

    async def list_services(self, only_healthy: bool = False) -> List[Dict[str, Any]]:
        """List all registered services with optional health filtering"""
        try:
            redis = await self._get_redis()
            pattern = f"{self.key_prefix}services:*"

            # Get all service keys
            service_keys = await redis.keys(pattern)
            services = []

            for service_key in service_keys:
                try:
                    service_data = await redis.get(service_key)
                    if service_data:
                        service_info = ServiceInfo.model_validate_json(service_data)

                        # Filter by health if requested
                        if only_healthy and not service_info.is_healthy:
                            continue

                        services.append({
                            "name": service_info.name,
                            "url": service_info.url,
                            "version": service_info.version,
                            "health_endpoint": service_info.health_endpoint,
                            "metadata": service_info.metadata,
                            "registered_at": service_info.registered_at.isoformat(),
                            "last_heartbeat": service_info.last_heartbeat.isoformat(),
                            "last_health_check": service_info.last_health_check.isoformat() if service_info.last_health_check else None,
                            "is_healthy": service_info.is_healthy,
                            "ttl": service_info.ttl
                        })
                except Exception as e:
                    # Skip invalid service entries
                    continue

            return services

        except Exception as e:
            raise ServiceRegistryError(f"Failed to list services: {str(e)}")

    async def get_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get specific service information"""
        try:
            redis = await self._get_redis()
            service_key = self._make_service_key(service_name)

            service_data = await redis.get(service_key)
            if not service_data:
                return None

            service_info = ServiceInfo.model_validate_json(service_data)
            return {
                "name": service_info.name,
                "url": service_info.url,
                "version": service_info.version,
                "health_endpoint": service_info.health_endpoint,
                "metadata": service_info.metadata,
                "registered_at": service_info.registered_at.isoformat(),
                "last_heartbeat": service_info.last_heartbeat.isoformat(),
                "last_health_check": service_info.last_health_check.isoformat() if service_info.last_health_check else None,
                "is_healthy": service_info.is_healthy,
                "api_key": service_info.api_key,
                "ttl": service_info.ttl
            }

        except Exception as e:
            raise ServiceRegistryError(f"Failed to get service: {str(e)}")

    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Call a registered service with automatic service discovery"""
        service = await self.get_service(service_name)
        if not service or not service.get("is_healthy", False):
            return None

        try:
            # Prepare request
            url = urljoin(service["url"], endpoint.lstrip("/"))
            request_headers = headers or {}

            # Add API key if available
            if service.get("api_key"):
                request_headers["X-API-Key"] = service["api_key"]

            # Make HTTP request
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method.upper(),
                    url=url,
                    json=data,
                    headers=request_headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.content_type == "application/json":
                        return await response.json()
                    else:
                        return {"status": response.status, "text": await response.text()}

        except Exception as e:
            # Mark service as unhealthy on communication failure
            await self._mark_service_unhealthy(service_name, str(e))
            return None

    async def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a registered service"""
        service = await self.get_service(service_name)
        if not service:
            return {"success": False, "error": "Service not found"}

        try:
            # Prepare restart request
            restart_url = urljoin(service["url"], service.get("restart_endpoint", "/admin/restart").lstrip("/"))
            headers = {}

            # Add API key if available
            if service.get("api_key"):
                headers["X-API-Key"] = service["api_key"]

            # Make restart request
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    restart_url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        # Mark service as unhealthy temporarily (it will be checked again by health monitor)
                        await self._update_service_health(service_name, False, "Service restarting")

                        # Get response data
                        response_data = None
                        if response.content_type == "application/json":
                            response_data = await response.json()
                        else:
                            response_data = await response.text()

                        return {
                            "success": True,
                            "service": service_name,
                            "status": "restarting",
                            "response": response_data
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "service": service_name,
                            "status_code": response.status,
                            "error": error_text
                        }

        except Exception as e:
            return {
                "success": False,
                "service": service_name,
                "error": f"Failed to restart service: {str(e)}"
            }

    async def _mark_service_unhealthy(self, service_name: str, error_message: str) -> None:
        """Mark a service as unhealthy"""
        try:
            redis = await self._get_redis()
            service_key = self._make_service_key(service_name)

            service_data = await redis.get(service_key)
            if service_data:
                service_info = ServiceInfo.model_validate_json(service_data)
                service_info.is_healthy = False
                service_info.last_health_check = datetime.now(timezone.utc)
                service_info.metadata["last_error"] = error_message

                # Update service with health status
                await redis.setex(service_key, service_info.ttl, service_info.model_dump_json())

        except Exception:
            # Ignore errors when marking unhealthy to prevent cascading failures
            pass

    async def _health_monitoring_loop(self) -> None:
        """Background task for health monitoring"""
        while self._running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                # Log error but continue monitoring
                print(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(self.health_check_interval)

    async def _perform_health_checks(self) -> None:
        """Perform health checks on all registered services"""
        try:
            services = await self.list_services()

            # Create tasks for parallel health checking
            health_check_tasks = []
            for service in services:
                task = asyncio.create_task(self._check_service_health(service["name"]))
                health_check_tasks.append(task)

            # Wait for all health checks to complete
            if health_check_tasks:
                await asyncio.gather(*health_check_tasks, return_exceptions=True)

        except Exception as e:
            print(f"Error performing health checks: {e}")

    async def _check_service_health(self, service_name: str) -> None:
        """Check health of a specific service"""
        try:
            service = await self.get_service(service_name)
            if not service:
                return

            # Make health check request
            health_url = urljoin(service["url"], service["health_endpoint"].lstrip("/"))

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    health_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    is_healthy = response.status == 200

                    # Update service health status
                    await self._update_service_health(service_name, is_healthy)

        except Exception as e:
            # Mark service as unhealthy on health check failure
            await self._update_service_health(service_name, False, str(e))

    async def _update_service_health(self, service_name: str, is_healthy: bool, error_message: Optional[str] = None) -> None:
        """Update service health status"""
        try:
            redis = await self._get_redis()
            service_key = self._make_service_key(service_name)

            service_data = await redis.get(service_key)
            if service_data:
                service_info = ServiceInfo.model_validate_json(service_data)
                service_info.is_healthy = is_healthy
                service_info.last_health_check = datetime.now(timezone.utc)

                if error_message:
                    service_info.metadata["last_error"] = error_message
                elif "last_error" in service_info.metadata:
                    # Clear error message if service is healthy
                    del service_info.metadata["last_error"]

                # Update service with new health status
                await redis.setex(service_key, service_info.ttl, service_info.model_dump_json())

        except Exception:
            # Ignore errors when updating health to prevent cascading failures
            pass

    async def get_healthy_services(self, service_name_pattern: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all healthy services, optionally filtered by name pattern"""
        services = await self.list_services(only_healthy=True)

        if service_name_pattern:
            import re
            pattern = re.compile(service_name_pattern)
            services = [s for s in services if pattern.match(s["name"])]

        return services

    async def get_service_stats(self) -> Dict[str, Any]:
        """Get service registry statistics"""
        try:
            all_services = await self.list_services()
            healthy_services = await self.list_services(only_healthy=True)

            return {
                "total_services": len(all_services),
                "healthy_services": len(healthy_services),
                "unhealthy_services": len(all_services) - len(healthy_services),
                "health_percentage": (len(healthy_services) / len(all_services) * 100) if all_services else 100,
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            raise ServiceRegistryError(f"Failed to get service stats: {str(e)}")

    async def cleanup_expired_services(self) -> Dict[str, Any]:
        """Manually cleanup expired services (Redis TTL should handle this automatically)"""
        try:
            redis = await self._get_redis()
            pattern = f"{self.key_prefix}services:*"

            service_keys = await redis.keys(pattern)
            expired_count = 0

            for service_key in service_keys:
                ttl = await redis.ttl(service_key)
                if ttl == -2:  # Key doesn't exist
                    expired_count += 1
                elif ttl == -1:  # Key exists but has no TTL
                    # Set TTL for keys without expiration
                    await redis.expire(service_key, self.default_ttl)

            return {
                "success": True,
                "expired_services_cleaned": expired_count,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to cleanup expired services: {str(e)}"
            }

    async def dispose(self) -> None:
        """Cleanup resources"""
        await self.stop_monitoring()
        if self._redis:
            await self._redis.close()
            self._redis = None
