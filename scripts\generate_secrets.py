#!/usr/bin/env python3
"""
Secret Key Generation Script

This script generates various types of cryptographic keys and secrets for the FastAPI Core Framework.
It can generate JWT secrets, API keys, encryption keys, and more, with options to store them
directly in your configured secret management provider.

Usage:
    python scripts/generate_secrets.py --help
    python scripts/generate_secrets.py --type jwt --length 32
    python scripts/generate_secrets.py --type all --store cloudflare
    python scripts/generate_secrets.py --interactive
"""

import os
import sys
import secrets
import string
import base64
import hashlib
import argparse
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from core.config.secret_providers import (
        SecretConfigProvider,
        CloudflareSecretsStoreProvider,
        EncryptedFileSecretProvider,
        SecretMetadata
    )
    SECRET_PROVIDERS_AVAILABLE = True
except ImportError:
    SECRET_PROVIDERS_AVAILABLE = False
    print("⚠️  Secret providers not available. Install dependencies with: pip install -r requirements.txt")


class SecretGenerator:
    """Generate various types of cryptographic secrets"""

    # Character sets for different secret types
    ALPHANUMERIC = string.ascii_letters + string.digits
    ALPHANUMERIC_SYMBOLS = string.ascii_letters + string.digits + "!@#$%^&*"
    HEX_CHARS = string.hexdigits.lower()

    def __init__(self):
        self.generated_secrets: Dict[str, str] = {}

    def generate_jwt_secret(self, length: int = 32) -> str:
        """Generate a JWT secret key (URL-safe base64)"""
        return secrets.token_urlsafe(length)

    def generate_app_secret(self, length: int = 32) -> str:
        """Generate an application secret key (URL-safe base64)"""
        return secrets.token_urlsafe(length)

    def generate_api_key(self, length: int = 32, prefix: str = "") -> str:
        """Generate an API key with optional prefix"""
        key = secrets.token_urlsafe(length)
        return f"{prefix}{key}" if prefix else key

    def generate_encryption_key(self) -> str:
        """Generate a Fernet encryption key (32 bytes, base64 encoded)"""
        try:
            from cryptography.fernet import Fernet
            return Fernet.generate_key().decode('utf-8')
        except ImportError:
            # Fallback: generate 32 random bytes and base64 encode
            key_bytes = secrets.token_bytes(32)
            return base64.urlsafe_b64encode(key_bytes).decode('utf-8')

    def generate_database_password(self, length: int = 16, include_symbols: bool = True) -> str:
        """Generate a secure database password"""
        charset = self.ALPHANUMERIC_SYMBOLS if include_symbols else self.ALPHANUMERIC
        return ''.join(secrets.choice(charset) for _ in range(length))

    def generate_hex_token(self, length: int = 32) -> str:
        """Generate a hexadecimal token"""
        return secrets.token_hex(length)

    def generate_random_string(self, length: int = 16, charset: str = None) -> str:
        """Generate a random string with custom charset"""
        if charset is None:
            charset = self.ALPHANUMERIC
        return ''.join(secrets.choice(charset) for _ in range(length))

    def generate_uuid_like(self) -> str:
        """Generate a UUID-like string"""
        import uuid
        return str(uuid.uuid4())

    def generate_hash_salt(self, length: int = 16) -> str:
        """Generate a salt for password hashing"""
        return secrets.token_hex(length)

    def generate_session_secret(self, length: int = 24) -> str:
        """Generate a session secret"""
        return secrets.token_urlsafe(length)

    def generate_csrf_token(self, length: int = 32) -> str:
        """Generate a CSRF token"""
        return secrets.token_urlsafe(length)

    def generate_webhook_secret(self, length: int = 32) -> str:
        """Generate a webhook signing secret"""
        return secrets.token_urlsafe(length)

    def generate_all_secrets(self) -> Dict[str, str]:
        """Generate a complete set of secrets for the application"""
        secrets_dict = {
            # Core application secrets
            'SECRET_KEY': self.generate_app_secret(32),
            'JWT_SECRET_KEY': self.generate_jwt_secret(32),
            'JWT_REFRESH_SECRET_KEY': self.generate_jwt_secret(32),

            # API keys
            'CORE_API_KEY': self.generate_api_key(32, 'core_'),
            'ADMIN_API_KEY': self.generate_api_key(32, 'admin_'),
            'SERVICE_API_KEY': self.generate_api_key(32, 'svc_'),

            # Database and cache
            'DATABASE_PASSWORD': self.generate_database_password(16),
            'REDIS_PASSWORD': self.generate_database_password(16),

            # Encryption
            'ENCRYPTION_KEY': self.generate_encryption_key(),
            'FILE_ENCRYPTION_KEY': self.generate_encryption_key(),

            # Security tokens
            'SESSION_SECRET': self.generate_session_secret(24),
            'CSRF_SECRET': self.generate_csrf_token(32),
            'WEBHOOK_SECRET': self.generate_webhook_secret(32),

            # Salts and hashes
            'PASSWORD_SALT': self.generate_hash_salt(16),
            'HASH_SALT': self.generate_hash_salt(16),

            # Monitoring and metrics
            'METRICS_TOKEN': self.generate_hex_token(24),
            'HEALTH_CHECK_TOKEN': self.generate_hex_token(16),

            # External service keys (placeholders)
            'OPENAI_API_KEY': 'sk-your-openai-key-here',
            'CLOUDFLARE_API_TOKEN': 'your-cloudflare-token-here',
            'SUPABASE_SERVICE_KEY': 'your-supabase-service-key-here',
        }

        self.generated_secrets.update(secrets_dict)
        return secrets_dict


class SecretFormatter:
    """Format secrets for different output types"""

    @staticmethod
    def format_env_file(secrets: Dict[str, str], include_comments: bool = True) -> str:
        """Format secrets as environment variables file"""
        lines = []

        if include_comments:
            lines.extend([
                "# Generated secrets for FastAPI Core Framework",
                f"# Generated on: {datetime.now().isoformat()}",
                "# WARNING: Keep these secrets secure and never commit to version control!",
                "",
                "# Core Application Secrets",
            ])

        # Group secrets by category
        core_secrets = ['SECRET_KEY', 'JWT_SECRET_KEY', 'JWT_REFRESH_SECRET_KEY']
        api_secrets = [k for k in secrets.keys() if 'API_KEY' in k]
        db_secrets = [k for k in secrets.keys() if any(db in k for db in ['DATABASE', 'REDIS'])]
        encryption_secrets = [k for k in secrets.keys() if 'ENCRYPTION' in k or 'KEY' in k and k not in core_secrets + api_secrets]
        security_secrets = [k for k in secrets.keys() if any(sec in k for sec in ['SESSION', 'CSRF', 'WEBHOOK', 'SALT', 'TOKEN'])]

        categories = [
            ("Core Application", core_secrets),
            ("API Keys", api_secrets),
            ("Database & Cache", db_secrets),
            ("Encryption Keys", encryption_secrets),
            ("Security Tokens", security_secrets),
        ]

        for category_name, category_keys in categories:
            if category_keys and include_comments:
                lines.extend(["", f"# {category_name}"])

            for key in category_keys:
                if key in secrets:
                    lines.append(f"{key}={secrets[key]}")

        # Add remaining secrets
        remaining = set(secrets.keys()) - set(sum([cat[1] for cat in categories], []))
        if remaining:
            if include_comments:
                lines.extend(["", "# Other Secrets"])
            for key in sorted(remaining):
                lines.append(f"{key}={secrets[key]}")

        return "\n".join(lines)

    @staticmethod
    def format_json(secrets: Dict[str, str], pretty: bool = True) -> str:
        """Format secrets as JSON"""
        import json
        if pretty:
            return json.dumps(secrets, indent=2, sort_keys=True)
        return json.dumps(secrets, sort_keys=True)

    @staticmethod
    def format_yaml(secrets: Dict[str, str]) -> str:
        """Format secrets as YAML"""
        lines = ["# Generated secrets for FastAPI Core Framework"]
        for key, value in sorted(secrets.items()):
            lines.append(f"{key}: {value}")
        return "\n".join(lines)

    @staticmethod
    def format_table(secrets: Dict[str, str]) -> str:
        """Format secrets as a readable table"""
        lines = [
            "Generated Secrets",
            "=" * 80,
            f"{'Secret Name':<30} {'Value':<50}",
            "-" * 80,
        ]

        for key, value in sorted(secrets.items()):
            # Truncate long values for display
            display_value = value if len(value) <= 47 else value[:44] + "..."
            lines.append(f"{key:<30} {display_value:<50}")

        lines.append("-" * 80)
        return "\n".join(lines)


async def store_secrets_in_provider(secrets: Dict[str, str], provider_type: str = "auto") -> bool:
    """Store generated secrets in the configured secret provider"""
    if not SECRET_PROVIDERS_AVAILABLE:
        print("❌ Secret providers not available. Cannot store secrets.")
        return False

    try:
        # Initialize the secret provider
        if provider_type == "cloudflare":
            account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
            api_token = os.getenv('CLOUDFLARE_API_TOKEN')

            if not account_id or not api_token:
                print("❌ Cloudflare credentials not found. Set CLOUDFLARE_ACCOUNT_ID and CLOUDFLARE_API_TOKEN")
                return False

            provider = CloudflareSecretsStoreProvider(
                account_id=account_id,
                api_token=api_token
            )
        elif provider_type == "file":
            provider = EncryptedFileSecretProvider()
        else:
            # Auto-detect
            provider = SecretConfigProvider(provider_type="auto")

        # Test provider health
        if not await provider.health_check():
            print(f"❌ Secret provider ({provider_type}) health check failed")
            return False

        print(f"✅ Connected to secret provider: {provider_type}")

        # Store each secret
        success_count = 0
        for key, value in secrets.items():
            metadata = SecretMetadata(
                key=key,
                tags={
                    'generated_by': 'generate_secrets.py',
                    'generated_at': datetime.now().isoformat(),
                    'category': 'application_secret'
                }
            )

            if await provider.set_secret(key, value, metadata):
                print(f"  ✅ Stored: {key}")
                success_count += 1
            else:
                print(f"  ❌ Failed to store: {key}")

        print(f"\n📊 Successfully stored {success_count}/{len(secrets)} secrets")
        return success_count == len(secrets)

    except Exception as e:
        print(f"❌ Failed to store secrets: {e}")
        return False


def print_security_warning():
    """Print important security warnings"""
    print("🔒 SECURITY WARNING:")
    print("   • Keep these secrets secure and never commit them to version control")
    print("   • Use environment variables or secure secret management in production")
    print("   • Rotate secrets regularly")
    print("   • Use different secrets for different environments (dev/staging/prod)")
    print()


def interactive_mode():
    """Interactive mode for generating secrets"""
    print("🔐 Interactive Secret Generation")
    print("=" * 50)

    generator = SecretGenerator()
    formatter = SecretFormatter()

    while True:
        print("\nAvailable secret types:")
        print("1. JWT Secret Key")
        print("2. Application Secret Key")
        print("3. API Key")
        print("4. Encryption Key")
        print("5. Database Password")
        print("6. Random Token")
        print("7. Generate All Secrets")
        print("8. Exit")

        choice = input("\nSelect option (1-8): ").strip()

        if choice == '1':
            length = int(input("Key length (default 32): ") or "32")
            secret = generator.generate_jwt_secret(length)
            print(f"\nJWT Secret: {secret}")

        elif choice == '2':
            length = int(input("Key length (default 32): ") or "32")
            secret = generator.generate_app_secret(length)
            print(f"\nApp Secret: {secret}")

        elif choice == '3':
            length = int(input("Key length (default 32): ") or "32")
            prefix = input("Prefix (optional): ").strip()
            secret = generator.generate_api_key(length, prefix)
            print(f"\nAPI Key: {secret}")

        elif choice == '4':
            secret = generator.generate_encryption_key()
            print(f"\nEncryption Key: {secret}")

        elif choice == '5':
            length = int(input("Password length (default 16): ") or "16")
            include_symbols = input("Include symbols? (y/n) [y]: ").strip().lower() != 'n'
            secret = generator.generate_database_password(length, include_symbols)
            print(f"\nDatabase Password: {secret}")

        elif choice == '6':
            length = int(input("Token length (default 32): ") or "32")
            secret = generator.generate_hex_token(length)
            print(f"\nRandom Token: {secret}")

        elif choice == '7':
            secrets = generator.generate_all_secrets()
            print("\n" + formatter.format_table(secrets))

            # Ask about storing
            if SECRET_PROVIDERS_AVAILABLE:
                store = input("\nStore in secret provider? (y/n) [n]: ").strip().lower() == 'y'
                if store:
                    provider_type = input("Provider type (auto/cloudflare/file) [auto]: ").strip() or "auto"
                    asyncio.run(store_secrets_in_provider(secrets, provider_type))

            # Ask about saving to file
            save = input("\nSave to .env file? (y/n) [n]: ").strip().lower() == 'y'
            if save:
                filename = input("Filename [.env.secrets]: ").strip() or ".env.secrets"
                with open(filename, 'w') as f:
                    f.write(formatter.format_env_file(secrets))
                print(f"✅ Saved to {filename}")

        elif choice == '8':
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice. Please select 1-8.")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Generate cryptographic secrets for FastAPI Core Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --type jwt --length 32
  %(prog)s --type all --format env --output .env.secrets
  %(prog)s --type api --prefix "api_" --length 24
  %(prog)s --interactive
  %(prog)s --type all --store cloudflare
        """
    )

    parser.add_argument(
        '--type', '-t',
        choices=['jwt', 'app', 'api', 'encryption', 'database', 'token', 'all'],
        help='Type of secret to generate'
    )

    parser.add_argument(
        '--length', '-l',
        type=int,
        default=32,
        help='Length of the secret (default: 32)'
    )

    parser.add_argument(
        '--prefix', '-p',
        default='',
        help='Prefix for API keys'
    )

    parser.add_argument(
        '--format', '-f',
        choices=['env', 'json', 'yaml', 'table'],
        default='env',
        help='Output format (default: env)'
    )

    parser.add_argument(
        '--output', '-o',
        help='Output file (default: stdout)'
    )

    parser.add_argument(
        '--store',
        choices=['auto', 'cloudflare', 'file'],
        help='Store secrets in secret provider'
    )

    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Run in interactive mode'
    )

    parser.add_argument(
        '--no-comments',
        action='store_true',
        help='Exclude comments in env format'
    )

    args = parser.parse_args()

    if args.interactive:
        interactive_mode()
        return

    if not args.type:
        parser.print_help()
        return

    print_security_warning()

    generator = SecretGenerator()
    formatter = SecretFormatter()

    # Generate secrets based on type
    secrets = {}
    if args.type == 'jwt':
        secrets = {'JWT_SECRET_KEY': generator.generate_jwt_secret(args.length)}
    elif args.type == 'app':
        secrets = {'SECRET_KEY': generator.generate_app_secret(args.length)}
    elif args.type == 'api':
        secrets = {'API_KEY': generator.generate_api_key(args.length, args.prefix)}
    elif args.type == 'encryption':
        secrets = {'ENCRYPTION_KEY': generator.generate_encryption_key()}
    elif args.type == 'database':
        secrets = {'DATABASE_PASSWORD': generator.generate_database_password(args.length)}
    elif args.type == 'token':
        secrets = {'TOKEN': generator.generate_hex_token(args.length)}
    elif args.type == 'all':
        secrets = generator.generate_all_secrets()

    # Format output
    output = ""
    if args.format == 'env':
        output = formatter.format_env_file(secrets, not args.no_comments)
    elif args.format == 'json':
        output = formatter.format_json(secrets)
    elif args.format == 'yaml':
        output = formatter.format_yaml(secrets)
    elif args.format == 'table':
        output = formatter.format_table(secrets)

    # Output to file or stdout
    if args.output:
        with open(args.output, 'w') as f:
            f.write(output)
        print(f"✅ Secrets saved to {args.output}")
    else:
        print(output)

    # Store in secret provider if requested
    if args.store and SECRET_PROVIDERS_AVAILABLE:
        print(f"\n📦 Storing secrets in {args.store} provider...")
        success = asyncio.run(store_secrets_in_provider(secrets, args.store))
        if success:
            print("✅ All secrets stored successfully")
        else:
            print("❌ Some secrets failed to store")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Secret generation cancelled by user.")
    except Exception as e:
        print(f"\n❌ Secret generation failed: {e}")
        sys.exit(1)
