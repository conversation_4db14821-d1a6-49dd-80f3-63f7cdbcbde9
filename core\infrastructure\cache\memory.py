"""
Memory Cache Implementation

This module provides an in-memory cache implementation with TTL support,
LRU eviction, and async operations.

Migrated from: core.infrastructure.cache
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional
from dataclasses import dataclass

from ...domain.interfaces import ICache
from ...domain.exceptions import CacheError

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with TTL support"""
    value: Any
    expires_at: Optional[float] = None

    def is_expired(self) -> bool:
        """Check if entry is expired"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at


class MemoryCache(ICache):
    """
    In-memory cache implementation with TTL and LRU eviction

    Features:
    - TTL (Time To Live) support for automatic expiration
    - LRU (Least Recently Used) eviction when max size is reached
    - Thread-safe async operations
    - Configurable max size and default TTL
    """

    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        Initialize memory cache

        Args:
            max_size: Maximum number of entries to store
            default_ttl: Default TTL in seconds (0 = no expiration)
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: Dict[str, float] = {}
        self._lock = asyncio.Lock()

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        async with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None

            if entry.is_expired():
                del self._cache[key]
                self._access_order.pop(key, None)
                return None

            # Update access time for LRU
            self._access_order[key] = time.time()
            return entry.value

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        async with self._lock:
            # Calculate expiration time
            expires_at = None
            if ttl is not None:
                expires_at = time.time() + ttl
            elif self.default_ttl > 0:
                expires_at = time.time() + self.default_ttl

            # Create cache entry
            entry = CacheEntry(value=value, expires_at=expires_at)

            # Add to cache
            self._cache[key] = entry
            self._access_order[key] = time.time()

            # Evict if over max size
            if len(self._cache) > self.max_size:
                await self._evict_lru()

    async def delete(self, key: str) -> None:
        """Delete value from cache"""
        async with self._lock:
            self._cache.pop(key, None)
            self._access_order.pop(key, None)

    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        entry = self._cache.get(key)
        if entry is None:
            return False

        if entry.is_expired():
            async with self._lock:
                self._cache.pop(key, None)
                self._access_order.pop(key, None)
            return False

        return True

    async def clear(self) -> None:
        """Clear all cache"""
        async with self._lock:
            self._cache.clear()
            self._access_order.clear()

    async def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self._access_order:
            return

        # Find oldest accessed key
        oldest_key = min(self._access_order.keys(), key=lambda k: self._access_order[k])
        self._cache.pop(oldest_key, None)
        self._access_order.pop(oldest_key, None)

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "default_ttl": self.default_ttl,
            "hit_ratio": 0.0,  # TODO: Implement hit/miss tracking
        }


__all__ = [
    "CacheEntry",
    "MemoryCache",
]
