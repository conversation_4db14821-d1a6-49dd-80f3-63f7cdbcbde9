"""
Metrics Collection and Reporting

This module provides concrete implementations of metrics collection interfaces
for monitoring application performance and health.

Migrated from: core.infrastructure.metrics
"""

import threading
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

try:
    from core.domain.interfaces import IMetricsCollector
except ImportError:
    # Fallback for development
    class IMetricsCollector:
        pass


class PrometheusMetricsCollector(IMetricsCollector):
    """Prometheus-based metrics collector with fallback to in-memory storage"""

    def __init__(self, port: int = 8000):
        self.port = port
        self._prometheus_available = False
        self._lock = threading.Lock()

        # In-memory storage for when Prometheus is not available
        self.counters = {}
        self.gauges = {}
        self.timers = {}

        # Try to import Prometheus
        try:
            from prometheus_client import Counter, Gauge, Summary, start_http_server
            self._Counter = Counter
            self._Gauge = Gauge
            self._Summary = Summary
            self._start_http_server = start_http_server
            self._prometheus_available = True
            self._prometheus_counters = {}
            self._prometheus_gauges = {}
            self._prometheus_timers = {}
        except ImportError:
            # Fallback to in-memory implementation
            pass

    def _ensure_prometheus(self):
        """Ensure Prometheus is available or raise error"""
        if not self._prometheus_available:
            raise RuntimeError("Prometheus client not available")

    def _get_or_create_counter(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus counter"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._prometheus_counters:
                label_names = list(labels.keys()) if labels else []
                self._prometheus_counters[name] = self._Counter(name, name, label_names)

            counter = self._prometheus_counters[name]
            return counter.labels(**labels) if labels else counter

    def _get_or_create_gauge(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus gauge"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._prometheus_gauges:
                label_names = list(labels.keys()) if labels else []
                self._prometheus_gauges[name] = self._Gauge(name, name, label_names)

            gauge = self._prometheus_gauges[name]
            return gauge.labels(**labels) if labels else gauge

    def _get_or_create_timer(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Get or create a Prometheus summary for timing"""
        self._ensure_prometheus()

        with self._lock:
            if name not in self._prometheus_timers:
                label_names = list(labels.keys()) if labels else []
                self._prometheus_timers[name] = self._Summary(name, name, label_names)

            timer = self._prometheus_timers[name]
            return timer.labels(**labels) if labels else timer

    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric"""
        try:
            if self._prometheus_available:
                counter = self._get_or_create_counter(name, tags)
                counter.inc()
            else:
                # Fallback to in-memory
                key = self._make_key(name, tags)
                with self._lock:
                    self.counters[key] = self.counters.get(key, 0) + 1
        except Exception as e:
            # Log error but don't break application
            print(f"Error incrementing counter {name}: {e}")

    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set gauge metric value"""
        try:
            if self._prometheus_available:
                gauge = self._get_or_create_gauge(name, tags)
                gauge.set(value)
            else:
                # Fallback to in-memory
                key = self._make_key(name, tags)
                with self._lock:
                    self.gauges[key] = value
        except Exception as e:
            # Log error but don't break application
            print(f"Error setting gauge {name}: {e}")

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record timer metric"""
        try:
            if self._prometheus_available:
                timer = self._get_or_create_timer(name, tags)
                timer.observe(duration)
            else:
                # Fallback to in-memory
                key = self._make_key(name, tags)
                with self._lock:
                    if key not in self.timers:
                        self.timers[key] = []
                    self.timers[key].append(duration)
        except Exception as e:
            # Log error but don't break application
            print(f"Error recording timer {name}: {e}")

    def _make_key(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Make a key for in-memory storage"""
        if not tags:
            return name
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

    def start_server(self) -> None:
        """Start Prometheus metrics server"""
        if self._prometheus_available:
            try:
                self._start_http_server(self.port)
                print(f"📊 Prometheus metrics server started on port {self.port}")
            except Exception as e:
                print(f"Failed to start metrics server: {e}")
        else:
            print("📊 Prometheus not available, using in-memory metrics storage")

    async def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics (for in-memory fallback)"""
        with self._lock:
            return {
                "counters": self.counters.copy(),
                "gauges": self.gauges.copy(),
                "timers": {k: v.copy() for k, v in self.timers.items()},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }


class InMemoryMetricsCollector(IMetricsCollector):
    """Simple in-memory metrics collector for testing"""

    def __init__(self):
        self._lock = threading.Lock()
        self.counters = {}
        self.gauges = {}
        self.timers = {}

    def _make_key(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Make a key for storage"""
        if not tags:
            return name
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric"""
        key = self._make_key(name, tags)
        with self._lock:
            self.counters[key] = self.counters.get(key, 0) + 1

    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set gauge metric value"""
        key = self._make_key(name, tags)
        with self._lock:
            self.gauges[key] = value

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record timer metric"""
        key = self._make_key(name, tags)
        with self._lock:
            if key not in self.timers:
                self.timers[key] = []
            self.timers[key].append(duration)

    async def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        with self._lock:
            return {
                "counters": self.counters.copy(),
                "gauges": self.gauges.copy(),
                "timers": {k: v.copy() for k, v in self.timers.items()},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }


__all__ = [
    "PrometheusMetricsCollector",
    "InMemoryMetricsCollector",
]
