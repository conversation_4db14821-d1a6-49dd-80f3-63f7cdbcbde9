"""
Scoring System Tests

This module tests the scoring system implementation including
metrics aggregation and score calculation for services.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from core.infrastructure.scoring import ScoringEngine, MetricsAggregator
from core.infrastructure.metrics import InMemoryMetricsCollector
from core.infrastructure.service_registry import ServiceRegistryImpl
from core.domain.interfaces import ScoreType


@pytest.fixture
def metrics_collector():
    """Create a metrics collector with sample data"""
    collector = InMemoryMetricsCollector()
    
    # Add sample metrics
    collector.increment_counter("requests_total", {"service": "test-service"}, 100)
    collector.increment_counter("errors_total", {"service": "test-service"}, 5)
    collector.record_timer("response_time", 0.25, {"service": "test-service"})
    collector.record_timer("response_time", 0.30, {"service": "test-service"})
    collector.record_timer("response_time", 0.20, {"service": "test-service"})
    
    return collector


@pytest.fixture
def service_registry():
    """Create a service registry"""
    return ServiceRegistryImpl()


@pytest.fixture
def metrics_aggregator(metrics_collector, service_registry):
    """Create a metrics aggregator"""
    return MetricsAggregator(metrics_collector, service_registry)


@pytest.fixture
def scoring_engine(metrics_aggregator):
    """Create a scoring engine"""
    return ScoringEngine(metrics_aggregator)


class TestMetricsAggregator:
    """Test metrics aggregation functionality"""
    
    @pytest.mark.asyncio
    async def test_aggregate_metrics(self, metrics_aggregator):
        """Test basic metrics aggregation"""
        result = await metrics_aggregator.aggregate_metrics("test-service")
        
        assert result["service_name"] == "test-service"
        assert result["request_count"] == 100
        assert result["error_count"] == 5
        assert result["error_rate"] == 0.05
        assert result["average_response_time"] == 0.25  # (0.25 + 0.30 + 0.20) / 3
        assert "timestamp" in result
    
    @pytest.mark.asyncio
    async def test_get_performance_metrics(self, metrics_aggregator):
        """Test getting performance metrics"""
        metrics = await metrics_aggregator.get_performance_metrics("test-service")
        
        assert "average_response_time" in metrics
        assert "throughput" in metrics
        assert "cpu_usage" in metrics
        assert "memory_usage" in metrics
        assert "disk_io" in metrics
        
        assert metrics["average_response_time"] == 0.25
    
    @pytest.mark.asyncio
    async def test_get_reliability_metrics(self, metrics_aggregator):
        """Test getting reliability metrics"""
        metrics = await metrics_aggregator.get_reliability_metrics("test-service")
        
        assert "error_rate" in metrics
        assert "uptime" in metrics
        assert "mtbf" in metrics
        assert "mttr" in metrics
        assert "success_rate" in metrics
        
        assert metrics["error_rate"] == 0.05
        assert metrics["success_rate"] == 0.95
    
    @pytest.mark.asyncio
    async def test_get_availability_metrics(self, metrics_aggregator):
        """Test getting availability metrics"""
        metrics = await metrics_aggregator.get_availability_metrics("test-service")
        
        assert "uptime_percentage" in metrics
        assert "response_success_rate" in metrics
        assert "health_check_success_rate" in metrics
        assert "service_discovery_success_rate" in metrics
        
        # All should be reasonable values
        for value in metrics.values():
            assert 0 <= value <= 100
    
    @pytest.mark.asyncio
    async def test_metrics_caching(self, metrics_aggregator):
        """Test that metrics are cached properly"""
        # First call
        result1 = await metrics_aggregator.aggregate_metrics("test-service")
        
        # Second call should use cache
        result2 = await metrics_aggregator.aggregate_metrics("test-service")
        
        # Results should be identical (from cache)
        assert result1["timestamp"] == result2["timestamp"]
        assert result1["request_count"] == result2["request_count"]


class TestScoringEngine:
    """Test scoring engine functionality"""
    
    @pytest.mark.asyncio
    async def test_calculate_performance_score(self, scoring_engine):
        """Test performance score calculation"""
        metrics = {
            "average_response_time": 0.25,
            "throughput": 5.0,
            "cpu_usage": 0.3,
            "memory_usage": 0.4
        }
        
        score = await scoring_engine.calculate_score(
            "test-service", 
            ScoreType.PERFORMANCE, 
            metrics
        )
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    @pytest.mark.asyncio
    async def test_calculate_reliability_score(self, scoring_engine):
        """Test reliability score calculation"""
        metrics = {
            "error_rate": 0.05,
            "uptime": 0.99,
            "mttr": 0.5
        }
        
        score = await scoring_engine.calculate_score(
            "test-service",
            ScoreType.RELIABILITY,
            metrics
        )
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    @pytest.mark.asyncio
    async def test_calculate_availability_score(self, scoring_engine):
        """Test availability score calculation"""
        metrics = {
            "uptime_percentage": 99.9,
            "response_success_rate": 99.5,
            "health_check_success_rate": 100.0
        }
        
        score = await scoring_engine.calculate_score(
            "test-service",
            ScoreType.AVAILABILITY,
            metrics
        )
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
        # Should be high score for good metrics
        assert score > 90
    
    @pytest.mark.asyncio
    async def test_calculate_composite_score(self, scoring_engine):
        """Test composite score calculation"""
        score = await scoring_engine.calculate_score(
            "test-service",
            ScoreType.COMPOSITE
        )
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    @pytest.mark.asyncio
    async def test_get_composite_score_with_breakdown(self, scoring_engine):
        """Test getting composite score with detailed breakdown"""
        result = await scoring_engine.get_composite_score("test-service")
        
        assert result["service_name"] == "test-service"
        assert "composite_score" in result
        assert "breakdown" in result
        assert "weights" in result
        assert "timestamp" in result
        assert "grade" in result
        
        # Check breakdown structure
        breakdown = result["breakdown"]
        assert "performance" in breakdown
        assert "reliability" in breakdown
        assert "availability" in breakdown
        
        # All scores should be valid
        assert 0 <= result["composite_score"] <= 100
        for score in breakdown.values():
            assert 0 <= score <= 100
    
    @pytest.mark.asyncio
    async def test_score_grading(self, scoring_engine):
        """Test score to grade conversion"""
        # Test different score ranges
        test_cases = [
            (95, "A"),
            (85, "B"),
            (75, "C"),
            (65, "D"),
            (45, "F")
        ]
        
        for score, expected_grade in test_cases:
            grade = scoring_engine._get_score_grade(score)
            assert grade == expected_grade
    
    @pytest.mark.asyncio
    async def test_update_score_weights(self, scoring_engine):
        """Test updating scoring weights"""
        new_weights = {
            ScoreType.COMPOSITE: {
                ScoreType.PERFORMANCE: 0.5,
                ScoreType.RELIABILITY: 0.3,
                ScoreType.AVAILABILITY: 0.2
            }
        }
        
        await scoring_engine.update_score_weights(new_weights)
        
        # Verify weights were updated
        composite_weights = scoring_engine.score_weights[ScoreType.COMPOSITE]
        assert composite_weights[ScoreType.PERFORMANCE] == 0.5
        assert composite_weights[ScoreType.RELIABILITY] == 0.3
        assert composite_weights[ScoreType.AVAILABILITY] == 0.2
    
    @pytest.mark.asyncio
    async def test_custom_weights_in_composite_score(self, scoring_engine):
        """Test using custom weights for composite score"""
        custom_weights = {
            ScoreType.PERFORMANCE: 0.6,
            ScoreType.RELIABILITY: 0.2,
            ScoreType.AVAILABILITY: 0.2
        }
        
        result = await scoring_engine.get_composite_score(
            "test-service",
            weights=custom_weights
        )
        
        # Verify custom weights were used
        assert result["weights"][ScoreType.PERFORMANCE] == 0.6
        assert result["weights"][ScoreType.RELIABILITY] == 0.2
        assert result["weights"][ScoreType.AVAILABILITY] == 0.2
    
    @pytest.mark.asyncio
    async def test_score_calculation_edge_cases(self, scoring_engine):
        """Test score calculation with edge case metrics"""
        # Test with zero/minimal metrics
        zero_metrics = {
            "average_response_time": 0.0,
            "throughput": 0.0,
            "cpu_usage": 0.0,
            "memory_usage": 0.0
        }
        
        score = await scoring_engine.calculate_score(
            "test-service",
            ScoreType.PERFORMANCE,
            zero_metrics
        )
        
        assert 0 <= score <= 100
        
        # Test with extreme metrics
        extreme_metrics = {
            "average_response_time": 10.0,  # Very slow
            "throughput": 0.1,  # Very low
            "cpu_usage": 1.0,   # 100% CPU
            "memory_usage": 1.0  # 100% memory
        }
        
        score = await scoring_engine.calculate_score(
            "test-service",
            ScoreType.PERFORMANCE,
            extreme_metrics
        )
        
        assert 0 <= score <= 100
        # Should be a low score for poor metrics
        assert score < 50


class TestScoringIntegration:
    """Integration tests for the scoring system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_scoring(self, scoring_engine, metrics_collector):
        """Test complete end-to-end scoring workflow"""
        # Add more realistic metrics
        service_name = "production-service"
        
        # Simulate production metrics
        for i in range(50):
            metrics_collector.increment_counter("requests_total", {"service": service_name})
            if i % 20 == 0:  # 5% error rate
                metrics_collector.increment_counter("errors_total", {"service": service_name})
            
            # Variable response times
            response_time = 0.1 + (i % 5) * 0.05  # 0.1 to 0.3 seconds
            metrics_collector.record_timer("response_time", response_time, {"service": service_name})
        
        # Calculate all score types
        performance_score = await scoring_engine.calculate_score(service_name, ScoreType.PERFORMANCE)
        reliability_score = await scoring_engine.calculate_score(service_name, ScoreType.RELIABILITY)
        availability_score = await scoring_engine.calculate_score(service_name, ScoreType.AVAILABILITY)
        composite_result = await scoring_engine.get_composite_score(service_name)
        
        # Verify all scores are reasonable
        assert 0 <= performance_score <= 100
        assert 0 <= reliability_score <= 100
        assert 0 <= availability_score <= 100
        assert 0 <= composite_result["composite_score"] <= 100
        
        # Verify composite score is within range of individual scores
        individual_scores = [performance_score, reliability_score, availability_score]
        min_score = min(individual_scores)
        max_score = max(individual_scores)
        
        assert min_score <= composite_result["composite_score"] <= max_score
