#!/usr/bin/env python3
"""
Example: Using Cloudflare KV Secrets in FastAPI
"""

import asyncio
from core.config.secret_providers import SecretConfigProvider

async def example_usage():
    """Example of how to use secrets in your FastAPI application"""

    # Auto-detects Cloudflare KV (no configuration needed!)
    provider = SecretConfigProvider()

    print("🔧 Setting up secrets...")

    # Store your application secrets
    secrets_to_store = {
        "database-password": "super-secure-db-password-123",
        "jwt-secret-key": "your-jwt-secret-key-here",
        "api-key": "your-external-api-key",
        "redis-password": "redis-secure-password",
        "encryption-key": "your-encryption-key-here"
    }

    # Set secrets in Cloudflare KV
    for key, value in secrets_to_store.items():
        success = await provider.set_secret(key, value)
        if success:
            print(f"✅ Stored: {key}")
        else:
            print(f"❌ Failed to store: {key}")

    print("\n📖 Retrieving secrets...")

    # Get secrets in your FastAPI app (returns string values)
    database_password = await provider.get_secret("database-password")
    jwt_secret = await provider.get_secret("jwt-secret-key")
    api_key = await provider.get_secret("api-key")

    if database_password:
        print(f"✅ Database password: {database_password[:8]}...")

    if jwt_secret:
        print(f"✅ JWT secret: {jwt_secret[:8]}...")

    if api_key:
        print(f"✅ API key: {api_key[:8]}...")

    # List all secrets
    all_secrets = await provider.list_secrets()
    print(f"\n📋 Total secrets stored: {len(all_secrets)}")
    for secret_name in all_secrets:
        print(f"   - {secret_name}")

if __name__ == "__main__":
    asyncio.run(example_usage())
