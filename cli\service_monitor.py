#!/usr/bin/env python3
"""
Service Monitor CLI

A command-line tool for monitoring and managing your 5-10 services including bots.
Provides real-time monitoring, health checks, and service management capabilities.
"""

import asyncio
import click
import httpx
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.layout import Layout
from rich.text import Text


console = Console()


class ServiceMonitor:
    """Service monitoring and management client"""
    
    def __init__(self, core_api_url: str = "http://localhost:8000"):
        self.core_api_url = core_api_url.rstrip("/")
        self.client = httpx.AsyncClient(timeout=10.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def get_services(self) -> List[Dict]:
        """Get list of all registered services"""
        try:
            response = await self.client.get(f"{self.core_api_url}/api/v1/services")
            response.raise_for_status()
            return response.json().get("services", [])
        except Exception as e:
            console.print(f"[red]Error fetching services: {e}[/red]")
            return []
    
    async def get_health(self) -> Dict:
        """Get system health status"""
        try:
            response = await self.client.get(f"{self.core_api_url}/api/v1/health")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error fetching health: {e}[/red]")
            return {"status": "error", "error": str(e)}
    
    async def get_metrics(self) -> Dict:
        """Get system metrics"""
        try:
            response = await self.client.get(f"{self.core_api_url}/api/v1/metrics")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error fetching metrics: {e}[/red]")
            return {"metrics": {}}
    
    async def restart_service(self, service_name: str) -> Dict:
        """Restart a specific service"""
        try:
            response = await self.client.post(
                f"{self.core_api_url}/api/v1/services/{service_name}/restart"
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error restarting service {service_name}: {e}[/red]")
            return {"success": False, "error": str(e)}
    
    async def call_service(self, service_name: str, endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict:
        """Call a service endpoint"""
        try:
            response = await self.client.post(
                f"{self.core_api_url}/api/v1/services/call",
                json={
                    "service_name": service_name,
                    "endpoint": endpoint,
                    "method": method,
                    "data": data or {}
                }
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error calling service {service_name}: {e}[/red]")
            return {"success": False, "error": str(e)}


@click.group()
@click.option("--api-url", default="http://localhost:8000", help="Core API URL")
@click.pass_context
def cli(ctx, api_url):
    """Service Monitor CLI - Manage your microservices and bots"""
    ctx.ensure_object(dict)
    ctx.obj["api_url"] = api_url


@cli.command()
@click.pass_context
async def status(ctx):
    """Show overall system status"""
    async with ServiceMonitor(ctx.obj["api_url"]) as monitor:
        health = await monitor.get_health()
        services = await monitor.get_services()
        metrics = await monitor.get_metrics()
        
        # System Health Panel
        health_status = health.get("status", "unknown")
        health_color = "green" if health_status == "healthy" else "red"
        
        health_panel = Panel(
            f"[{health_color}]{health_status.upper()}[/{health_color}]\n"
            f"Version: {health.get('version', 'unknown')}\n"
            f"Uptime: {health.get('uptime', 0):.1f}s",
            title="System Health",
            border_style=health_color
        )
        
        # Services Table
        table = Table(title="Registered Services")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="magenta")
        table.add_column("URL", style="blue")
        table.add_column("Version", style="green")
        table.add_column("Health", style="bold")
        
        for service in services:
            service_type = service.get("metadata", {}).get("type", "service")
            health_status = "✅ Healthy" if service.get("is_healthy", False) else "❌ Unhealthy"
            health_style = "green" if service.get("is_healthy", False) else "red"
            
            table.add_row(
                service["name"],
                service_type,
                service["url"],
                service["version"],
                f"[{health_style}]{health_status}[/{health_style}]"
            )
        
        # Metrics Panel
        metrics_data = metrics.get("metrics", {})
        counters = metrics_data.get("counters", {})
        gauges = metrics_data.get("gauges", {})
        
        metrics_text = ""
        if counters:
            metrics_text += "Counters:\n"
            for key, value in counters.items():
                metrics_text += f"  {key}: {value}\n"
        
        if gauges:
            metrics_text += "\nGauges:\n"
            for key, value in gauges.items():
                metrics_text += f"  {key}: {value}\n"
        
        metrics_panel = Panel(
            metrics_text or "No metrics available",
            title="System Metrics",
            border_style="blue"
        )
        
        # Display everything
        console.print(health_panel)
        console.print(table)
        console.print(metrics_panel)


@cli.command()
@click.pass_context
async def list(ctx):
    """List all registered services"""
    async with ServiceMonitor(ctx.obj["api_url"]) as monitor:
        services = await monitor.get_services()
        
        if not services:
            console.print("[yellow]No services registered[/yellow]")
            return
        
        table = Table(title=f"Registered Services ({len(services)} total)")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="magenta") 
        table.add_column("URL", style="blue")
        table.add_column("Version", style="green")
        table.add_column("Health", style="bold")
        table.add_column("Capabilities", style="yellow")
        
        for service in services:
            metadata = service.get("metadata", {})
            service_type = metadata.get("type", "service")
            capabilities = ", ".join(metadata.get("capabilities", []))
            health_status = "✅ Healthy" if service.get("is_healthy", False) else "❌ Unhealthy"
            health_style = "green" if service.get("is_healthy", False) else "red"
            
            table.add_row(
                service["name"],
                service_type,
                service["url"],
                service["version"],
                f"[{health_style}]{health_status}[/{health_style}]",
                capabilities
            )
        
        console.print(table)


@cli.command()
@click.argument("service_name")
@click.pass_context
async def restart(ctx, service_name):
    """Restart a specific service"""
    async with ServiceMonitor(ctx.obj["api_url"]) as monitor:
        with console.status(f"[bold green]Restarting {service_name}..."):
            result = await monitor.restart_service(service_name)
        
        if result.get("success"):
            console.print(f"[green]✅ Successfully restarted {service_name}[/green]")
        else:
            error = result.get("error", "Unknown error")
            console.print(f"[red]❌ Failed to restart {service_name}: {error}[/red]")


@cli.command()
@click.argument("service_name")
@click.argument("endpoint")
@click.option("--method", default="GET", help="HTTP method")
@click.option("--data", help="JSON data to send")
@click.pass_context
async def call(ctx, service_name, endpoint, method, data):
    """Call a service endpoint"""
    async with ServiceMonitor(ctx.obj["api_url"]) as monitor:
        request_data = None
        if data:
            try:
                request_data = json.loads(data)
            except json.JSONDecodeError:
                console.print("[red]Invalid JSON data[/red]")
                return
        
        with console.status(f"[bold green]Calling {service_name}{endpoint}..."):
            result = await monitor.call_service(service_name, endpoint, method, request_data)
        
        if result.get("success", True):
            console.print(f"[green]✅ Response from {service_name}:[/green]")
            console.print_json(data=result)
        else:
            error = result.get("error", "Unknown error")
            console.print(f"[red]❌ Failed to call {service_name}: {error}[/red]")


@cli.command()
@click.option("--interval", default=5, help="Refresh interval in seconds")
@click.pass_context
async def watch(ctx, interval):
    """Watch services in real-time"""
    async with ServiceMonitor(ctx.obj["api_url"]) as monitor:
        
        def create_dashboard():
            layout = Layout()
            layout.split_column(
                Layout(name="header", size=3),
                Layout(name="body"),
                Layout(name="footer", size=3)
            )
            
            return layout
        
        async def update_dashboard():
            while True:
                try:
                    services = await monitor.get_services()
                    health = await monitor.get_health()
                    
                    # Create services table
                    table = Table(title="Live Service Status")
                    table.add_column("Name", style="cyan")
                    table.add_column("Type", style="magenta")
                    table.add_column("Health", style="bold")
                    table.add_column("Last Check", style="yellow")
                    
                    for service in services:
                        service_type = service.get("metadata", {}).get("type", "service")
                        health_status = "✅ Healthy" if service.get("is_healthy", False) else "❌ Unhealthy"
                        health_style = "green" if service.get("is_healthy", False) else "red"
                        last_check = datetime.now().strftime("%H:%M:%S")
                        
                        table.add_row(
                            service["name"],
                            service_type,
                            f"[{health_style}]{health_status}[/{health_style}]",
                            last_check
                        )
                    
                    layout = create_dashboard()
                    layout["header"].update(Panel(f"Service Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="bold blue"))
                    layout["body"].update(table)
                    layout["footer"].update(Panel(f"System Status: {health.get('status', 'unknown').upper()}", style="bold green"))
                    
                    return layout
                    
                except Exception as e:
                    error_panel = Panel(f"Error: {e}", style="bold red")
                    layout = create_dashboard()
                    layout["body"].update(error_panel)
                    return layout
        
        console.print("[bold green]Starting real-time monitoring... Press Ctrl+C to stop[/bold green]")
        
        try:
            while True:
                dashboard = await update_dashboard()
                console.clear()
                console.print(dashboard)
                await asyncio.sleep(interval)
        except KeyboardInterrupt:
            console.print("\n[yellow]Monitoring stopped[/yellow]")


def main():
    """Main entry point"""
    # Convert sync click commands to async
    import sys
    if len(sys.argv) > 1 and sys.argv[1] in ["status", "list", "restart", "call", "watch"]:
        # Run async command
        asyncio.run(cli())
    else:
        # Run sync command (help, etc.)
        cli()


if __name__ == "__main__":
    main()
