import pytest
from unittest.mock import AsyncMock, MagicMock
from fastapi import <PERSON><PERSON><PERSON>
from httpx import AsyncClient
from datetime import datetime, timezone

from core.presentation.api import router as api_router, get_container
from core.infrastructure.service_registry import ServiceInfo
from core.domain.interfaces import IServiceRegistry, IHealthCheck, IMetricsCollector


@pytest.fixture
def app():
    """Create test app with mocked dependencies"""
    app = FastAPI()
    app.include_router(api_router)

    # Mock the dependency injection container
    mock_container = MagicMock()
    mock_service_registry = MagicMock()
    mock_health_checker = MagicMock()
    mock_metrics_collector = MagicMock()

    # Configure the container to return our mocked services
    async def mock_get_service_async(interface):
        if interface == IServiceRegistry:
            return mock_service_registry
        elif interface == IHealthCheck:
            return mock_health_checker
        elif interface == IMetricsCollector:
            return mock_metrics_collector
        return MagicMock()

    mock_container.get_service_async = mock_get_service_async
    app.state.container = mock_container

    # Store references for easy access in tests
    app.state.service_registry = mock_service_registry
    app.state.health_checker = mock_health_checker
    app.state.metrics = mock_metrics_collector

    yield app

    # Cleanup dependency overrides after each test
    app.dependency_overrides.clear()


@pytest.fixture
async def client(app):
    """Create async test client"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.mark.asyncio
async def test_health_endpoint(client, app):
    """Test health endpoint"""
    # Setup mock
    app.state.health_checker.check_health = AsyncMock(return_value={
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0",
        "uptime": 3600.0
    })

    # Make request
    response = await client.get("/api/v1/health")

    # Assertions
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
    assert app.state.health_checker.check_health.called


@pytest.mark.asyncio
async def test_metrics_endpoint(client, app):
    """Test metrics endpoint"""
    # Setup mock
    app.state.metrics.get_metrics = AsyncMock(return_value={
        "uptime_seconds": 3600,
        "counters": {"requests": 100},
        "gauges": {"memory_usage": 1024},
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

    # Make request
    response = await client.get("/api/v1/metrics")

    # Assertions
    assert response.status_code == 200
    assert "uptime_seconds" in response.json()["metrics"]
    assert app.state.metrics.get_metrics.called


@pytest.mark.asyncio
async def test_register_service(client, app):
    """Test service registration endpoint"""
    # Setup mocks
    app.state.service_registry.register_service = AsyncMock(return_value={
        "success": True,
        "service": "test-service",
        "api_key": "generated-api-key"
    })

    # Make request
    service_data = {
        "name": "test-service",
        "url": "http://test-service:8000",
        "version": "1.0.0",
        "health_endpoint": "/health",
        "metadata": {"description": "Test service"}
    }
    response = await client.post(
        "/api/v1/services/register",
        json=service_data
    )

    # Assertions
    assert response.status_code == 200
    assert response.json()["status"] == "registered"
    assert response.json()["service"] == "test-service"
    assert response.json()["api_key"] == "generated-api-key"
    app.state.service_registry.register_service.assert_called_once()


@pytest.mark.asyncio
async def test_register_service_failure(client, app):
    """Test service registration failure"""
    # Setup mocks
    app.state.service_registry.register_service = AsyncMock(return_value={
        "success": False,
        "error": "Service already exists"
    })

    # Make request
    service_data = {
        "name": "test-service",
        "url": "http://test-service:8000",
        "version": "1.0.0"
    }
    response = await client.post(
        "/api/v1/services/register",
        json=service_data
    )

    # Assertions
    # The API wraps the error in a 500 response, but includes the original error message
    assert response.status_code == 500
    assert "Service already exists" in response.json()["detail"]


@pytest.mark.asyncio
async def test_unregister_service(client, app, mock_auth):
    """Test service unregistration endpoint"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    app.state.service_registry.unregister = AsyncMock(return_value=True)

    # Make request
    response = await client.delete(
        "/api/v1/services/test-service",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 200
    assert response.json()["status"] == "unregistered"
    assert response.json()["service"] == "test-service"
    app.state.service_registry.unregister.assert_called_once_with("test-service")


@pytest.mark.asyncio
async def test_unregister_service_not_found(client, app, mock_auth):
    """Test unregistering a non-existent service"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    app.state.service_registry.unregister = AsyncMock(return_value=False)

    # Make request
    response = await client.delete(
        "/api/v1/services/non-existent",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 404
    assert "Service not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_restart_service(client, app, mock_auth):
    """Test service restart endpoint"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    app.state.service_registry.restart_service = AsyncMock(return_value={
        "success": True,
        "service": "test-service",
        "status": "restarting"
    })
    app.state.metrics.increment_counter = MagicMock()

    # Make request
    response = await client.post(
        "/api/v1/services/test-service/restart",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 200
    assert response.json()["success"] is True
    assert response.json()["service"] == "test-service"
    app.state.service_registry.restart_service.assert_called_once_with("test-service")
    app.state.metrics.increment_counter.assert_called_once()


@pytest.mark.asyncio
async def test_restart_service_failure(client, app, mock_auth):
    """Test service restart failure"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    app.state.service_registry.restart_service = AsyncMock(return_value={
        "success": False,
        "service": "test-service",
        "error": "Failed to restart"
    })

    # Make request
    response = await client.post(
        "/api/v1/services/test-service/restart",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 503
    assert "Failed to restart" in response.json()["detail"]


@pytest.mark.asyncio
async def test_list_services(client, app, mock_auth):
    """Test listing services endpoint"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    service1 = ServiceInfo(
        name="service1",
        url="http://service1:8000",
        version="1.0.0",
        is_healthy=True
    )
    service2 = ServiceInfo(
        name="service2",
        url="http://service2:8000",
        version="1.0.0",
        is_healthy=False
    )

    app.state.service_registry.list_services = AsyncMock(return_value=[service1, service2])

    # Make request - all services
    response = await client.get(
        "/api/v1/services",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 200
    services = response.json()["services"]
    assert len(services) == 2
    assert services[0]["name"] == "service1"
    assert services[1]["name"] == "service2"
    app.state.service_registry.list_services.assert_called_with(only_healthy=False)

    # Reset mock
    app.state.service_registry.list_services.reset_mock()
    app.state.service_registry.list_services.return_value = [service1]

    # Make request - only healthy services
    response = await client.get(
        "/api/v1/services?only_healthy=true",
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 200
    services = response.json()["services"]
    assert len(services) == 1
    assert services[0]["name"] == "service1"
    app.state.service_registry.list_services.assert_called_with(only_healthy=True)


@pytest.mark.asyncio
async def test_call_service(client, app, mock_auth):
    """Test calling a service endpoint"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"result": "success"}
    mock_response.headers = {"content-type": "application/json"}

    app.state.service_registry.call_service = AsyncMock(return_value=mock_response)

    # Make request
    call_data = {
        "service": "test-service",
        "endpoint": "/api/test",
        "method": "POST",
        "data": {"param": "value"},
        "headers": {"Custom-Header": "value"}
    }

    response = await client.post(
        "/api/v1/services/call",
        json=call_data,
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 200
    assert response.json()["status_code"] == 200
    assert response.json()["content"]["result"] == "success"
    app.state.service_registry.call_service.assert_called_once_with(
        service_name="test-service",
        endpoint="/api/test",
        method="POST",
        json={"param": "value"},
        headers={"Custom-Header": "value"}
    )


@pytest.mark.asyncio
async def test_call_service_unavailable(client, app, mock_auth):
    """Test calling an unavailable service"""
    # Override security dependency
    app.dependency_overrides[security_barrier.verify_api_key] = lambda: mock_auth

    # Setup mocks
    app.state.service_registry.call_service = AsyncMock(return_value=None)

    # Make request
    call_data = {
        "service": "unavailable-service",
        "endpoint": "/api/test",
        "method": "GET"
    }

    response = await client.post(
        "/api/v1/services/call",
        json=call_data,
        headers={"X-API-Key": "test-key"}
    )

    # Assertions
    assert response.status_code == 503
    assert "Service unavailable" in response.json()["detail"]