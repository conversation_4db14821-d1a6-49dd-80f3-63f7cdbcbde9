"""
Domain Interfaces and Protocols

This module defines the core interfaces and contracts that must be implemented
by the infrastructure and application layers.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, TypeVar, Protocol, runtime_checkable
from datetime import datetime
from enum import Enum

T = TypeVar('T')
R = TypeVar('R')


class ServiceStatus(Enum):
    """Service status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


class PluginStatus(Enum):
    """Plugin status enumeration"""
    LOADED = "loaded"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"


class CircuitBreakerState(Enum):
    """Circuit breaker state enumeration"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class ScoreType(Enum):
    """Score type enumeration"""
    PERFORMANCE = "performance"
    RELIABILITY = "reliability"
    AVAILABILITY = "availability"
    QUALITY = "quality"
    COMPOSITE = "composite"


@runtime_checkable
class IHealthCheck(Protocol):
    """Health check interface"""

    async def check_health(self) -> Dict[str, Any]:
        """Perform health check and return status"""
        ...

    @property
    def name(self) -> str:
        """Health check name"""
        ...


@runtime_checkable
class IService(Protocol):
    """Service interface"""

    @property
    def name(self) -> str:
        """Service name"""
        ...

    @property
    def version(self) -> str:
        """Service version"""
        ...

    async def start(self) -> None:
        """Start the service"""
        ...

    async def stop(self) -> None:
        """Stop the service"""
        ...

    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        ...

    @property
    def status(self) -> ServiceStatus:
        """Current service status"""
        ...


@runtime_checkable
class IServiceRegistry(Protocol):
    """Service registry interface"""

    async def register_service(
        self,
        name: str,
        url: str,
        version: str,
        health_endpoint: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a new service"""
        ...

    async def unregister_service(self, service_name: str) -> Dict[str, Any]:
        """Unregister a service"""
        ...

    async def list_services(self, only_healthy: bool = False) -> List[Dict[str, Any]]:
        """List all registered services"""
        ...

    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Call a registered service"""
        ...

    async def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a registered service"""
        ...


@runtime_checkable
class IPlugin(Protocol):
    """Plugin interface"""

    @property
    def name(self) -> str:
        """Plugin name"""
        ...

    @property
    def version(self) -> str:
        """Plugin version"""
        ...

    @property
    def dependencies(self) -> List[str]:
        """Plugin dependencies"""
        ...

    async def load(self) -> None:
        """Load the plugin"""
        ...

    async def unload(self) -> None:
        """Unload the plugin"""
        ...

    async def configure(self, config: Dict[str, Any]) -> None:
        """Configure the plugin"""
        ...

    @property
    def status(self) -> PluginStatus:
        """Current plugin status"""
        ...


@runtime_checkable
class IRepository(Protocol, Generic[T]):
    """Repository interface for data access"""

    async def get_by_id(self, id: Any) -> Optional[T]:
        """Get entity by ID"""
        ...

    async def get_all(self, **filters) -> List[T]:
        """Get all entities with optional filters"""
        ...

    async def create(self, entity: T) -> T:
        """Create new entity"""
        ...

    async def update(self, entity: T) -> T:
        """Update existing entity"""
        ...

    async def delete(self, id: Any) -> bool:
        """Delete entity by ID"""
        ...


@runtime_checkable
class ICache(Protocol):
    """Cache interface"""

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        ...

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        ...

    async def delete(self, key: str) -> None:
        """Delete value from cache"""
        ...

    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        ...

    async def clear(self) -> None:
        """Clear all cache"""
        ...


@runtime_checkable
class IUserStore(Protocol):
    """User store interface for user management"""

    async def get_user_by_id(self, user_id: str) -> Optional[Any]:
        """Get user by ID"""
        ...

    async def get_user_by_username(self, username: str) -> Optional[Any]:
        """Get user by username"""
        ...

    async def get_user_by_email(self, email: str) -> Optional[Any]:
        """Get user by email"""
        ...

    async def create_user(self, user_data: Dict[str, Any]) -> Any:
        """Create new user"""
        ...

    async def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Optional[Any]:
        """Update existing user"""
        ...

    async def delete_user(self, user_id: str) -> bool:
        """Delete user by ID"""
        ...

    async def list_users(self, limit: int = 100, offset: int = 0, **filters) -> List[Any]:
        """List users with pagination and filters"""
        ...

    async def verify_password(self, user_id: str, password: str) -> bool:
        """Verify user password"""
        ...

    async def update_password(self, user_id: str, new_password: str) -> bool:
        """Update user password"""
        ...

    async def is_username_taken(self, username: str) -> bool:
        """Check if username is already taken"""
        ...

    async def is_email_taken(self, email: str) -> bool:
        """Check if email is already taken"""
        ...


@runtime_checkable
class IRoleStore(Protocol):
    """Role store interface for role-based access control"""

    async def get_role_by_id(self, role_id: str) -> Optional[Any]:
        """Get role by ID"""
        ...

    async def get_role_by_name(self, role_name: str) -> Optional[Any]:
        """Get role by name"""
        ...

    async def create_role(self, role_data: Dict[str, Any]) -> Any:
        """Create new role"""
        ...

    async def update_role(self, role_id: str, role_data: Dict[str, Any]) -> Optional[Any]:
        """Update existing role"""
        ...

    async def delete_role(self, role_id: str) -> bool:
        """Delete role by ID"""
        ...

    async def list_roles(self, limit: int = 100, offset: int = 0) -> List[Any]:
        """List all roles with pagination"""
        ...

    async def get_user_roles(self, user_id: str) -> List[Any]:
        """Get all roles assigned to a user"""
        ...

    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """Assign role to user"""
        ...

    async def remove_role_from_user(self, user_id: str, role_id: str) -> bool:
        """Remove role from user"""
        ...

    async def get_role_permissions(self, role_id: str) -> List[str]:
        """Get all permissions for a role"""
        ...

    async def add_permission_to_role(self, role_id: str, permission: str) -> bool:
        """Add permission to role"""
        ...

    async def remove_permission_from_role(self, role_id: str, permission: str) -> bool:
        """Remove permission from role"""
        ...


@runtime_checkable
class IEventBus(Protocol):
    """Event bus interface"""

    async def publish(self, event: str, data: Any) -> None:
        """Publish an event"""
        ...

    async def subscribe(self, event: str, handler: Any) -> None:
        """Subscribe to an event"""
        ...

    async def unsubscribe(self, event: str, handler: Any) -> None:
        """Unsubscribe from an event"""
        ...


@runtime_checkable
class IMessageBroker(Protocol):
    """Message broker interface"""

    async def send_message(self, queue: str, message: Any) -> None:
        """Send message to queue"""
        ...

    async def receive_message(self, queue: str) -> Optional[Any]:
        """Receive message from queue"""
        ...

    async def create_queue(self, queue: str) -> None:
        """Create a queue"""
        ...

    async def delete_queue(self, queue: str) -> None:
        """Delete a queue"""
        ...


class IConfigurationProvider(ABC):
    """Configuration provider interface"""

    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        pass

    @abstractmethod
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        pass

    @abstractmethod
    def reload(self) -> None:
        """Reload configuration"""
        pass


class IMetricsCollector(ABC):
    """Metrics collector interface"""

    @abstractmethod
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric"""
        pass

    @abstractmethod
    async def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics"""
        pass

    @abstractmethod
    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record gauge metric"""
        pass

    @abstractmethod
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record histogram metric"""
        pass

    @abstractmethod
    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record timer metric"""
        pass


class ISecurityProvider(ABC):
    """Security provider interface"""

    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user"""
        pass

    @abstractmethod
    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action"""
        pass

    @abstractmethod
    def hash_password(self, password: str) -> str:
        """Hash password"""
        pass

    @abstractmethod
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password"""
        pass

    @abstractmethod
    def create_token(self, payload: Dict[str, Any]) -> str:
        """Create JWT token"""
        pass

    @abstractmethod
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        pass


@runtime_checkable
class ICircuitBreaker(Protocol):
    """Circuit breaker interface"""

    @property
    def state(self) -> CircuitBreakerState:
        """Current circuit breaker state"""
        ...

    async def call(self, func: Any, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        ...

    def record_success(self) -> None:
        """Record successful call"""
        ...

    def record_failure(self) -> None:
        """Record failed call"""
        ...

    def reset(self) -> None:
        """Reset circuit breaker to closed state"""
        ...


@runtime_checkable
class IScoringEngine(Protocol):
    """Scoring engine interface"""

    async def calculate_score(
        self,
        service_name: str,
        score_type: ScoreType,
        metrics: Dict[str, Any]
    ) -> float:
        """Calculate score for a service"""
        ...

    async def get_composite_score(
        self,
        service_name: str,
        weights: Optional[Dict[ScoreType, float]] = None
    ) -> Dict[str, Any]:
        """Get composite score with breakdown"""
        ...

    async def get_service_ranking(
        self,
        score_type: ScoreType = ScoreType.COMPOSITE
    ) -> List[Dict[str, Any]]:
        """Get ranked list of services by score"""
        ...

    async def update_score_weights(
        self,
        weights: Dict[ScoreType, float]
    ) -> None:
        """Update scoring weights"""
        ...


@runtime_checkable
class IMetricsAggregator(Protocol):
    """Metrics aggregator interface"""

    async def aggregate_metrics(
        self,
        service_name: str,
        time_window: int = 300  # 5 minutes default
    ) -> Dict[str, Any]:
        """Aggregate metrics for a service over time window"""
        ...

    async def get_performance_metrics(
        self,
        service_name: str
    ) -> Dict[str, float]:
        """Get performance metrics for scoring"""
        ...

    async def get_reliability_metrics(
        self,
        service_name: str
    ) -> Dict[str, float]:
        """Get reliability metrics for scoring"""
        ...

    async def get_availability_metrics(
        self,
        service_name: str
    ) -> Dict[str, float]:
        """Get availability metrics for scoring"""
        ...