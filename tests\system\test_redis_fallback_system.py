#!/usr/bin/env python3
"""
Redis Fallback System Tests

This module contains system tests for the Redis fallback mechanisms including:
- Connection pooling and rate limiting
- Memory cache fallback
- Queue system for retrying operations
- Graceful degradation under load
- Service registry with fallback
"""

import asyncio
import os
import time
import pytest
from datetime import datetime, timezone
from typing import Dict, Any

from core.infrastructure.redis_cache import RedisCache
from core.infrastructure.redis_service_registry_v2 import RedisServiceRegistryV2
from core.infrastructure.redis_fallback_manager import RedisLimits, FallbackStrategy


@pytest.mark.system
@pytest.mark.asyncio
async def test_enhanced_cache_fallback():
    """Test enhanced cache with fallback mechanisms"""
    redis_url = os.getenv("REDIS_URL", "redis://default:<EMAIL>:10798")

    # Create cache with conservative limits
    cache = RedisCache(
        redis_url=redis_url,
        fallback_strategy=FallbackStrategy.HYBRID,
        redis_limits=RedisLimits(
            max_connections=20,  # Conservative limit
            max_ops_per_second=50,  # Conservative limit
            max_memory_mb=25
        )
    )

    try:
        await cache.initialize()

        # Test 1: Normal operations
        await cache.set("test_key_1", {"data": "test_value_1"}, ttl=30)
        result = await cache.get("test_key_1")
        assert result is not None
        assert result.get("data") == "test_value_1"

        # Test 2: Stress test to trigger fallback
        start_time = time.time()

        # Perform many operations quickly to test rate limiting
        tasks = []
        for i in range(100):
            tasks.append(cache.set(f"stress_key_{i}", f"stress_value_{i}", ttl=10))

        await asyncio.gather(*tasks, return_exceptions=True)

        stress_time = time.time() - start_time
        assert stress_time < 30  # Should complete within reasonable time

        # Test 3: Verify fallback worked
        health = await cache.get_health_status()
        assert "redis_healthy" in health
        assert "success_rate" in health
        assert "fallback_rate" in health

        # Test 4: Get cache statistics
        stats = await cache.get_cache_stats()
        assert "performance" in stats
        assert "capacity" in stats
        assert "recommendations" in stats

        # Test 5: Verify data integrity after fallback
        test_value = await cache.get("test_key_1")
        assert test_value is not None
        assert test_value.get("data") == "test_value_1"

    finally:
        await cache.dispose()


@pytest.mark.system
@pytest.mark.asyncio
async def test_enhanced_service_registry_fallback():
    """Test enhanced service registry with fallback mechanisms"""
    redis_url = os.getenv("REDIS_URL", "redis://default:<EMAIL>:10798")

    # Create service registry with conservative limits
    registry = RedisServiceRegistryV2(
        redis_url=redis_url,
        fallback_strategy=FallbackStrategy.HYBRID,
        redis_limits=RedisLimits(
            max_connections=15,  # Conservative limit
            max_ops_per_second=40,  # Conservative limit
            max_memory_mb=25
        )
    )

    try:
        await registry.initialize()

        # Test 1: Normal service registration
        result = await registry.register_service(
            name="test-service-fallback",
            url="http://localhost:8001",
            version="1.0.0",
            metadata={"test": "fallback"}
        )
        assert result.get('success', False) is True

        # Test 2: Stress test service operations
        start_time = time.time()

        # Register multiple services quickly to test limits
        registration_tasks = []
        for i in range(20):
            task = registry.register_service(
                name=f"stress-service-{i}",
                url=f"http://localhost:800{i % 10}",
                version="1.0.0",
                metadata={"stress_test": True, "index": i}
            )
            registration_tasks.append(task)

        results = await asyncio.gather(*registration_tasks, return_exceptions=True)
        successful_registrations = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))

        stress_time = time.time() - start_time
        assert stress_time < 30  # Should complete within reasonable time
        assert successful_registrations > 0  # At least some should succeed

        # Test 3: Service discovery with fallback
        services = await registry.list_services()
        assert len(services) >= 1  # At least the test service should be there

        # Test 4: Health status
        health = await registry.get_health_status()
        assert "redis_healthy" in health
        assert "success_rate" in health
        assert "fallback_rate" in health

        # Test 5: Cleanup with fallback
        cleanup_tasks = []
        for i in range(20):
            task = registry.unregister_service(f"stress-service-{i}")
            cleanup_tasks.append(task)

        cleanup_results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        successful_cleanups = sum(1 for r in cleanup_results if isinstance(r, dict) and r.get('success', False))

        # Final cleanup
        await registry.unregister_service("test-service-fallback")

    finally:
        await registry.dispose()


@pytest.mark.system
@pytest.mark.asyncio
async def test_fallback_manager_directly():
    """Test the fallback manager directly"""
    redis_url = os.getenv("REDIS_URL", "redis://default:<EMAIL>:10798")

    from core.infrastructure.redis_fallback_manager import RedisFallbackManager

    # Create fallback manager with very conservative limits
    manager = RedisFallbackManager(
        redis_url=redis_url,
        limits=RedisLimits(
            max_connections=10,
            max_ops_per_second=30,
            max_memory_mb=20
        ),
        fallback_strategy=FallbackStrategy.HYBRID
    )

    try:
        await manager.initialize()

        # Test 1: Normal operations
        await manager.execute_operation("set", "test_key", "test_value", ttl=30)
        result = await manager.execute_operation("get", "test_key", fallback_value=None)
        assert result == "test_value"

        # Test 2: Overwhelm the system
        start_time = time.time()

        # Create many concurrent operations
        tasks = []
        for i in range(150):  # More than our limits
            tasks.append(manager.execute_operation(
                "set",
                f"overload_key_{i}",
                f"overload_value_{i}",
                fallback_value=True,
                ttl=10,
                priority=1
            ))

        results = await asyncio.gather(*tasks, return_exceptions=True)
        successful_ops = sum(1 for r in results if r is True or (isinstance(r, str) and r))

        overload_time = time.time() - start_time
        assert overload_time < 60  # Should complete within reasonable time
        assert successful_ops > 0  # At least some should succeed

        # Test 3: Check health after overload
        health = await manager.get_health_status()
        assert "redis_healthy" in health
        assert "success_rate" in health
        assert "fallback_rate" in health

        # Test 4: Wait for queue processing
        await asyncio.sleep(5)  # Let queue processor work

        final_health = await manager.get_health_status()
        assert "queue_size" in final_health
        assert "memory_cache_size" in final_health

    finally:
        await manager.dispose()


def main():
    """Main entry point for manual testing"""
    async def run_all_tests():
        print("🚀 Redis Fallback System Testing Suite")
        print("=" * 80)
        print(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")

        # Test all components
        await test_fallback_manager_directly()
        await test_enhanced_cache_fallback()
        await test_enhanced_service_registry_fallback()

        print("\n📊 Fallback System Test Summary")
        print("=" * 80)
        print("✅ Fallback Manager: Direct testing completed")
        print("✅ Enhanced Cache: Fallback mechanisms tested")
        print("✅ Enhanced Service Registry: Fallback mechanisms tested")
        print("\n🎯 Your Redis fallback system is production-ready!")
        print("💡 The system gracefully handles Redis Cloud free tier limits")
        print("💡 Memory fallback ensures continued operation during Redis issues")
        print("💡 Queue system retries important operations automatically")

    asyncio.run(run_all_tests())


if __name__ == "__main__":
    main()
