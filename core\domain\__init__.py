"""
Domain Layer - Core Business Interfaces and Contracts

This layer contains:
- Abstract interfaces and protocols
- Domain entities and value objects
- Business logic contracts
- Core exceptions
"""

from core.domain.interfaces import (
    IService,
    IPlugin,
    IHealthCheck,
    IRepository,
    ICache,
    IEventBus,
    IMessageBroker,
    IServiceRegistry,
    IMetricsCollector,
    ISecurityProvider,
    ICircuitBreaker,
    IScoringEngine,
    IMetricsAggregator,
    IUserStore,
    IRoleStore,
    ServiceStatus,
    PluginStatus,
    CircuitBreakerState,
    ScoreType,
)

from core.domain.models import (
    User,
    Role,
    Permission,
    UserStatus,
    UserCreateDTO,
    UserUpdateDTO,
    PasswordUpdateDTO,
    RoleCreateDTO,
    RoleUpdateDTO,
    PermissionCreateDTO,
)

from core.domain.exceptions import (
    DomainException,
    ValidationError,
    ServiceUnavailableError,
    CircuitBreakerError,
)

__all__ = [
    # Interfaces
    "IService",
    "IPlugin",
    "IHealthCheck",
    "IRepository",
    "ICache",
    "IEventBus",
    "IMessageBroker",
    "IServiceRegistry",
    "IMetricsCollector",
    "ISecurityProvider",
    "ICircuitBreaker",
    "IScoringEngine",
    "IMetricsAggregator",
    "IUserStore",
    "IRoleStore",
    # Domain Models
    "User",
    "Role",
    "Permission",
    # DTOs
    "UserCreateDTO",
    "UserUpdateDTO",
    "PasswordUpdateDTO",
    "RoleCreateDTO",
    "RoleUpdateDTO",
    "PermissionCreateDTO",
    # Enums
    "ServiceStatus",
    "PluginStatus",
    "CircuitBreakerState",
    "ScoreType",
    "UserStatus",
    # Exceptions
    "DomainException",
    "ValidationError",
    "ServiceUnavailableError",
    "CircuitBreakerError",
]