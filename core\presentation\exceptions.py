"""
Exception Handlers

This module provides global exception handlers for the FastAPI application
to ensure consistent error responses and proper logging.
"""

import logging
from typing import Any, Dict
from datetime import datetime

from fastapi import FastAP<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from pydantic import ValidationError

logger = logging.getLogger(__name__)


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup global exception handlers for the FastAPI application"""
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(
        request: Request, 
        exc: StarletteHTTPException
    ) -> JSONResponse:
        """Handle HTTP exceptions with proper logging and response format"""
        
        # Log the exception
        logger.warning(
            f"HTTP {exc.status_code} error on {request.method} {request.url.path}: {exc.detail}",
            extra={
                "status_code": exc.status_code,
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request)
            }
        )
        
        return J<PERSON>NResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "type": "http_error",
                    "code": exc.status_code,
                    "message": exc.detail,
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(
        request: Request, 
        exc: RequestValidationError
    ) -> JSONResponse:
        """Handle request validation errors with detailed field information"""
        
        # Log the validation error
        logger.warning(
            f"Validation error on {request.method} {request.url.path}: {len(exc.errors())} errors",
            extra={
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request),
                "validation_errors": exc.errors()
            }
        )
        
        # Format validation errors for better user experience
        formatted_errors = []
        for error in exc.errors():
            field_path = " -> ".join(str(loc) for loc in error["loc"])
            formatted_errors.append({
                "field": field_path,
                "message": error["msg"],
                "type": error["type"],
                "input": error.get("input")
            })
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": {
                    "type": "validation_error",
                    "code": 422,
                    "message": "Request validation failed",
                    "details": formatted_errors,
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
            }
        )
    
    @app.exception_handler(ValidationError)
    async def pydantic_validation_exception_handler(
        request: Request, 
        exc: ValidationError
    ) -> JSONResponse:
        """Handle Pydantic validation errors"""
        
        # Log the validation error
        logger.warning(
            f"Pydantic validation error on {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request),
                "validation_errors": exc.errors()
            }
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": {
                    "type": "validation_error",
                    "code": 422,
                    "message": "Data validation failed",
                    "details": exc.errors(),
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
            }
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(
        request: Request, 
        exc: ValueError
    ) -> JSONResponse:
        """Handle value errors as bad requests"""
        
        # Log the error
        logger.warning(
            f"Value error on {request.method} {request.url.path}: {str(exc)}",
            extra={
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request),
                "error": str(exc)
            }
        )
        
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": {
                    "type": "value_error",
                    "code": 400,
                    "message": str(exc),
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
            }
        )
    
    @app.exception_handler(KeyError)
    async def key_error_handler(
        request: Request, 
        exc: KeyError
    ) -> JSONResponse:
        """Handle key errors as not found"""
        
        # Log the error
        logger.warning(
            f"Key error on {request.method} {request.url.path}: {str(exc)}",
            extra={
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request),
                "missing_key": str(exc)
            }
        )
        
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "error": {
                    "type": "not_found",
                    "code": 404,
                    "message": f"Resource not found: {str(exc)}",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(
        request: Request, 
        exc: Exception
    ) -> JSONResponse:
        """Handle all other exceptions as internal server errors"""
        
        # Log the unexpected error
        logger.error(
            f"Unexpected error on {request.method} {request.url.path}: {str(exc)}",
            extra={
                "method": request.method,
                "path": str(request.url.path),
                "client_ip": _get_client_ip(request),
                "exception_type": type(exc).__name__,
                "exception_message": str(exc)
            },
            exc_info=True  # Include stack trace
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "type": "internal_error",
                    "code": 500,
                    "message": "An unexpected error occurred. Please try again later.",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    # Only include exception details in development
                    "debug": {
                        "exception_type": type(exc).__name__,
                        "exception_message": str(exc)
                    } if __debug__ else None
                }
            }
        )


def _get_client_ip(request: Request) -> str:
    """Extract client IP address from request with proxy support"""
    # Check for forwarded headers first
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fallback to direct client IP
    return request.client.host if request.client else "unknown" 