# Developer Guide: Secret Management with Cloudflare

This guide helps developers understand and implement the FastAPI Core Framework's secret management system powered by Cloudflare Workers KV.

## 🎯 Quick Overview

The FastAPI Core Framework provides **enterprise-grade secret management** with:

- **🚀 Cloudflare Workers KV**: Primary storage (available now)
- **🔮 Cloudflare Secrets Store**: Future upgrade (automatic detection)
- **🔄 Auto-Detection**: Seamlessly chooses the best available option
- **🛡️ Fallback Support**: Environment variables as backup
- **🌍 Global Distribution**: 300+ edge locations worldwide

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                     │
├─────────────────────────────────────────────────────────────┤
│                SecretConfigProvider                        │
│                  (Auto-Detection)                          │
├─────────────────────────────────────────────────────────────┤
│  CloudflareKVSecretProvider  │  CloudflareSecretsStore     │
│         (Primary)            │        (Future)             │
├─────────────────────────────────────────────────────────────┤
│              Environment Variables (Fallback)              │
├─────────────────────────────────────────────────────────────┤
│                 Cloudflare Global Network                  │
│                   (300+ Edge Locations)                    │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Implementation Guide

### 1. Basic Usage

```python
from core.config.secret_providers import SecretConfigProvider

# Initialize (auto-detects best provider)
provider = SecretConfigProvider()

# Store a secret
await provider.set_secret("database-password", "super-secure-password")

# Retrieve a secret (returns string)
password = await provider.get_secret("database-password")

# List all secrets
secrets = await provider.list_secrets()

# Health check
healthy = await provider.health_check()
```

### 2. FastAPI Integration

```python
from fastapi import FastAPI, Depends, HTTPException
from core.config.secret_providers import SecretConfigProvider

app = FastAPI()
secret_provider = SecretConfigProvider()

@app.on_event("startup")
async def startup_event():
    """Initialize secrets on startup"""
    healthy = await secret_provider.health_check()
    if not healthy:
        raise RuntimeError("Secret provider is not healthy")
    
    print("✅ Secret management initialized")

async def get_database_url():
    """Dependency to get database URL"""
    password = await secret_provider.get_secret("database-password")
    if not password:
        raise HTTPException(status_code=500, detail="Database password not found")
    
    return f"postgresql://user:{password}@localhost/db"

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    secret_health = await secret_provider.health_check()
    return {
        "status": "healthy" if secret_health else "unhealthy",
        "secret_provider": "CloudflareKV"
    }
```

### 3. Advanced Configuration

```python
from core.config.secret_providers import CloudflareKVSecretProvider

# Direct provider usage
kv_provider = CloudflareKVSecretProvider(
    account_id="your-account-id",
    api_token="your-api-token",
    namespace_name="production-secrets",  # Custom namespace
    cache_ttl=600  # 10 minutes cache
)

# With metadata
from core.config.providers import SecretMetadata

metadata = SecretMetadata(
    key="api-key",
    tags={"environment": "production", "service": "payment"}
)

await kv_provider.set_secret("api-key", "secret-value", metadata)
```

## 🔄 Migration Strategies

### From Environment Variables

```python
import os
from core.config.secret_providers import SecretConfigProvider

async def migrate_from_env():
    """Migrate secrets from environment variables"""
    provider = SecretConfigProvider()
    
    # Define mapping
    env_to_kv = {
        'DATABASE_PASSWORD': 'database-password',
        'JWT_SECRET_KEY': 'jwt-secret-key',
        'API_KEY': 'api-key',
        'REDIS_PASSWORD': 'redis-password'
    }
    
    for env_var, kv_key in env_to_kv.items():
        value = os.getenv(env_var)
        if value and value not in ['placeholder', 'your-secret-here']:
            success = await provider.set_secret(kv_key, value)
            if success:
                print(f"✅ Migrated {env_var} → {kv_key}")
            else:
                print(f"❌ Failed to migrate {env_var}")

# Run migration
import asyncio
asyncio.run(migrate_from_env())
```

### From Other Secret Stores

```python
async def migrate_from_vault():
    """Migrate from HashiCorp Vault to Cloudflare KV"""
    vault_provider = SecretConfigProvider(provider_type="vault")
    kv_provider = SecretConfigProvider(provider_type="auto")
    
    # Get all secrets from Vault
    vault_secrets = await vault_provider.list_secrets()
    
    for secret_key in vault_secrets:
        # Get from Vault
        value = await vault_provider.get_secret(secret_key)
        
        if value:
            # Store in KV
            success = await kv_provider.set_secret(secret_key, value)
            print(f"{'✅' if success else '❌'} {secret_key}")
```

## 🛡️ Security Best Practices

### 1. API Token Management

```python
# ✅ Good: Use environment variables
CLOUDFLARE_API_TOKEN = os.getenv('CLOUDFLARE_API_TOKEN')

# ❌ Bad: Hardcode in source
CLOUDFLARE_API_TOKEN = "your-token-here"  # Never do this!
```

### 2. Secret Naming Conventions

```python
# ✅ Good: Use kebab-case, descriptive names
await provider.set_secret("database-password", password)
await provider.set_secret("jwt-secret-key", jwt_key)
await provider.set_secret("external-api-key", api_key)

# ❌ Bad: Unclear or inconsistent naming
await provider.set_secret("pwd", password)
await provider.set_secret("JWT_SECRET", jwt_key)
await provider.set_secret("key123", api_key)
```

### 3. Error Handling

```python
async def safe_get_secret(key: str, default: str = None):
    """Safely get a secret with proper error handling"""
    try:
        secret = await provider.get_secret(key)
        if secret:
            return secret
        
        # Log warning but don't expose secret names in logs
        logger.warning(f"Secret not found: {key[:8]}...")
        return default
        
    except Exception as e:
        logger.error(f"Failed to retrieve secret: {e}")
        return default
```

## 📊 Monitoring and Observability

### 1. Health Checks

```python
async def comprehensive_health_check():
    """Comprehensive health check for secret management"""
    provider = SecretConfigProvider()
    
    # Basic health
    basic_health = await provider.health_check()
    
    # Provider info
    info = provider.get_provider_info()
    
    # Test secret operations
    test_key = "health-check-test"
    test_value = "test-value-123"
    
    try:
        # Test set
        set_success = await provider.set_secret(test_key, test_value)
        
        # Test get
        retrieved = await provider.get_secret(test_key)
        get_success = retrieved == test_value
        
        # Test delete
        delete_success = await provider.delete_secret(test_key)
        
        return {
            "healthy": basic_health and set_success and get_success and delete_success,
            "provider": info['provider_class'],
            "operations": {
                "set": set_success,
                "get": get_success,
                "delete": delete_success
            }
        }
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "provider": info.get('provider_class', 'unknown')
        }
```

### 2. Metrics and Logging

```python
import time
import logging
from functools import wraps

logger = logging.getLogger(__name__)

def monitor_secret_operations(func):
    """Decorator to monitor secret operations"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        operation = func.__name__
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            
            logger.info(f"Secret operation {operation} completed in {duration:.3f}s")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Secret operation {operation} failed after {duration:.3f}s: {e}")
            raise
    
    return wrapper

# Usage
@monitor_secret_operations
async def get_database_password():
    return await provider.get_secret("database-password")
```

## 🧪 Testing

### 1. Unit Tests

```python
import pytest
from unittest.mock import AsyncMock, patch
from core.config.secret_providers import SecretConfigProvider

@pytest.mark.asyncio
async def test_secret_operations():
    """Test secret operations"""
    with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
        provider = SecretConfigProvider(provider_type="file")
        
        # Test set
        result = await provider.set_secret("test-key", "test-value")
        assert result is True
        
        # Test get
        value = await provider.get_secret("test-key")
        assert value == "test-value"
        
        # Test list
        secrets = await provider.list_secrets()
        assert "test-key" in secrets
        
        # Test delete
        result = await provider.delete_secret("test-key")
        assert result is True
```

### 2. Integration Tests

```python
@pytest.mark.asyncio
async def test_cloudflare_integration():
    """Test Cloudflare KV integration (requires credentials)"""
    if not os.getenv('CLOUDFLARE_ACCOUNT_ID'):
        pytest.skip("Cloudflare credentials not available")
    
    provider = SecretConfigProvider(provider_type="auto")
    
    # Test health
    healthy = await provider.health_check()
    assert healthy is True
    
    # Test operations
    test_key = f"test-{int(time.time())}"
    test_value = "integration-test-value"
    
    try:
        # Set
        await provider.set_secret(test_key, test_value)
        
        # Get
        retrieved = await provider.get_secret(test_key)
        assert retrieved == test_value
        
    finally:
        # Cleanup
        await provider.delete_secret(test_key)
```

## 🚀 Deployment Guide

### 1. Environment Configuration

```bash
# Production .env
CLOUDFLARE_ACCOUNT_ID=your-production-account-id
CLOUDFLARE_API_TOKEN=your-production-api-token
SECRET_PROVIDER=auto
SECRET_CACHE_TTL=300
FALLBACK_TO_ENV=true

# Optional
CLOUDFLARE_KV_NAMESPACE=production-secrets
```

### 2. Docker Configuration

```dockerfile
# Dockerfile
FROM python:3.12-slim

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY . /app
WORKDIR /app

# Environment variables (use secrets in production)
ENV SECRET_PROVIDER=auto
ENV FALLBACK_TO_ENV=true

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 3. Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fastapi-app
spec:
  template:
    spec:
      containers:
      - name: app
        image: your-app:latest
        env:
        - name: CLOUDFLARE_ACCOUNT_ID
          valueFrom:
            secretKeyRef:
              name: cloudflare-credentials
              key: account-id
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: cloudflare-credentials
              key: api-token
        - name: SECRET_PROVIDER
          value: "auto"
```

## 🔍 Troubleshooting

### Common Issues and Solutions

1. **Provider Not Detected**:
   ```python
   # Check environment variables
   import os
   print(f"Account ID: {os.getenv('CLOUDFLARE_ACCOUNT_ID')}")
   print(f"API Token: {os.getenv('CLOUDFLARE_API_TOKEN')[:10]}...")
   
   # Test provider info
   provider = SecretConfigProvider()
   info = provider.get_provider_info()
   print(f"Provider: {info}")
   ```

2. **Authentication Errors**:
   ```bash
   # Test API token
   curl -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/tokens/verify"
   ```

3. **Performance Issues**:
   ```python
   # Increase cache TTL
   provider = SecretConfigProvider(cache_ttl=600)  # 10 minutes
   
   # Pre-load critical secrets
   await provider.get_secret("database-password")
   await provider.get_secret("jwt-secret-key")
   ```

## 📚 Additional Resources

- [Cloudflare Workers KV Documentation](https://developers.cloudflare.com/workers/runtime-apis/kv/)
- [FastAPI Dependency Injection](https://fastapi.tiangolo.com/tutorial/dependencies/)
- [Secret Management Best Practices](https://owasp.org/www-project-cheat-sheets/cheatsheets/Secrets_Management_Cheat_Sheet.html)

---

**Need help?** Check the [troubleshooting guide](docs/README_CLOUDFLARE_SECRETS.md#troubleshooting) or run the test scripts to diagnose issues.
