"""
Database User Store Implementation

This module provides PostgreSQL-backed implementations of IUserStore and IRoleStore
using SQLAlchemy with async support and proper password hashing.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set
from sqlalchemy import Column, String, Boolean, DateTime, Text, Table, ForeignKey
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, relationship, selectinload
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.exc import IntegrityError

from core.domain.interfaces import IUserStore, IRoleStore
from core.domain.models import User, Role, Permission, UserStatus
from core.domain.exceptions import ValidationError

# SQLAlchemy Base
Base = declarative_base()

# Association tables for many-to-many relationships
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True),
    Column('role_id', UUID(as_uuid=True), Foreign<PERSON>ey('roles.id'), primary_key=True)
)

role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', UUID(as_uuid=True), ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', UUID(as_uuid=True), ForeignKey('permissions.id'), primary_key=True)
)


class UserEntity(Base):
    """SQLAlchemy User entity"""
    __tablename__ = 'users'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    status = Column(String(50), nullable=False, default='active')
    is_superuser = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)

    # Relationships
    roles = relationship("RoleEntity", secondary=user_roles, back_populates="users", lazy="selectin")


class RoleEntity(Base):
    """SQLAlchemy Role entity"""
    __tablename__ = 'roles'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_system_role = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)

    # Relationships
    users = relationship("UserEntity", secondary=user_roles, back_populates="roles")
    permissions = relationship("PermissionEntity", secondary=role_permissions, back_populates="roles", lazy="selectin")


class PermissionEntity(Base):
    """SQLAlchemy Permission entity"""
    __tablename__ = 'permissions'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False, index=True)
    resource = Column(String(100), nullable=False, index=True)
    action = Column(String(50), nullable=False, index=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)

    # Relationships
    roles = relationship("RoleEntity", secondary=role_permissions, back_populates="permissions")


class DatabaseUserStore(IUserStore):
    """PostgreSQL-backed user store implementation"""

    def __init__(self, database_url: str, echo: bool = False):
        self.database_url = database_url
        self.engine = create_async_engine(database_url, echo=echo)
        self.async_session = async_sessionmaker(self.engine, expire_on_commit=False)

    async def create_tables(self) -> None:
        """Create database tables"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

    async def drop_tables(self) -> None:
        """Drop database tables (for testing)"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    def _entity_to_domain(self, entity: UserEntity) -> User:
        """Convert SQLAlchemy entity to domain model"""
        role_ids = {str(role.id) for role in entity.roles} if entity.roles else set()

        return User(
            id=str(entity.id),
            username=entity.username,  # type: ignore
            email=entity.email,  # type: ignore
            password_hash=entity.password_hash,  # type: ignore
            first_name=entity.first_name,  # type: ignore
            last_name=entity.last_name,  # type: ignore
            status=UserStatus(entity.status),  # type: ignore
            is_superuser=entity.is_superuser,  # type: ignore
            is_verified=entity.is_verified,  # type: ignore
            last_login=entity.last_login,  # type: ignore
            created_at=entity.created_at,  # type: ignore
            updated_at=entity.updated_at,  # type: ignore
            role_ids=role_ids
        )

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        async with self.async_session() as session:
            stmt = select(UserEntity).options(selectinload(UserEntity.roles)).where(UserEntity.id == uuid.UUID(user_id))
            result = await session.execute(stmt)
            entity = result.scalar_one_or_none()
            return self._entity_to_domain(entity) if entity else None

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        async with self.async_session() as session:
            stmt = select(UserEntity).options(selectinload(UserEntity.roles)).where(UserEntity.username == username.lower())
            result = await session.execute(stmt)
            entity = result.scalar_one_or_none()
            return self._entity_to_domain(entity) if entity else None

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        async with self.async_session() as session:
            stmt = select(UserEntity).options(selectinload(UserEntity.roles)).where(UserEntity.email == email.lower())
            result = await session.execute(stmt)
            entity = result.scalar_one_or_none()
            return self._entity_to_domain(entity) if entity else None

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create new user"""
        async with self.async_session() as session:
            try:
                # Create user entity
                entity = UserEntity(
                    username=user_data['username'].lower(),
                    email=user_data['email'].lower(),
                    password_hash=user_data['password_hash'],
                    first_name=user_data.get('first_name'),
                    last_name=user_data.get('last_name'),
                    status=user_data.get('status', 'active'),
                    is_superuser=user_data.get('is_superuser', False),
                    is_verified=user_data.get('is_verified', False)
                )

                session.add(entity)
                await session.flush()  # Get the ID

                # Add roles if specified
                if 'role_ids' in user_data and user_data['role_ids']:
                    role_stmt = select(RoleEntity).where(RoleEntity.id.in_([uuid.UUID(rid) for rid in user_data['role_ids']]))
                    role_result = await session.execute(role_stmt)
                    roles = role_result.scalars().all()
                    entity.roles = list(roles)

                await session.commit()
                await session.refresh(entity, ['roles'])
                return self._entity_to_domain(entity)

            except IntegrityError as e:
                await session.rollback()
                if 'username' in str(e):
                    raise ValidationError(f"Username '{user_data['username']}' is already taken")
                elif 'email' in str(e):
                    raise ValidationError(f"Email '{user_data['email']}' is already taken")
                else:
                    raise ValidationError(f"User creation failed: {str(e)}")

    async def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Optional[User]:
        """Update existing user"""
        async with self.async_session() as session:
            try:
                # Get existing user
                stmt = select(UserEntity).options(selectinload(UserEntity.roles)).where(UserEntity.id == uuid.UUID(user_id))
                result = await session.execute(stmt)
                entity = result.scalar_one_or_none()

                if not entity:
                    return None

                # Update fields
                for field, value in user_data.items():
                    if field == 'role_ids':
                        # Handle role updates
                        if value is not None:
                            role_stmt = select(RoleEntity).where(RoleEntity.id.in_([uuid.UUID(rid) for rid in value]))
                            role_result = await session.execute(role_stmt)
                            roles = role_result.scalars().all()
                            entity.roles = list(roles)
                    elif hasattr(entity, field):
                        setattr(entity, field, value)

                entity.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(entity, ['roles'])
                return self._entity_to_domain(entity)

            except IntegrityError as e:
                await session.rollback()
                if 'username' in str(e):
                    raise ValidationError(f"Username is already taken")
                elif 'email' in str(e):
                    raise ValidationError(f"Email is already taken")
                else:
                    raise ValidationError(f"User update failed: {str(e)}")

    async def delete_user(self, user_id: str) -> bool:
        """Delete user by ID"""
        async with self.async_session() as session:
            stmt = delete(UserEntity).where(UserEntity.id == uuid.UUID(user_id))
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def list_users(self, limit: int = 100, offset: int = 0, **filters) -> List[User]:
        """List users with pagination and filters"""
        async with self.async_session() as session:
            stmt = select(UserEntity).options(selectinload(UserEntity.roles))

            # Apply filters
            if 'status' in filters:
                stmt = stmt.where(UserEntity.status == filters['status'])
            if 'is_superuser' in filters:
                stmt = stmt.where(UserEntity.is_superuser == filters['is_superuser'])
            if 'is_verified' in filters:
                stmt = stmt.where(UserEntity.is_verified == filters['is_verified'])
            if 'search' in filters:
                search_term = f"%{filters['search']}%"
                stmt = stmt.where(or_(
                    UserEntity.username.ilike(search_term),
                    UserEntity.email.ilike(search_term),
                    UserEntity.first_name.ilike(search_term),
                    UserEntity.last_name.ilike(search_term)
                ))

            stmt = stmt.offset(offset).limit(limit).order_by(UserEntity.created_at.desc())
            result = await session.execute(stmt)
            entities = result.scalars().all()
            return [self._entity_to_domain(entity) for entity in entities]

    async def verify_password(self, user_id: str, password: str) -> bool:
        """Verify user password - delegated to security provider"""
        # This method should be used in conjunction with a security provider
        # that handles password hashing/verification
        raise NotImplementedError("Password verification should be handled by security provider")

    async def update_password(self, user_id: str, new_password: str) -> bool:
        """Update user password hash (expects already hashed password)"""
        async with self.async_session() as session:
            stmt = update(UserEntity).where(UserEntity.id == uuid.UUID(user_id)).values(
                password_hash=new_password,
                updated_at=datetime.now(timezone.utc)
            )
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def is_username_taken(self, username: str) -> bool:
        """Check if username is already taken"""
        async with self.async_session() as session:
            stmt = select(UserEntity.id).where(UserEntity.username == username.lower())
            result = await session.execute(stmt)
            return result.scalar_one_or_none() is not None

    async def is_email_taken(self, email: str) -> bool:
        """Check if email is already taken"""
        async with self.async_session() as session:
            stmt = select(UserEntity.id).where(UserEntity.email == email.lower())
            result = await session.execute(stmt)
            return result.scalar_one_or_none() is not None


class DatabaseRoleStore(IRoleStore):
    """PostgreSQL-backed role store implementation"""

    def __init__(self, database_url: str, echo: bool = False):
        self.database_url = database_url
        self.engine = create_async_engine(database_url, echo=echo)
        self.async_session = async_sessionmaker(self.engine, expire_on_commit=False)

    def _role_entity_to_domain(self, entity: RoleEntity) -> Role:
        """Convert SQLAlchemy role entity to domain model"""
        permission_ids = {str(perm.id) for perm in entity.permissions} if entity.permissions else set()

        return Role(
            id=str(entity.id),
            name=entity.name,  # type: ignore
            description=entity.description,  # type: ignore
            is_system_role=entity.is_system_role,  # type: ignore
            created_at=entity.created_at,  # type: ignore
            updated_at=entity.updated_at,  # type: ignore
            permission_ids=permission_ids
        )

    def _permission_entity_to_domain(self, entity: PermissionEntity) -> Permission:
        """Convert SQLAlchemy permission entity to domain model"""
        return Permission(
            id=str(entity.id),
            name=entity.name,  # type: ignore
            resource=entity.resource,  # type: ignore
            action=entity.action,  # type: ignore
            description=entity.description,  # type: ignore
            created_at=entity.created_at  # type: ignore
        )

    async def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get role by ID"""
        async with self.async_session() as session:
            stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).where(RoleEntity.id == uuid.UUID(role_id))
            result = await session.execute(stmt)
            entity = result.scalar_one_or_none()
            return self._role_entity_to_domain(entity) if entity else None

    async def get_role_by_name(self, role_name: str) -> Optional[Role]:
        """Get role by name"""
        async with self.async_session() as session:
            stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).where(RoleEntity.name == role_name.lower())
            result = await session.execute(stmt)
            entity = result.scalar_one_or_none()
            return self._role_entity_to_domain(entity) if entity else None

    async def create_role(self, role_data: Dict[str, Any]) -> Role:
        """Create new role"""
        async with self.async_session() as session:
            try:
                # Create role entity
                entity = RoleEntity(
                    name=role_data['name'].lower(),
                    description=role_data.get('description'),
                    is_system_role=role_data.get('is_system_role', False)
                )

                session.add(entity)
                await session.flush()  # Get the ID

                # Add permissions if specified
                if 'permission_ids' in role_data and role_data['permission_ids']:
                    perm_stmt = select(PermissionEntity).where(PermissionEntity.id.in_([uuid.UUID(pid) for pid in role_data['permission_ids']]))
                    perm_result = await session.execute(perm_stmt)
                    permissions = perm_result.scalars().all()
                    entity.permissions = list(permissions)

                await session.commit()
                await session.refresh(entity, ['permissions'])
                return self._role_entity_to_domain(entity)

            except IntegrityError as e:
                await session.rollback()
                raise ValidationError(f"Role name '{role_data['name']}' is already taken")

    async def update_role(self, role_id: str, role_data: Dict[str, Any]) -> Optional[Role]:
        """Update existing role"""
        async with self.async_session() as session:
            try:
                # Get existing role
                stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).where(RoleEntity.id == uuid.UUID(role_id))
                result = await session.execute(stmt)
                entity = result.scalar_one_or_none()

                if not entity:
                    return None

                # Update fields
                for field, value in role_data.items():
                    if field == 'permission_ids':
                        # Handle permission updates
                        if value is not None:
                            perm_stmt = select(PermissionEntity).where(PermissionEntity.id.in_([uuid.UUID(pid) for pid in value]))
                            perm_result = await session.execute(perm_stmt)
                            permissions = perm_result.scalars().all()
                            entity.permissions = list(permissions)
                    elif hasattr(entity, field):
                        setattr(entity, field, value)

                entity.updated_at = datetime.now(timezone.utc)  # type: ignore
                await session.commit()
                await session.refresh(entity, ['permissions'])
                return self._role_entity_to_domain(entity)

            except IntegrityError as e:
                await session.rollback()
                raise ValidationError(f"Role name is already taken")

    async def delete_role(self, role_id: str) -> bool:
        """Delete role by ID"""
        async with self.async_session() as session:
            stmt = delete(RoleEntity).where(RoleEntity.id == uuid.UUID(role_id))
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def list_roles(self, limit: int = 100, offset: int = 0) -> List[Role]:
        """List all roles with pagination"""
        async with self.async_session() as session:
            stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).offset(offset).limit(limit).order_by(RoleEntity.created_at.desc())
            result = await session.execute(stmt)
            entities = result.scalars().all()
            return [self._role_entity_to_domain(entity) for entity in entities]

    async def get_user_roles(self, user_id: str) -> List[Role]:
        """Get all roles assigned to a user"""
        async with self.async_session() as session:
            stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).join(user_roles).where(user_roles.c.user_id == uuid.UUID(user_id))
            result = await session.execute(stmt)
            entities = result.scalars().all()
            return [self._role_entity_to_domain(entity) for entity in entities]

    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """Assign role to user"""
        async with self.async_session() as session:
            try:
                # Get user and role
                user_stmt = select(UserEntity).where(UserEntity.id == uuid.UUID(user_id))
                role_stmt = select(RoleEntity).where(RoleEntity.id == uuid.UUID(role_id))

                user_result = await session.execute(user_stmt)
                role_result = await session.execute(role_stmt)

                user = user_result.scalar_one_or_none()
                role = role_result.scalar_one_or_none()

                if not user or not role:
                    return False

                # Check if already assigned
                if role not in user.roles:
                    user.roles.append(role)
                    user.updated_at = datetime.now(timezone.utc)  # type: ignore
                    await session.commit()

                return True
            except Exception:
                await session.rollback()
                return False

    async def remove_role_from_user(self, user_id: str, role_id: str) -> bool:
        """Remove role from user"""
        async with self.async_session() as session:
            try:
                # Get user with roles
                user_stmt = select(UserEntity).options(selectinload(UserEntity.roles)).where(UserEntity.id == uuid.UUID(user_id))
                user_result = await session.execute(user_stmt)
                user = user_result.scalar_one_or_none()

                if not user:
                    return False

                # Remove role if present
                role_to_remove = None
                for role in user.roles:
                    if str(role.id) == role_id:
                        role_to_remove = role
                        break

                if role_to_remove:
                    user.roles.remove(role_to_remove)
                    user.updated_at = datetime.now(timezone.utc)  # type: ignore
                    await session.commit()

                return True
            except Exception:
                await session.rollback()
                return False

    async def get_role_permissions(self, role_id: str) -> List[str]:
        """Get all permissions for a role"""
        async with self.async_session() as session:
            stmt = select(PermissionEntity).join(role_permissions).where(role_permissions.c.role_id == uuid.UUID(role_id))
            result = await session.execute(stmt)
            permissions = result.scalars().all()
            return [f"{perm.resource}:{perm.action}" for perm in permissions]

    async def add_permission_to_role(self, role_id: str, permission: str) -> bool:
        """Add permission to role"""
        # Parse permission string (format: "resource:action")
        try:
            resource, action = permission.split(':', 1)
        except ValueError:
            raise ValidationError(f"Invalid permission format: {permission}. Expected 'resource:action'")

        async with self.async_session() as session:
            try:
                # Get or create permission
                perm_stmt = select(PermissionEntity).where(
                    and_(PermissionEntity.resource == resource, PermissionEntity.action == action)
                )
                perm_result = await session.execute(perm_stmt)
                perm_entity = perm_result.scalar_one_or_none()

                if not perm_entity:
                    # Create new permission
                    perm_entity = PermissionEntity(
                        name=f"{resource}_{action}",
                        resource=resource,
                        action=action,
                        description=f"Permission to {action} {resource}"
                    )
                    session.add(perm_entity)
                    await session.flush()

                # Get role
                role_stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).where(RoleEntity.id == uuid.UUID(role_id))
                role_result = await session.execute(role_stmt)
                role_entity = role_result.scalar_one_or_none()

                if not role_entity:
                    return False

                # Add permission if not already present
                if perm_entity not in role_entity.permissions:
                    role_entity.permissions.append(perm_entity)
                    role_entity.updated_at = datetime.now(timezone.utc)  # type: ignore
                    await session.commit()

                return True
            except Exception:
                await session.rollback()
                return False

    async def remove_permission_from_role(self, role_id: str, permission: str) -> bool:
        """Remove permission from role"""
        # Parse permission string (format: "resource:action")
        try:
            resource, action = permission.split(':', 1)
        except ValueError:
            raise ValidationError(f"Invalid permission format: {permission}. Expected 'resource:action'")

        async with self.async_session() as session:
            try:
                # Get role with permissions
                role_stmt = select(RoleEntity).options(selectinload(RoleEntity.permissions)).where(RoleEntity.id == uuid.UUID(role_id))
                role_result = await session.execute(role_stmt)
                role_entity = role_result.scalar_one_or_none()

                if not role_entity:
                    return False

                # Find and remove permission
                perm_to_remove = None
                for perm in role_entity.permissions:
                    if perm.resource == resource and perm.action == action:
                        perm_to_remove = perm
                        break

                if perm_to_remove:
                    role_entity.permissions.remove(perm_to_remove)
                    role_entity.updated_at = datetime.now(timezone.utc)  # type: ignore
                    await session.commit()

                return True
            except Exception:
                await session.rollback()
                return False
