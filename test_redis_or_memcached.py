#!/usr/bin/env python3
"""
Red<PERSON> vs Memcached Detection and Connection Test

This script tests whether your instance is Redis or Memcached and establishes the correct connection.
"""

import asyncio
import socket
import sys


async def test_raw_connection():
    """Test raw TCP connection to determine service type"""
    print("🔍 Testing Raw TCP Connection")
    print("=" * 50)
    
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    try:
        print(f"Connecting to {hostname}:{port}...")
        
        # Test TCP connection
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(hostname, port),
            timeout=10.0
        )
        
        print("✅ TCP connection successful")
        
        # Try Redis PING command
        print("\n🔧 Testing Redis protocol...")
        try:
            writer.write(b"PING\r\n")
            await writer.drain()
            
            response = await asyncio.wait_for(reader.read(1024), timeout=5.0)
            print(f"Redis PING response: {response}")
            
            if b"PONG" in response:
                print("✅ This appears to be a Redis server!")
                writer.close()
                await writer.wait_closed()
                return "redis"
            else:
                print("❌ No Redis PONG response")
        except Exception as e:
            print(f"❌ Redis protocol test failed: {e}")
        
        # Try Memcached stats command
        print("\n🔧 Testing Memcached protocol...")
        try:
            writer.write(b"stats\r\n")
            await writer.drain()
            
            response = await asyncio.wait_for(reader.read(1024), timeout=5.0)
            print(f"Memcached stats response: {response}")
            
            if b"STAT" in response:
                print("✅ This appears to be a Memcached server!")
                writer.close()
                await writer.wait_closed()
                return "memcached"
            else:
                print("❌ No Memcached STAT response")
        except Exception as e:
            print(f"❌ Memcached protocol test failed: {e}")
        
        writer.close()
        await writer.wait_closed()
        
    except Exception as e:
        print(f"❌ TCP connection failed: {e}")
    
    return "unknown"


async def test_redis_connection():
    """Test Redis connection with various methods"""
    print("\n🔴 Testing Redis Connection Methods")
    print("=" * 50)
    
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    # Test methods for Redis
    redis_methods = [
        {
            "name": "No authentication",
            "config": {"host": hostname, "port": port, "decode_responses": True}
        },
        {
            "name": "With default/null auth",
            "config": {"host": hostname, "port": port, "decode_responses": True, "username": "default", "password": "null"}
        },
        {
            "name": "With password only",
            "config": {"host": hostname, "port": port, "decode_responses": True, "password": "null"}
        },
        {
            "name": "SSL connection",
            "config": {"host": hostname, "port": port, "decode_responses": True, "ssl": True, "ssl_check_hostname": False}
        }
    ]
    
    for method in redis_methods:
        print(f"\n   Testing: {method['name']}")
        try:
            import redis
            
            r = redis.Redis(**method['config'])
            
            # Test ping
            pong = r.ping()
            print(f"      Ping result: {pong}")
            
            # Test set/get
            r.set('test_key', 'test_value', ex=30)
            value = r.get('test_key')
            print(f"      Set/Get result: {value}")
            
            if value == 'test_value':
                print(f"      ✅ {method['name']} working!")
                
                # Get Redis info
                info = r.info()
                print(f"      Redis version: {info.get('redis_version', 'unknown')}")
                
                # Cleanup
                r.delete('test_key')
                r.close()
                return method['config']
            else:
                print(f"      ❌ {method['name']} failed - wrong value")
                r.close()
                
        except Exception as e:
            print(f"      ❌ {method['name']} failed: {e}")
    
    return None


async def test_memcached_connection():
    """Test Memcached connection"""
    print("\n🟡 Testing Memcached Connection")
    print("=" * 50)
    
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    try:
        # Install pymemcache if not available
        try:
            import pymemcache
        except ImportError:
            print("Installing pymemcache...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pymemcache"])
            import pymemcache
        
        from pymemcache.client.base import Client
        
        print(f"Connecting to Memcached at {hostname}:{port}...")
        
        # Test basic connection
        client = Client((hostname, port))
        
        # Test set/get
        client.set('test_key', 'test_value', expire=30)
        value = client.get('test_key')
        print(f"Set/Get result: {value}")
        
        if value == b'test_value' or value == 'test_value':
            print("✅ Memcached connection working!")
            
            # Test stats
            stats = client.stats()
            print(f"Memcached stats: {stats}")
            
            # Cleanup
            client.delete('test_key')
            client.close()
            return True
        else:
            print("❌ Memcached connection failed - wrong value")
            client.close()
            
    except Exception as e:
        print(f"❌ Memcached connection failed: {e}")
    
    return False


async def test_async_redis():
    """Test async Redis connection"""
    print("\n🔴 Testing Async Redis Connection")
    print("=" * 50)
    
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    try:
        import redis.asyncio as aioredis
        
        # Test different async connection methods
        async_methods = [
            f"redis://{hostname}:{port}",
            f"redis://default:null@{hostname}:{port}",
            f"rediss://{hostname}:{port}",  # SSL
        ]
        
        for url in async_methods:
            print(f"\n   Testing URL: {url}")
            try:
                redis_client = await aioredis.from_url(url, decode_responses=True)
                
                # Test ping
                pong = await redis_client.ping()
                print(f"      Ping result: {pong}")
                
                # Test set/get
                await redis_client.set('async_test', 'async_value', ex=30)
                value = await redis_client.get('async_test')
                print(f"      Set/Get result: {value}")
                
                if value == 'async_value':
                    print(f"      ✅ Async Redis working with URL: {url}")
                    
                    # Cleanup
                    await redis_client.delete('async_test')
                    await redis_client.close()
                    return url
                else:
                    print(f"      ❌ Wrong value returned")
                    await redis_client.close()
                    
            except Exception as e:
                print(f"      ❌ Failed: {e}")
    
    except Exception as e:
        print(f"❌ Async Redis test failed: {e}")
    
    return None


async def update_configuration(service_type, config):
    """Update .env file with working configuration"""
    print(f"\n📝 Updating Configuration")
    print("=" * 50)
    
    try:
        # Read existing .env file
        with open(".env", "r") as f:
            content = f.read()
        
        lines = content.split('\n')
        
        if service_type == "redis":
            if isinstance(config, dict):
                # Redis sync config
                if config.get('username') and config.get('password'):
                    redis_url = f"redis://{config['username']}:{config['password']}@{config['host']}:{config['port']}"
                elif config.get('password'):
                    redis_url = f"redis://:{config['password']}@{config['host']}:{config['port']}"
                else:
                    redis_url = f"redis://{config['host']}:{config['port']}"
            else:
                # Redis async URL
                redis_url = config
            
            # Update REDIS_URL
            for i, line in enumerate(lines):
                if line.startswith('REDIS_URL='):
                    lines[i] = f"REDIS_URL={redis_url}"
                    break
            else:
                lines.append(f"REDIS_URL={redis_url}")
            
            print(f"✅ Updated REDIS_URL to: {redis_url}")
            
        elif service_type == "memcached":
            # Add Memcached configuration
            hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
            port = 10401
            
            for i, line in enumerate(lines):
                if line.startswith('MEMCACHED_URL='):
                    lines[i] = f"MEMCACHED_URL={hostname}:{port}"
                    break
            else:
                lines.append(f"MEMCACHED_URL={hostname}:{port}")
            
            print(f"✅ Updated MEMCACHED_URL to: {hostname}:{port}")
        
        # Write back to .env
        with open(".env", "w") as f:
            f.write('\n'.join(lines))
        
        print("✅ Configuration file updated successfully")
        
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")


async def main():
    """Main test function"""
    print("🚀 Redis vs Memcached Detection and Connection Test")
    print("=" * 70)
    
    # Step 1: Detect service type
    service_type = await test_raw_connection()
    
    working_config = None
    
    if service_type == "redis" or service_type == "unknown":
        # Test Redis connections
        redis_config = await test_redis_connection()
        if redis_config:
            working_config = redis_config
            service_type = "redis"
        else:
            # Try async Redis
            async_redis_url = await test_async_redis()
            if async_redis_url:
                working_config = async_redis_url
                service_type = "redis"
    
    if service_type == "memcached" or (service_type == "unknown" and not working_config):
        # Test Memcached connection
        memcached_success = await test_memcached_connection()
        if memcached_success:
            working_config = True
            service_type = "memcached"
    
    # Summary
    print(f"\n📊 Test Results")
    print("=" * 70)
    
    if working_config:
        print(f"🎉 Success! Your instance is: {service_type.upper()}")
        print(f"✅ Connection established successfully")
        
        # Update configuration
        await update_configuration(service_type, working_config)
        
        print(f"\n🔧 Next Steps:")
        if service_type == "redis":
            print(f"   • Your Redis instance is ready to use")
            print(f"   • Run: python test_redis_setup.py")
            print(f"   • Your application can now use Redis for caching and service registry")
        else:
            print(f"   • Your Memcached instance is ready to use")
            print(f"   • Note: Memcached doesn't support all Redis features")
            print(f"   • You may need to use a different caching strategy")
        
    else:
        print(f"❌ Failed to connect to the instance")
        print(f"Service type detected: {service_type}")
        print(f"\n🔧 Troubleshooting:")
        print(f"   • Check if the instance is running in your dashboard")
        print(f"   • Verify network connectivity")
        print(f"   • Check firewall and security group settings")
        print(f"   • Ensure your IP is whitelisted")
    
    return working_config is not None


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
