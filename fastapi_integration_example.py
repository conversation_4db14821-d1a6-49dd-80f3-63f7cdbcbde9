#!/usr/bin/env python3
"""
FastAPI Integration Example with Cloudflare KV Secrets
"""

from fastapi import FastAP<PERSON>, Depends, HTTPException
from sqlalchemy import create_engine
import jwt
from core.config.secret_providers import SecretConfigProvider

app = FastAPI(title="FastAPI with Cloudflare KV Secrets")

# Global secret provider (auto-detects Cloudflare KV)
secret_provider = SecretConfigProvider()

async def get_database_url():
    """Get database connection URL using secrets"""
    db_password = await secret_provider.get_secret("database-password")
    if not db_password:
        raise HTTPException(status_code=500, detail="Database password not found")
    
    # Build connection URL
    db_host = "localhost"  # From environment or config
    db_user = "laneswap_user"  # From environment or config
    db_name = "laneswap_db"  # From environment or config
    
    return f"postgresql://{db_user}:{db_password}@{db_host}/{db_name}"

async def get_jwt_secret():
    """Get JWT secret key"""
    jwt_secret = await secret_provider.get_secret("jwt-secret-key")
    if not jwt_secret:
        raise HTTPException(status_code=500, detail="JWT secret not found")
    return jwt_secret

async def get_api_key():
    """Get external API key"""
    api_key = await secret_provider.get_secret("api-key")
    if not api_key:
        raise HTTPException(status_code=500, detail="API key not found")
    return api_key

@app.on_event("startup")
async def startup_event():
    """Initialize application with secrets"""
    print("🚀 Starting FastAPI with Cloudflare KV Secrets...")
    
    # Test secret provider health
    healthy = await secret_provider.health_check()
    if healthy:
        print("✅ Secret provider is healthy")
    else:
        print("⚠️  Secret provider health check failed")
    
    # Initialize database connection
    try:
        db_url = await get_database_url()
        print("✅ Database connection configured")
        # engine = create_engine(db_url)  # Uncomment when ready
    except Exception as e:
        print(f"❌ Database configuration failed: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "FastAPI with Cloudflare KV Secrets", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    secret_health = await secret_provider.health_check()
    return {
        "status": "healthy" if secret_health else "unhealthy",
        "secret_provider": "CloudflareKV",
        "secret_health": secret_health
    }

@app.get("/secrets/list")
async def list_secrets():
    """List available secrets (admin endpoint)"""
    try:
        secrets = await secret_provider.list_secrets()
        return {
            "total_secrets": len(secrets),
            "secrets": secrets
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list secrets: {e}")

@app.post("/auth/token")
async def create_token(user_data: dict):
    """Create JWT token using secret from KV"""
    try:
        jwt_secret = await get_jwt_secret()
        
        # Create JWT token
        token = jwt.encode(user_data, jwt_secret, algorithm="HS256")
        
        return {"access_token": token, "token_type": "bearer"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token creation failed: {e}")

@app.get("/external-api/data")
async def get_external_data():
    """Example of using external API key from secrets"""
    try:
        api_key = await get_api_key()
        
        # Use the API key for external service
        # response = requests.get("https://api.example.com/data", 
        #                        headers={"Authorization": f"Bearer {api_key}"})
        
        return {
            "message": "External API call would use secret API key",
            "api_key_available": bool(api_key)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"External API call failed: {e}")

if __name__ == "__main__":
    import uvicorn
    print("🌟 FastAPI with Cloudflare KV Secrets")
    print("📋 Available endpoints:")
    print("   GET  /              - Root endpoint")
    print("   GET  /health        - Health check")
    print("   GET  /secrets/list  - List secrets")
    print("   POST /auth/token    - Create JWT token")
    print("   GET  /external-api/data - External API example")
    print("\n🚀 Starting server...")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
