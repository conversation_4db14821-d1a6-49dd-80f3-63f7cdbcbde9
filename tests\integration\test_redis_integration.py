#!/usr/bin/env python3
"""
Comprehensive Redis Integration Tests

This module provides comprehensive tests for Redis functionality including:
- Basic connectivity and operations
- Service registry functionality
- Cache operations
- Application factory integration
- Production-ready scenarios
"""

import asyncio
import os
import pytest
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from core.infrastructure.redis_service_registry import RedisServiceRegistry
from core.infrastructure.cache import RedisCache
from core.config.settings import Settings
from core.application.factory import ApplicationFactory


class TestRedisConnectivity:
    """Test basic Redis connectivity and operations"""

    @pytest.fixture
    def redis_url(self) -> str:
        """Get Redis URL from environment or use default"""
        return os.getenv(
            "REDIS_URL",
            "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
        )

    @pytest.mark.asyncio
    async def test_basic_redis_connection(self, redis_url: str):
        """Test basic Redis connectivity"""
        try:
            import redis.asyncio as aioredis

            redis_client = await aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True
            )

            # Test basic operations
            test_key = "test_connectivity"
            test_value = "redis_working"

            await redis_client.set(test_key, test_value, ex=10)
            retrieved_value = await redis_client.get(test_key)

            assert retrieved_value == test_value, "Redis set/get operation failed"

            # Cleanup
            await redis_client.delete(test_key)
            await redis_client.close()

        except Exception as e:
            pytest.fail(f"Redis connection failed: {e}")

    @pytest.mark.asyncio
    async def test_redis_operations(self, redis_url: str):
        """Test various Redis operations"""
        try:
            import redis.asyncio as aioredis

            redis_client = await aioredis.from_url(redis_url)

            # Test string operations
            await redis_client.set("test_string", "value", ex=30)
            assert await redis_client.get("test_string") == b"value"

            # Test hash operations
            await redis_client.hset("test_hash", "field1", "value1")
            await redis_client.hset("test_hash", "field2", "value2")
            hash_data = await redis_client.hgetall("test_hash")
            assert hash_data[b"field1"] == b"value1"
            assert hash_data[b"field2"] == b"value2"

            # Test list operations
            await redis_client.lpush("test_list", "item1", "item2")
            list_length = await redis_client.llen("test_list")
            assert list_length == 2

            # Test TTL operations
            await redis_client.set("test_ttl", "value", ex=5)
            ttl = await redis_client.ttl("test_ttl")
            assert 0 < ttl <= 5

            # Cleanup
            await redis_client.delete("test_string", "test_hash", "test_list", "test_ttl")
            await redis_client.close()

        except Exception as e:
            pytest.fail(f"Redis operations failed: {e}")


class TestRedisServiceRegistry:
    """Test Redis Service Registry functionality"""

    @pytest.fixture
    def redis_url(self) -> str:
        return os.getenv(
            "REDIS_URL",
            "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
        )

    @pytest.fixture
    async def service_registry(self, redis_url: str) -> RedisServiceRegistry:
        """Create a test service registry"""
        registry = RedisServiceRegistry(
            redis_url=redis_url,
            key_prefix="test_service_registry:",
            default_ttl=60,  # 1 minute for testing
            health_check_interval=10
        )
        yield registry
        await registry.dispose()

    @pytest.mark.asyncio
    async def test_service_registration(self, service_registry: RedisServiceRegistry):
        """Test service registration functionality"""
        service_name = "test-service"
        service_url = "http://localhost:8001"
        service_version = "1.0.0"

        # Register service
        result = await service_registry.register_service(
            name=service_name,
            url=service_url,
            version=service_version,
            metadata={"test": True}
        )

        assert result["success"] is True
        assert "api_key" in result
        assert result["service"] == service_name

        # Verify service exists
        service = await service_registry.get_service(service_name)
        assert service is not None
        assert service["name"] == service_name
        assert service["url"] == service_url
        assert service["version"] == service_version

        # Cleanup
        await service_registry.unregister_service(service_name)

    @pytest.mark.asyncio
    async def test_service_heartbeat(self, service_registry: RedisServiceRegistry):
        """Test service heartbeat functionality"""
        service_name = "heartbeat-test-service"

        # Register service
        await service_registry.register_service(
            name=service_name,
            url="http://localhost:8002",
            version="1.0.0"
        )

        # Send heartbeat
        result = await service_registry.heartbeat(service_name)
        assert result["success"] is True
        assert result["service"] == service_name
        assert "next_heartbeat" in result

        # Cleanup
        await service_registry.unregister_service(service_name)

    @pytest.mark.asyncio
    async def test_service_listing(self, service_registry: RedisServiceRegistry):
        """Test service listing functionality"""
        # Register multiple services
        services_to_register = [
            ("list-test-service-1", "http://localhost:8001"),
            ("list-test-service-2", "http://localhost:8002"),
            ("list-test-service-3", "http://localhost:8003")
        ]

        for name, url in services_to_register:
            await service_registry.register_service(
                name=name,
                url=url,
                version="1.0.0"
            )

        # List services
        services = await service_registry.list_services()
        registered_names = [s["name"] for s in services]

        for name, _ in services_to_register:
            assert name in registered_names

        # Cleanup
        for name, _ in services_to_register:
            await service_registry.unregister_service(name)


class TestRedisCache:
    """Test Redis Cache functionality"""

    @pytest.fixture
    def redis_url(self) -> str:
        return os.getenv(
            "REDIS_URL",
            "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
        )

    @pytest.fixture
    async def redis_cache(self, redis_url: str) -> RedisCache:
        """Create a test Redis cache"""
        cache = RedisCache(redis_url=redis_url, key_prefix="test_cache:")
        yield cache
        # Cleanup is handled by Redis TTL

    @pytest.mark.asyncio
    async def test_cache_operations(self, redis_cache: RedisCache):
        """Test basic cache operations"""
        test_key = "test_cache_key"
        test_value = {"data": "test_value", "number": 42}

        # Test set and get
        await redis_cache.set(test_key, test_value, ttl=30)
        retrieved_value = await redis_cache.get(test_key)

        assert retrieved_value == test_value

        # Test exists
        exists = await redis_cache.exists(test_key)
        assert exists is True

        # Test delete
        await redis_cache.delete(test_key)
        exists_after_delete = await redis_cache.exists(test_key)
        assert exists_after_delete is False

    @pytest.mark.asyncio
    async def test_cache_ttl(self, redis_cache: RedisCache):
        """Test cache TTL functionality"""
        test_key = "test_ttl_key"
        test_value = "ttl_test_value"

        # Set with short TTL
        await redis_cache.set(test_key, test_value, ttl=2)

        # Should exist immediately
        assert await redis_cache.exists(test_key) is True

        # Wait for expiration
        await asyncio.sleep(3)

        # Should not exist after TTL
        assert await redis_cache.exists(test_key) is False


class TestApplicationIntegration:
    """Test Redis integration with the application factory"""

    @pytest.mark.asyncio
    async def test_application_factory_redis_integration(self):
        """Test that ApplicationFactory properly integrates with Redis"""
        # Set Redis URL in environment
        redis_url = os.getenv(
            "REDIS_URL",
            "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
        )

        # Create settings with Redis URL
        settings = Settings()
        settings.cache.redis_url = redis_url

        # Create application factory
        factory = ApplicationFactory(settings)

        # Verify Redis cache is configured
        from core.domain.interfaces import ICache
        cache = factory.container.get(ICache)

        assert cache is not None
        assert isinstance(cache, RedisCache)

        # Test cache functionality through DI container
        test_key = "di_test_key"
        test_value = {"integration": "test"}

        await cache.set(test_key, test_value, ttl=30)
        retrieved_value = await cache.get(test_key)

        assert retrieved_value == test_value

        # Cleanup
        await cache.delete(test_key)


@pytest.mark.asyncio
async def test_redis_performance():
    """Test Redis performance with multiple operations"""
    redis_url = os.getenv(
        "REDIS_URL",
        "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
    )

    try:
        import redis.asyncio as aioredis

        redis_client = await aioredis.from_url(redis_url)

        # Performance test: multiple concurrent operations
        async def set_operation(i: int):
            await redis_client.set(f"perf_test_{i}", f"value_{i}", ex=30)

        async def get_operation(i: int):
            return await redis_client.get(f"perf_test_{i}")

        # Set 100 keys concurrently
        start_time = asyncio.get_event_loop().time()
        await asyncio.gather(*[set_operation(i) for i in range(100)])
        set_time = asyncio.get_event_loop().time() - start_time

        # Get 100 keys concurrently
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(*[get_operation(i) for i in range(100)])
        get_time = asyncio.get_event_loop().time() - start_time

        # Verify all operations succeeded
        assert len(results) == 100
        assert all(result == f"value_{i}".encode() for i, result in enumerate(results))

        # Performance assertions (adjust based on your requirements)
        assert set_time < 5.0, f"Set operations took too long: {set_time}s"
        assert get_time < 5.0, f"Get operations took too long: {get_time}s"

        # Cleanup
        await redis_client.delete(*[f"perf_test_{i}" for i in range(100)])
        await redis_client.close()

    except Exception as e:
        pytest.fail(f"Redis performance test failed: {e}")


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
