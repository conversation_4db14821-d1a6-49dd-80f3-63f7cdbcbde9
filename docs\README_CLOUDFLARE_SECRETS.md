# Cloudflare Secret Management Integration

This FastAPI Core Framework provides **enterprise-grade secret management** using **Cloudflare's global infrastructure** with automatic detection and seamless upgrades.

## 🌟 Overview

The framework supports **two Cloudflare secret storage options** with intelligent auto-detection:

### 🚀 **Cloudflare Workers KV** (Current - Production Ready)
- ✅ **Available Now**: No beta access required
- ✅ **Global Distribution**: 300+ edge locations worldwide
- ✅ **High Performance**: Sub-millisecond access times
- ✅ **Cost Effective**: No additional charges
- ✅ **Battle Tested**: Used by millions of applications

### 🔮 **Cloudflare Secrets Store** (Future - Beta)
- 🔄 **Auto-Upgrade**: Seamlessly switches when available
- 🔒 **Enhanced Security**: Additional enterprise features
- 📊 **Rich Metadata**: Advanced versioning and audit trails

## 🚀 Quick Setup

### 1. Create Cloudflare API Token

1. Go to [Cloudflare Dashboard → API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. Click **"Create Token"** → **"Custom token"**
3. **Set Permissions**:
   - `Account` → `Account Settings` → `Read`
   - `Account` → `Workers KV Storage` → `Edit`
   - `Account` → `Workers R2 Data Catalog` → `Edit` (optional)
4. **Set Account Resources**: Include your account
5. **Create Token** and **save it securely**

### 2. Get Your Account ID

- Found in the **right sidebar** of any Cloudflare dashboard page
- Or from the URL when viewing your account

### 3. Configure Environment Variables

```bash
# Required - Add to your .env file
CLOUDFLARE_ACCOUNT_ID=your-account-id-here
CLOUDFLARE_API_TOKEN=your-api-token-here

# Optional - KV namespace (defaults to "secrets")
CLOUDFLARE_KV_NAMESPACE=secrets

# Secret Management Configuration
SECRET_PROVIDER=auto
SECRET_CACHE_TTL=300
FALLBACK_TO_ENV=true
```

### 4. Test the Integration

```bash
# Test the connection
python test_cloudflare_kv.py

# Or use the setup script
python setup_secret_management.py
# Select option 6: Test Auto-Detection
```

### 5. Start Using Secrets

```python
from core.config.secret_providers import SecretConfigProvider

# Auto-detects best Cloudflare option (KV → Secrets Store)
provider = SecretConfigProvider()

# Store a secret
await provider.set_secret("database-password", "your-secure-password")

# Retrieve a secret (returns string value)
password = await provider.get_secret("database-password")

# List all secrets
secrets = await provider.list_secrets()

# Check provider health
healthy = await provider.health_check()
```

## 🔧 Configuration Options

### Auto-Detection Priority

The system automatically detects the best available provider:

1. **🚀 Cloudflare Workers KV** (if `CLOUDFLARE_ACCOUNT_ID` and `CLOUDFLARE_API_TOKEN` are set)
2. **🔮 Cloudflare Secrets Store** (fallback when KV fails, future primary)
3. **🏛️ HashiCorp Vault** (if `VAULT_ADDR` is configured)
4. **☁️ AWS Secrets Manager** (if AWS credentials available)
5. **🔑 Azure Key Vault** (if Azure credentials available)
6. **📁 Encrypted File** (development fallback)

### Manual Configuration

```python
# Force specific Cloudflare provider
from core.config.secret_providers import CloudflareKVSecretProvider

provider = CloudflareKVSecretProvider(
    account_id="your-account-id",
    api_token="your-api-token",
    namespace_name="secrets"  # Optional, defaults to "secrets"
)

# Or use the unified provider with auto-detection
provider = SecretConfigProvider(provider_type="auto")
```

### Environment Variables

```bash
# Core Configuration
SECRET_PROVIDER=auto                    # auto, cloudflare, vault, aws, azure, file
SECRET_CACHE_TTL=300                   # Cache TTL in seconds
FALLBACK_TO_ENV=true                   # Fallback to environment variables

# Cloudflare Configuration
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_KV_NAMESPACE=secrets        # Optional, defaults to "secrets"
```

## 🌟 Benefits of Cloudflare Integration

### 🚀 **Workers KV Benefits**
- **✅ Available Now**: No beta access or special permissions required
- **🌍 Global Distribution**: 300+ edge locations worldwide
- **⚡ High Performance**: Sub-millisecond access times globally
- **💰 Cost Effective**: No additional charges for KV usage
- **🔒 Secure**: Encrypted at rest and in transit
- **📈 Scalable**: Handles millions of requests per second

### 🔮 **Future Secrets Store Benefits**
- **🔄 Seamless Upgrade**: Automatic detection and migration
- **🔐 Enhanced Security**: Additional enterprise security features
- **📊 Rich Metadata**: Advanced versioning and audit trails
- **🛡️ RBAC Support**: Fine-grained access control and permissions
- **📝 Audit Logging**: Complete audit trail of secret access

## 🔄 Migration and Integration

### From Environment Variables

Use the built-in migration script:

```bash
# Automated migration
python migrate_secrets_to_kv.py

# Manual migration
python -c "
import asyncio
from core.config.secret_providers import SecretConfigProvider

async def migrate():
    provider = SecretConfigProvider()

    # Migrate your secrets
    secrets = {
        'database-password': 'your-db-password',
        'jwt-secret-key': 'your-jwt-secret',
        'api-key': 'your-api-key'
    }

    for key, value in secrets.items():
        await provider.set_secret(key, value)
        print(f'✅ Migrated {key}')

asyncio.run(migrate())
"
```

### From HashiCorp Vault

```python
# Export from Vault and import to Cloudflare KV
vault_provider = SecretConfigProvider(provider_type="vault")
cf_provider = SecretConfigProvider(provider_type="auto")  # Uses KV

secrets = await vault_provider.list_secrets()
for secret_key in secrets:
    value = await vault_provider.get_secret(secret_key)
    await cf_provider.set_secret(secret_key, value)
    print(f"✅ Migrated {secret_key}")
```

### FastAPI Integration

```python
# In your FastAPI application
from fastapi import FastAPI, Depends
from core.config.secret_providers import SecretConfigProvider

app = FastAPI()
secret_provider = SecretConfigProvider()  # Auto-detects KV

@app.on_event("startup")
async def startup():
    # Test secret provider health
    healthy = await secret_provider.health_check()
    print(f"Secret provider healthy: {healthy}")

async def get_db_password():
    return await secret_provider.get_secret("database-password")

@app.get("/")
async def root(db_password: str = Depends(get_db_password)):
    # Use the secret in your application
    return {"status": "connected", "db_configured": bool(db_password)}
```

## 🛠️ Development vs Production

### Development Environment

- **Fallback Support**: Uses environment variables if Cloudflare not configured
- **Local Testing**: Easy testing with file-based secrets
- **Auto-Detection**: Automatically switches to KV when credentials available
- **Debug Mode**: Detailed logging for troubleshooting

### Production Environment

- **Cloudflare KV**: Primary secret storage with global distribution
- **Environment Fallback**: Graceful degradation to environment variables
- **Health Monitoring**: Continuous health checks and monitoring
- **Caching**: Intelligent caching for optimal performance

## 📊 Monitoring and Health Checks

```python
# Check provider health
healthy = await provider.health_check()

# Get provider information
info = provider.get_provider_info()
print(f"Using: {info['provider_class']}")
print(f"Healthy: {info['healthy']}")
```

## 🔐 Security Best Practices

1. **API Token Security**:
   - Use tokens with minimal required permissions
   - Rotate tokens regularly
   - Store tokens securely (not in code)

2. **Access Control**:
   - Use RBAC to limit secret access
   - Implement least-privilege principles
   - Monitor access patterns

3. **Secret Rotation**:
   - Implement regular secret rotation
   - Use versioning for rollback capability
   - Automate rotation where possible

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Failed**:

   ```bash
   # Test API token
   curl -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/tokens/verify"

   # Test KV access
   curl -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/storage/kv/namespaces"
   ```

2. **Permission Denied**:
   - Check API token permissions (Account Settings: Read, Workers KV Storage: Edit)
   - Verify account ID is correct
   - Ensure token is not expired

3. **KV Namespace Issues**:
   - Namespace is created automatically
   - Check namespace exists: `python test_cloudflare_kv.py`
   - Verify account has KV access

4. **Network Issues**:
   - Check firewall rules for api.cloudflare.com
   - Verify DNS resolution
   - Test with: `ping api.cloudflare.com`

### Debug Mode

```python
import logging

# Enable debug logging
logging.getLogger('CloudflareKVSecretProvider').setLevel(logging.DEBUG)
logging.getLogger('SecretConfigProvider').setLevel(logging.DEBUG)

# Test with debug info
provider = SecretConfigProvider()
info = provider.get_provider_info()
print(f"Provider: {info}")
```

### Testing Scripts

```bash
# Test KV integration
python test_cloudflare_kv.py

# Test auto-detection
python simple_kv_test.py

# Test migration
python migrate_secrets_to_kv.py

# Full integration test
python example_usage.py
```

## 📚 Additional Resources

- **Cloudflare Workers KV**: [Documentation](https://developers.cloudflare.com/workers/runtime-apis/kv/)
- **API Reference**: [KV API](https://developers.cloudflare.com/api/operations/workers-kv-namespace-list-namespaces)
- **Security Best Practices**: [Cloudflare Security](https://developers.cloudflare.com/fundamentals/security/)
- **Framework Documentation**: [Secret Management](docs/SECRET_MANAGEMENT.md)
- **Migration Guide**: [Migration Scripts](migrate_secrets_to_kv.py)

## 🤝 Support

For issues related to:

- **Framework Integration**: Run `python test_cloudflare_kv.py` and check logs
- **Cloudflare KV Service**: Contact Cloudflare support or check status page
- **API Token Issues**: Verify permissions and test with curl commands above
- **Migration Problems**: Use the migration script and verify credentials

## 🎯 Quick Start Checklist

- [ ] Create Cloudflare API token with KV permissions
- [ ] Add `CLOUDFLARE_ACCOUNT_ID` and `CLOUDFLARE_API_TOKEN` to `.env`
- [ ] Run `python test_cloudflare_kv.py` to verify connection
- [ ] Use `python migrate_secrets_to_kv.py` to migrate existing secrets
- [ ] Update your FastAPI app to use `SecretConfigProvider()`
- [ ] Test in staging environment
- [ ] Deploy to production with confidence! 🚀

---

**Ready to get started?** Run `python test_cloudflare_kv.py` to test your Cloudflare Workers KV integration!
