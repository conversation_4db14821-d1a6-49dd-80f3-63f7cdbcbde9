"""
Supabase Infrastructure Sub-Module

This module provides a unified Supabase ecosystem that replaces:
- Custom JWT authentication
- Database user stores
- Complex secret management
- Multiple authentication providers

Everything is handled through Supabase's native services.

Organized structure:
- client.py: Unified Supabase client (consolidates supabase_client.py + unified_supabase.py)
- auth.py: Authentication providers (from supabase_providers.py)
- database.py: Database operations and stores (from supabase_user_store.py + supabase_providers.py)
- storage.py: File storage operations
- realtime.py: Real-time subscriptions and presence (from supabase_realtime.py)
"""

# Core client only for now
from .client import UnifiedSupabaseClient, SupabaseClientManager

# For backward compatibility, export the main client with alias
from .client import UnifiedSupabaseClient as SupabaseClient

# Try to import other services, but don't fail if they have issues
try:
    from .auth import SupabaseAuthProvider
    from .database import SupabaseDatabase
    from .storage import SupabaseStorage
    from .realtime import SupabaseRealtimeService
except ImportError as e:
    # Services will be available after fixing import issues
    pass

__all__ = [
    # Core client
    "UnifiedSupabaseClient",
    "SupabaseClient",  # Alias
    "SupabaseClientManager",  # Legacy alias
]
