"""
Service Registry Implementation

This module provides a concrete implementation of the IServiceRegistry interface
for managing microservices registration and communication.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import httpx
from pydantic import BaseModel
from urllib.parse import urljoin

from core.domain.interfaces import IServiceRegistry


class ServiceInfo(BaseModel):
    """Service information model"""
    name: str
    url: str
    version: str
    health_endpoint: str = "/health"
    api_key: Optional[str] = None
    metadata: Dict[str, Any] = {}
    registered_at: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True
    restart_endpoint: str = "/admin/restart"

    def __init__(self, **data):
        if 'registered_at' not in data:
            data['registered_at'] = datetime.now(timezone.utc)
        super().__init__(**data)


class ServiceRegistryImpl(IServiceRegistry):
    """Service registry implementation"""

    def __init__(self):
        self.services: Dict[str, ServiceInfo] = {}
        self._lock = asyncio.Lock()
        self._health_check_interval = 30  # seconds

    async def register_service(
        self,
        name: str,
        url: str,
        version: str,
        health_endpoint: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a new service"""
        async with self._lock:
            try:
                # Generate API key for the service
                api_key = f"api_key_{name}_{datetime.now().timestamp()}"

                service_info = ServiceInfo(
                    name=name,
                    url=url,
                    version=version,
                    health_endpoint=health_endpoint,
                    api_key=api_key,
                    metadata=metadata or {}
                )

                self.services[name] = service_info

                return {
                    "success": True,
                    "service": name,
                    "api_key": api_key
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to register service: {str(e)}"
                }

    async def unregister_service(self, service_name: str) -> Dict[str, Any]:
        """Unregister a service"""
        async with self._lock:
            if service_name in self.services:
                del self.services[service_name]
                return {"success": True, "service": service_name}
            else:
                return {"success": False, "error": "Service not found"}

    async def list_services(self, only_healthy: bool = False) -> List[Dict[str, Any]]:
        """List all registered services"""
        services = []
        for service in self.services.values():
            if only_healthy and not service.is_healthy:
                continue

            services.append({
                "name": service.name,
                "url": str(service.url),
                "version": service.version,
                "health_endpoint": service.health_endpoint,
                "metadata": service.metadata,
                "registered_at": service.registered_at.isoformat() if service.registered_at else None,
                "last_health_check": service.last_health_check.isoformat() if service.last_health_check else None,
                "is_healthy": service.is_healthy
            })

        return services

    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Call a registered service"""
        service = await self._get_healthy_service(service_name)
        if not service:
            return None

        async with httpx.AsyncClient() as client:
            request_headers = headers or {}
            if service.api_key:
                request_headers["X-API-Key"] = service.api_key

            url = urljoin(str(service.url), endpoint)

            try:
                response = await client.request(
                    method=method,
                    url=url,
                    json=data,
                    headers=request_headers
                )

                return {
                    "status_code": response.status_code,
                    "content": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                    "headers": dict(response.headers)
                }
            except Exception as e:
                print(f"Error calling service {service_name}: {e}")
                return None

    async def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a registered service"""
        service = await self._get_service(service_name)
        if not service:
            return {"success": False, "error": "Service not found"}

        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {}
                if service.api_key:
                    headers["X-API-Key"] = service.api_key

                response = await client.post(
                    urljoin(str(service.url), service.restart_endpoint),
                    headers=headers
                )

                if response.status_code == 200:
                    # Update service status to reflect restart
                    service.last_health_check = None
                    service.is_healthy = False
                    return {
                        "success": True,
                        "service": service_name,
                        "status": "restarting",
                        "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                    }
                else:
                    return {
                        "success": False,
                        "service": service_name,
                        "status_code": response.status_code,
                        "error": response.text
                    }
        except Exception as e:
            return {
                "success": False,
                "service": service_name,
                "error": str(e)
            }

    async def get_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get service information by name"""
        service = self.services.get(service_name)
        if not service:
            return None

        return {
            "name": service.name,
            "url": str(service.url),
            "version": service.version,
            "health_endpoint": service.health_endpoint,
            "api_key": service.api_key,
            "metadata": service.metadata,
            "registered_at": service.registered_at.isoformat() if service.registered_at else None,
            "last_health_check": service.last_health_check.isoformat() if service.last_health_check else None,
            "is_healthy": service.is_healthy
        }

    async def _get_service(self, name: str) -> Optional[ServiceInfo]:
        """Get service by name (internal method)"""
        return self.services.get(name)

    async def _get_healthy_service(self, name: str) -> Optional[ServiceInfo]:
        """Get healthy service by name"""
        service = self.services.get(name)
        if service and service.is_healthy:
            return service
        return None

    async def shutdown_all(self) -> None:
        """Gracefully shutdown all services"""
        for service_name, service in list(self.services.items()):
            try:
                print(f"Shutting down service: {service_name}")
                # Optionally call a shutdown endpoint on each service
                # await self.call_service(service_name, "/admin/shutdown", method="POST")
            except Exception as e:
                print(f"Error shutting down service {service_name}: {e}")

        # Clear the services dictionary
        self.services.clear()