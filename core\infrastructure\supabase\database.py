"""
Supabase Database Operations

This module provides Supabase-backed database operations and stores
using Supabase Auth + PostgreSQL with Row Level Security (RLS).

Migrated from: core.infrastructure.supabase_user_store, core.infrastructure.supabase_providers
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set
from pydantic import BaseModel

try:
    from core.domain.interfaces import IUserStore, IRoleStore
    from core.domain.models import User, Role, Permission, UserStatus
except ImportError:
    # Fallback for development
    class IUserStore:
        pass
    class IRoleStore:
        pass
    class User:
        pass
    class Role:
        pass
    class Permission:
        pass
    class UserStatus:
        pass
from .client import UnifiedSupabaseClient

import logging

logger = logging.getLogger(__name__)


class SupabaseDatabase:
    """
    Unified Supabase database operations

    Provides high-level database operations with proper error handling,
    logging, and Row Level Security (RLS) support.
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client

    async def initialize(self) -> None:
        """Initialize database and ensure required tables exist"""
        await self.supabase.initialize()
        await self._ensure_tables_exist()

    async def _ensure_tables_exist(self) -> None:
        """Ensure required tables exist (would typically be handled by migrations)"""
        try:
            # Check if user_profiles table exists
            response = self.supabase.table("user_profiles").select("id").limit(1).execute()
            logger.info("✅ Database tables verified")
        except Exception as e:
            logger.warning(f"Database table verification failed: {e}")

    # Generic database operations
    async def insert(self, table: str, data: Dict[str, Any], use_service_key: bool = False) -> Optional[Dict[str, Any]]:
        """Insert data into table"""
        try:
            response = self.supabase.table(table, use_service_key=use_service_key).insert(data).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"Insert failed for table {table}: {e}")
            return None

    async def select(self, table: str, columns: str = "*", filters: Optional[Dict[str, Any]] = None,
                    use_service_key: bool = False) -> List[Dict[str, Any]]:
        """Select data from table"""
        try:
            query = self.supabase.table(table, use_service_key=use_service_key).select(columns)

            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            response = query.execute()
            return response.data or []
        except Exception as e:
            logger.error(f"Select failed for table {table}: {e}")
            return []

    async def update(self, table: str, data: Dict[str, Any], filters: Dict[str, Any],
                    use_service_key: bool = False) -> Optional[Dict[str, Any]]:
        """Update data in table"""
        try:
            query = self.supabase.table(table, use_service_key=use_service_key).update(data)

            for key, value in filters.items():
                query = query.eq(key, value)

            response = query.execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"Update failed for table {table}: {e}")
            return None

    async def delete(self, table: str, filters: Dict[str, Any], use_service_key: bool = False) -> bool:
        """Delete data from table"""
        try:
            query = self.supabase.table(table, use_service_key=use_service_key).delete()

            for key, value in filters.items():
                query = query.eq(key, value)

            response = query.execute()
            return bool(response.data)
        except Exception as e:
            logger.error(f"Delete failed for table {table}: {e}")
            return False

    async def count(self, table: str, filters: Optional[Dict[str, Any]] = None,
                   use_service_key: bool = False) -> int:
        """Count records in table"""
        try:
            query = self.supabase.table(table, use_service_key=use_service_key).select("id", count="exact")

            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            response = query.execute()
            return response.count or 0
        except Exception as e:
            logger.error(f"Count failed for table {table}: {e}")
            return 0


class SupabaseUserStore(IUserStore):
    """
    Supabase-backed user store with Auth integration

    Features:
    - Supabase Auth for authentication
    - PostgreSQL with RLS for user profiles
    - Real-time user updates
    - Social login support
    - Email verification workflows
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client
        self.db = SupabaseDatabase(supabase_client)
        self.table_name = "user_profiles"

    async def create_user(self, user: User) -> User:
        """Create user with Supabase Auth + profile"""
        try:
            # Create user in Supabase Auth
            auth_response = await self.supabase.sign_up(
                email=user.email,
                password="temp_password",  # Will be updated via password reset
                options={
                    "data": {
                        "username": user.username,
                        "first_name": user.first_name,
                        "last_name": user.last_name
                    }
                }
            )

            if not auth_response.get("user"):
                raise Exception("Failed to create auth user")

            auth_user_id = auth_response["user"].id

            # Create user profile in database
            profile_data = {
                "id": user.id,
                "auth_user_id": auth_user_id,
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status.value,
                "is_superuser": user.is_superuser,
                "is_verified": user.is_verified,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat(),
                "metadata": {"role_ids": list(user.role_ids)}
            }

            result = await self.db.insert(self.table_name, profile_data, use_service_key=True)

            if not result:
                raise Exception("Failed to create user profile")

            logger.info(f"Created user: {user.username}")
            return user

        except Exception as e:
            logger.error(f"Failed to create user {user.username}: {e}")
            raise

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            users = await self.db.select(self.table_name, filters={"id": user_id})

            if users:
                return self._map_to_user(users[0])

            return None

        except Exception as e:
            logger.error(f"Failed to get user by ID {user_id}: {e}")
            return None

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        try:
            users = await self.db.select(self.table_name, filters={"username": username})

            if users:
                return self._map_to_user(users[0])

            return None

        except Exception as e:
            logger.error(f"Failed to get user by username {username}: {e}")
            return None

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        try:
            # Email is stored in Supabase Auth, need to query by auth_user_id
            # This is a simplified implementation
            users = await self.db.select(self.table_name)

            for user_data in users:
                # Would need to cross-reference with auth table
                # For now, return None
                pass

            return None

        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            return None

    async def update_user(self, user: User) -> User:
        """Update user profile"""
        try:
            update_data = {
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status.value,
                "is_superuser": user.is_superuser,
                "is_verified": user.is_verified,
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": {"role_ids": list(user.role_ids)}
            }

            result = await self.db.update(
                self.table_name,
                update_data,
                {"id": user.id},
                use_service_key=True
            )

            if not result:
                raise Exception("Failed to update user profile")

            logger.info(f"Updated user: {user.username}")
            return user

        except Exception as e:
            logger.error(f"Failed to update user {user.id}: {e}")
            raise

    async def delete_user(self, user_id: str) -> bool:
        """Delete user"""
        try:
            success = await self.db.delete(self.table_name, {"id": user_id}, use_service_key=True)

            if success:
                logger.info(f"Deleted user: {user_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to delete user {user_id}: {e}")
            return False

    async def list_users(self, limit: int = 100, offset: int = 0) -> List[User]:
        """List users with pagination"""
        try:
            # Note: Supabase pagination would use .range(offset, offset + limit - 1)
            users_data = await self.db.select(self.table_name)

            # Apply manual pagination for now
            paginated_data = users_data[offset:offset + limit]

            return [self._map_to_user(user_data) for user_data in paginated_data]

        except Exception as e:
            logger.error(f"Failed to list users: {e}")
            return []

    async def count_users(self) -> int:
        """Count total users"""
        try:
            return await self.db.count(self.table_name)
        except Exception as e:
            logger.error(f"Failed to count users: {e}")
            return 0

    def _map_to_user(self, user_data: Dict[str, Any]) -> User:
        """Map database record to User domain model"""
        return User(
            id=user_data["id"],
            username=user_data["username"],
            email=user_data.get("email", ""),  # Would come from auth table
            first_name=user_data.get("first_name", ""),
            last_name=user_data.get("last_name", ""),
            status=UserStatus(user_data.get("status", "active")),
            is_superuser=user_data.get("is_superuser", False),
            is_verified=user_data.get("is_verified", False),
            role_ids=set(user_data.get("metadata", {}).get("role_ids", [])),
            created_at=datetime.fromisoformat(user_data["created_at"]) if user_data.get("created_at") else datetime.now(timezone.utc),
            updated_at=datetime.fromisoformat(user_data["updated_at"]) if user_data.get("updated_at") else datetime.now(timezone.utc)
        )


class SupabaseRoleStore(IRoleStore):
    """
    Supabase-backed role store

    Features:
    - Role management with permissions
    - Hierarchical role support
    - Real-time role updates
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client
        self.db = SupabaseDatabase(supabase_client)
        self.table_name = "roles"

    async def create_role(self, role: Role) -> Role:
        """Create new role"""
        try:
            role_data = {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "permissions": list(role.permissions),
                "is_system": role.is_system,
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat()
            }

            result = await self.db.insert(self.table_name, role_data, use_service_key=True)

            if not result:
                raise Exception("Failed to create role")

            logger.info(f"Created role: {role.name}")
            return role

        except Exception as e:
            logger.error(f"Failed to create role {role.name}: {e}")
            raise

    async def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get role by ID"""
        try:
            roles = await self.db.select(self.table_name, filters={"id": role_id})

            if roles:
                return self._map_to_role(roles[0])

            return None

        except Exception as e:
            logger.error(f"Failed to get role by ID {role_id}: {e}")
            return None

    async def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get role by name"""
        try:
            roles = await self.db.select(self.table_name, filters={"name": name})

            if roles:
                return self._map_to_role(roles[0])

            return None

        except Exception as e:
            logger.error(f"Failed to get role by name {name}: {e}")
            return None

    async def update_role(self, role: Role) -> Role:
        """Update role"""
        try:
            update_data = {
                "name": role.name,
                "description": role.description,
                "permissions": list(role.permissions),
                "is_system": role.is_system,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            result = await self.db.update(
                self.table_name,
                update_data,
                {"id": role.id},
                use_service_key=True
            )

            if not result:
                raise Exception("Failed to update role")

            logger.info(f"Updated role: {role.name}")
            return role

        except Exception as e:
            logger.error(f"Failed to update role {role.id}: {e}")
            raise

    async def delete_role(self, role_id: str) -> bool:
        """Delete role"""
        try:
            success = await self.db.delete(self.table_name, {"id": role_id}, use_service_key=True)

            if success:
                logger.info(f"Deleted role: {role_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to delete role {role_id}: {e}")
            return False

    async def list_roles(self) -> List[Role]:
        """List all roles"""
        try:
            roles_data = await self.db.select(self.table_name)
            return [self._map_to_role(role_data) for role_data in roles_data]

        except Exception as e:
            logger.error(f"Failed to list roles: {e}")
            return []

    async def get_user_roles(self, user_id: str) -> List[Role]:
        """Get roles for a specific user"""
        try:
            # Get user profile to get role IDs
            user_data = await self.db.select("user_profiles", filters={"id": user_id})

            if not user_data:
                return []

            role_ids = user_data[0].get("metadata", {}).get("role_ids", [])

            if not role_ids:
                return []

            # Get roles by IDs
            roles = []
            for role_id in role_ids:
                role = await self.get_role_by_id(role_id)
                if role:
                    roles.append(role)

            return roles

        except Exception as e:
            logger.error(f"Failed to get user roles for {user_id}: {e}")
            return []

    def _map_to_role(self, role_data: Dict[str, Any]) -> Role:
        """Map database record to Role domain model"""
        return Role(
            id=role_data["id"],
            name=role_data["name"],
            description=role_data.get("description", ""),
            permissions=set(role_data.get("permissions", [])),
            is_system=role_data.get("is_system", False),
            created_at=datetime.fromisoformat(role_data["created_at"]) if role_data.get("created_at") else datetime.now(timezone.utc),
            updated_at=datetime.fromisoformat(role_data["updated_at"]) if role_data.get("updated_at") else datetime.now(timezone.utc)
        )
