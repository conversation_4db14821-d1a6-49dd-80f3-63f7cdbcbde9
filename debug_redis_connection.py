#!/usr/bin/env python3
"""
Debug Redis Connection

This script helps debug Redis connection issues by testing different
connection methods and configurations.
"""

import asyncio
import socket
import sys
from typing import List, Tuple


async def test_dns_resolution(hostname: str) -> bool:
    """Test DNS resolution for the hostname"""
    print(f"🔍 Testing DNS resolution for: {hostname}")
    try:
        # Test DNS resolution
        result = socket.getaddrinfo(hostname, None)
        if result:
            ip_addresses = [addr[4][0] for addr in result]
            print(f"   ✅ DNS resolution successful")
            print(f"   IP addresses: {', '.join(set(ip_addresses))}")
            return True
        else:
            print(f"   ❌ DNS resolution failed: No addresses found")
            return False
    except Exception as e:
        print(f"   ❌ DNS resolution failed: {e}")
        return False


async def test_tcp_connection(hostname: str, port: int) -> bool:
    """Test TCP connection to the Redis server"""
    print(f"🔌 Testing TCP connection to: {hostname}:{port}")
    try:
        # Test TCP connection
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(hostname, port),
            timeout=10.0
        )
        print(f"   ✅ TCP connection successful")
        writer.close()
        await writer.wait_closed()
        return True
    except asyncio.TimeoutError:
        print(f"   ❌ TCP connection timed out")
        return False
    except Exception as e:
        print(f"   ❌ TCP connection failed: {e}")
        return False


async def test_redis_connection_variants(base_hostname: str, port: int) -> List[Tuple[str, bool]]:
    """Test different Redis connection URL variants"""
    print(f"🧪 Testing Redis connection variants")
    
    # Different URL formats to try
    variants = [
        f"redis://{base_hostname}:{port}",
        f"redis://{base_hostname}:{port}/0",
        f"rediss://{base_hostname}:{port}",  # SSL
        f"rediss://{base_hostname}:{port}/0",  # SSL with DB
    ]
    
    results = []
    
    for url in variants:
        print(f"\n   Testing: {url}")
        try:
            import redis.asyncio as aioredis
            
            # Try to connect with different configurations
            redis_client = await aioredis.from_url(
                url,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test ping
            pong = await redis_client.ping()
            print(f"      ✅ Connection successful: {pong}")
            await redis_client.close()
            results.append((url, True))
            
        except Exception as e:
            print(f"      ❌ Connection failed: {e}")
            results.append((url, False))
    
    return results


async def test_redis_with_auth(hostname: str, port: int) -> bool:
    """Test Redis connection with potential authentication"""
    print(f"🔐 Testing Redis with authentication scenarios")
    
    # Common authentication patterns
    auth_variants = [
        f"redis://{hostname}:{port}",  # No auth
        f"redis://default@{hostname}:{port}",  # Default user
        f"redis://:{hostname}:{port}",  # Empty password
    ]
    
    for url in auth_variants:
        print(f"\n   Testing auth variant: {url}")
        try:
            import redis.asyncio as aioredis
            
            redis_client = await aioredis.from_url(
                url,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            pong = await redis_client.ping()
            print(f"      ✅ Auth successful: {pong}")
            await redis_client.close()
            return True
            
        except Exception as e:
            print(f"      ❌ Auth failed: {e}")
    
    return False


async def main():
    """Main debugging function"""
    print("🔧 Redis Connection Debugging Tool")
    print("=" * 60)
    
    # Parse the hostname and port from your Redis URL
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redis-cloud.com"
    port = 10401
    
    print(f"Target Redis server: {hostname}:{port}")
    print()
    
    # Step 1: Test DNS resolution
    dns_ok = await test_dns_resolution(hostname)
    print()
    
    # Step 2: Test TCP connection
    tcp_ok = await test_tcp_connection(hostname, port)
    print()
    
    # Step 3: Test Redis connection variants
    if tcp_ok:
        redis_results = await test_redis_connection_variants(hostname, port)
        print()
        
        # Step 4: Test authentication if basic connection fails
        if not any(result[1] for result in redis_results):
            auth_ok = await test_redis_with_auth(hostname, port)
            print()
    
    # Summary
    print("📊 Debugging Summary")
    print("=" * 60)
    print(f"DNS Resolution: {'✅ OK' if dns_ok else '❌ FAILED'}")
    print(f"TCP Connection: {'✅ OK' if tcp_ok else '❌ FAILED'}")
    
    if tcp_ok:
        print("\nRedis Connection Tests:")
        if 'redis_results' in locals():
            for url, success in redis_results:
                status = "✅ OK" if success else "❌ FAILED"
                print(f"   {url}: {status}")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if not dns_ok:
        print("   • Check if the hostname is correct")
        print("   • Verify network connectivity")
        print("   • Check if you're behind a firewall/proxy")
    elif not tcp_ok:
        print("   • Check if the port is correct")
        print("   • Verify the Redis server is running")
        print("   • Check firewall rules")
        print("   • Verify your IP is whitelisted in Redis Cloud")
    else:
        print("   • TCP connection works, but Redis protocol fails")
        print("   • Check if authentication is required")
        print("   • Verify Redis server configuration")
        print("   • Check if SSL/TLS is required")
    
    print("\n🔍 Additional Checks:")
    print("   • Verify your Redis Cloud subscription is active")
    print("   • Check if your IP address is whitelisted")
    print("   • Confirm the endpoint URL from Redis Cloud dashboard")
    print("   • Check if there are any authentication requirements")


if __name__ == "__main__":
    asyncio.run(main())
