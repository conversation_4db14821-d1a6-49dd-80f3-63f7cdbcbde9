#!/usr/bin/env python3
"""
Redis Service Registry System Tests

This module contains system tests for the Redis-backed service registry implementation.
"""

import asyncio
import json
import time
import pytest
from datetime import datetime, timezone
from typing import Dict, Any

from core.infrastructure.redis_service_registry import RedisServiceRegistry


class MockService:
    """Mock service for testing"""

    def __init__(self, name: str, port: int):
        self.name = name
        self.port = port
        self.url = f"http://localhost:{port}"
        self.version = "1.0.0"
        self.is_running = False

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "url": self.url,
            "version": self.version,
            "health_endpoint": "/health",
            "metadata": {
                "port": self.port,
                "test_service": True
            }
        }


@pytest.mark.system
@pytest.mark.asyncio
async def test_redis_service_registry():
    """Test Redis service registry functionality"""

    # Redis connection from your AWS MemoryDB instance
    redis_url = "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"

    # Initialize registry
    registry = RedisServiceRegistry(
        redis_url=redis_url,
        key_prefix="test_service_registry:",
        default_ttl=60,  # 1 minute for testing
        health_check_interval=10  # 10 seconds for testing
    )

    try:
        # Test 1: Service Registration
        mock_services = [
            MockService("translation-service", 8001),
            MockService("auth-service", 8002),
            MockService("notification-service", 8003)
        ]

        registered_services = []
        for service in mock_services:
            service_data = service.to_dict()
            result = await registry.register_service(
                name=service_data["name"],
                url=service_data["url"],
                version=service_data["version"],
                health_endpoint=service_data["health_endpoint"],
                metadata=service_data["metadata"]
            )

            assert result["success"] is True
            assert "api_key" in result
            assert result["ttl"] > 0
            registered_services.append(service.name)

        # Test 2: List Services
        services = await registry.list_services()
        assert len(services) >= len(registered_services)
        
        service_names = [s['name'] for s in services]
        for service_name in registered_services:
            assert service_name in service_names

        # Test 3: Get Specific Service
        if registered_services:
            service_name = registered_services[0]
            service = await registry.get_service(service_name)
            assert service is not None
            assert service['name'] == service_name
            assert 'url' in service
            assert 'version' in service
            assert 'api_key' in service
            assert 'metadata' in service

        # Test 4: Heartbeat
        if registered_services:
            service_name = registered_services[0]
            result = await registry.heartbeat(service_name)
            assert result["success"] is True
            assert "next_heartbeat" in result

        # Test 5: Service Statistics
        stats = await registry.get_service_stats()
        assert "total_services" in stats
        assert "healthy_services" in stats
        assert "unhealthy_services" in stats
        assert "health_percentage" in stats
        assert stats["total_services"] >= len(registered_services)

        # Test 6: Distributed Locking
        if registered_services:
            service_name = registered_services[0]

            # Try to register the same service again (should be blocked by lock)
            result = await registry.register_service(
                name=service_name,
                url="http://localhost:9999",
                version="2.0.0"
            )

            # Should either succeed (if lock expired) or fail with lock error
            assert "success" in result

        # Test 7: Health Monitoring (start background task)
        await registry.start_monitoring()
        
        # Wait and check health updates
        await asyncio.sleep(15)

        services_after_monitoring = await registry.list_services()
        assert len(services_after_monitoring) >= 0

        # Test 8: Service Cleanup
        cleanup_result = await registry.cleanup_expired_services()
        assert cleanup_result["success"] is True
        assert "expired_services_cleaned" in cleanup_result

        # Test 9: Service Unregistration
        for service_name in registered_services:
            result = await registry.unregister_service(service_name)
            assert result["success"] is True

        # Final verification
        final_services = await registry.list_services()
        # Note: There might be other services from other tests, so we just check
        # that our test services are gone
        final_service_names = [s['name'] for s in final_services]
        for service_name in registered_services:
            assert service_name not in final_service_names

    finally:
        # Cleanup
        await registry.stop_monitoring()
        await registry.dispose()


@pytest.mark.system
@pytest.mark.asyncio
async def test_redis_connection():
    """Test basic Redis connectivity"""
    redis_url = "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redis-cloud.com:10401"

    try:
        import redis.asyncio as aioredis

        redis_client = await aioredis.from_url(redis_url, encoding="utf-8", decode_responses=True)

        # Test basic operations
        await redis_client.set("test_key", "test_value", ex=10)
        value = await redis_client.get("test_key")

        assert value == "test_value"

        # Cleanup
        await redis_client.delete("test_key")
        await redis_client.close()

    except Exception as e:
        pytest.fail(f"Redis connection failed: {e}")


def main():
    """Main test function"""
    async def run_tests():
        print("🚀 Redis Service Registry Test Suite")
        print("=" * 60)

        # Test Redis connection first
        try:
            await test_redis_connection()
            print("✅ Redis connection test passed")
            await test_redis_service_registry()
            print("✅ Redis service registry test passed")
        except Exception as e:
            print(f"❌ Tests failed: {e}")
            raise

    asyncio.run(run_tests())


if __name__ == "__main__":
    main()
