"""
Redis Cache with Fallback Mechanisms

This module provides a Redis cache implementation with comprehensive
fallback mechanisms for handling Redis Cloud free tier limits.

Migrated from: core.infrastructure.redis_cache
"""

import asyncio
import logging
import pickle
from typing import Any, Optional, Dict, List
from datetime import datetime, timezone

from ...domain.interfaces import ICache
from ...domain.exceptions import CacheError

# Import fallback manager from modular location
try:
    from .fallback import (
        RedisFallbackManager,
        RedisLimits,
        FallbackStrategy
    )
except ImportError:
    # Fallback to legacy location
    try:
        from ..redis_fallback_manager import (
            RedisFallbackManager,
            RedisLimits,
            FallbackStrategy
        )
    except ImportError:
        # Final fallback for development
        RedisFallbackManager = None
        RedisLimits = None
        FallbackStrategy = None

# Import memory cache
try:
    from ..cache import MemoryCache
except ImportError:
    try:
        from .memory import MemoryCache
    except ImportError:
        MemoryCache = None

logger = logging.getLogger(__name__)


class RedisCache(ICache):
    """
    Redis cache with fallback mechanisms for free tier limits

    Features:
    - Connection pooling and rate limiting
    - Memory cache fallback when Redis is unavailable
    - Queue system for retrying failed operations
    - Graceful degradation under load
    - Comprehensive monitoring and health checks
    """

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "cache:",
        fallback_strategy: Optional[Any] = None,  # FallbackStrategy type
        memory_cache_size: int = 1000,
        memory_cache_ttl: int = 300,
        redis_limits: Optional[Any] = None  # RedisLimits type
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.fallback_strategy = fallback_strategy or (FallbackStrategy.HYBRID if FallbackStrategy else None)

        # Initialize fallback manager if available
        if RedisFallbackManager and RedisLimits:
            self.fallback_manager = RedisFallbackManager(
                redis_url=redis_url,
                limits=redis_limits or RedisLimits(),
                fallback_strategy=self.fallback_strategy,
                enable_memory_fallback=True,
                enable_queue_system=True
            )
        else:
            self.fallback_manager = None

        # Memory cache for fallback
        if MemoryCache:
            self.memory_cache = MemoryCache(
                max_size=memory_cache_size,
                default_ttl=memory_cache_ttl
            )
        else:
            self.memory_cache = None

        # Initialization flag
        self._initialized = False

    async def initialize(self):
        """Initialize the cache system"""
        if not self._initialized:
            if self.fallback_manager:
                await self.fallback_manager.initialize()
            self._initialized = True
            logger.info("Redis cache initialized")

    def _make_key(self, key: str) -> str:
        """Create prefixed key"""
        return f"{self.key_prefix}{key}"

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first with fallback manager
            if self.fallback_manager:
                result = await self.fallback_manager.execute_operation(
                    "get",
                    prefixed_key,
                    fallback_value=None
                )

                if result is not None:
                    # Deserialize Redis result
                    try:
                        return pickle.loads(result) if isinstance(result, bytes) else result
                    except (pickle.PickleError, TypeError):
                        # If deserialization fails, return raw value
                        return result

            # If Redis returns None or unavailable, try memory cache as secondary fallback
            if self.memory_cache:
                return await self.memory_cache.get(key)

            return None

        except Exception as e:
            logger.warning(f"Cache get operation failed for key {key}: {e}")
            # Final fallback to memory cache
            if self.memory_cache:
                return await self.memory_cache.get(key)
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Serialize value for Redis
            serialized_value = pickle.dumps(value)

            # Try Redis first
            if self.fallback_manager:
                if ttl is not None:
                    await self.fallback_manager.execute_operation(
                        "setex",
                        prefixed_key,
                        ttl,
                        serialized_value,
                        fallback_value=True,
                        ttl=ttl,
                        priority=1  # Set operations have priority
                    )
                else:
                    await self.fallback_manager.execute_operation(
                        "set",
                        prefixed_key,
                        serialized_value,
                        fallback_value=True,
                        ttl=ttl,
                        priority=1
                    )

            # Also set in memory cache as backup
            if self.memory_cache:
                await self.memory_cache.set(key, value, ttl)

        except Exception as e:
            logger.warning(f"Cache set operation failed for key {key}: {e}")
            # Fallback to memory cache only
            if self.memory_cache:
                await self.memory_cache.set(key, value, ttl)

    async def delete(self, key: str) -> None:
        """Delete value from cache with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first
            if self.fallback_manager:
                await self.fallback_manager.execute_operation(
                    "delete",
                    prefixed_key,
                    fallback_value=True,
                    priority=2  # Delete operations have high priority
                )

        except Exception as e:
            logger.warning(f"Cache delete operation failed for key {key}: {e}")

        # Always delete from memory cache too
        if self.memory_cache:
            await self.memory_cache.delete(key)

    async def exists(self, key: str) -> bool:
        """Check if key exists with fallback"""
        if not self._initialized:
            await self.initialize()

        prefixed_key = self._make_key(key)

        try:
            # Try Redis first
            if self.fallback_manager:
                result = await self.fallback_manager.execute_operation(
                    "exists",
                    prefixed_key,
                    fallback_value=False
                )

                if result:
                    return bool(result)

            # Fallback to memory cache
            if self.memory_cache:
                return await self.memory_cache.exists(key)

            return False

        except Exception as e:
            logger.warning(f"Cache exists operation failed for key {key}: {e}")
            # Final fallback to memory cache
            if self.memory_cache:
                return await self.memory_cache.exists(key)
            return False

    async def clear(self) -> None:
        """Clear all cache with fallback"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to clear Redis cache
            if self.fallback_manager:
                pattern = f"{self.key_prefix}*"

                # Get keys first
                keys = await self.fallback_manager.execute_operation(
                    "keys",
                    pattern,
                    fallback_value=[]
                )

                # Delete keys if found
                if keys:
                    await self.fallback_manager.execute_operation(
                        "delete",
                        *keys,
                        fallback_value=True,
                        priority=3  # Clear operations have highest priority
                    )

        except Exception as e:
            logger.warning(f"Cache clear operation failed: {e}")

        # Always clear memory cache
        if self.memory_cache:
            await self.memory_cache.clear()

    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        if not self._initialized:
            return {
                "status": "not_initialized",
                "redis_healthy": False,
                "memory_cache_size": 0
            }

        # Get fallback manager health
        if self.fallback_manager:
            health = await self.fallback_manager.get_health_status()
        else:
            health = {"status": "fallback_manager_unavailable", "redis_healthy": False}

        # Add memory cache info
        if self.memory_cache:
            health.update({
                "memory_cache_size": len(self.memory_cache._cache),
                "memory_cache_max_size": self.memory_cache.max_size,
            })

        health.update({
            "cache_strategy": str(self.fallback_strategy) if self.fallback_strategy else "none",
            "initialized": self._initialized
        })

        return health

    async def dispose(self) -> None:
        """Dispose cache resources"""
        if self._initialized:
            if self.fallback_manager:
                await self.fallback_manager.dispose()
            if self.memory_cache:
                await self.memory_cache.clear()
            self._initialized = False
            logger.info("Redis cache disposed")


__all__ = [
    "RedisCache",
]
