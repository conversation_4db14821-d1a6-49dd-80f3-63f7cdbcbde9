"""
Domain Models for User Management

This module contains the core domain entities for user management,
including User, Role, and Permission models with proper validation.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional, Set
from pydantic import BaseModel, Field, EmailStr, validator


class UserStatus(str, Enum):
    """User account status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(BaseModel):
    """User domain entity"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique user identifier")
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="User email address")
    password_hash: str = Field(..., description="Hashed password")
    first_name: Optional[str] = Field(None, max_length=100, description="User's first name")
    last_name: Optional[str] = Field(None, max_length=100, description="User's last name")
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="Account status")
    is_superuser: bool = Field(default=False, description="Superuser flag")
    is_verified: bool = Field(default=False, description="Email verification status")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Role relationships (stored as role IDs)
    role_ids: Set[str] = Field(default_factory=set, description="Set of assigned role IDs")
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username format"""
        if not v.isalnum() and '_' not in v and '-' not in v:
            raise ValueError('Username can only contain letters, numbers, underscores, and hyphens')
        return v.lower()
    
    @validator('email')
    def validate_email(cls, v):
        """Validate and normalize email"""
        return v.lower()
    
    def add_role(self, role_id: str) -> None:
        """Add role to user"""
        self.role_ids.add(role_id)
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_role(self, role_id: str) -> None:
        """Remove role from user"""
        self.role_ids.discard(role_id)
        self.updated_at = datetime.now(timezone.utc)
    
    def has_role(self, role_id: str) -> bool:
        """Check if user has specific role"""
        return role_id in self.role_ids
    
    def activate(self) -> None:
        """Activate user account"""
        self.status = UserStatus.ACTIVE
        self.updated_at = datetime.now(timezone.utc)
    
    def deactivate(self) -> None:
        """Deactivate user account"""
        self.status = UserStatus.INACTIVE
        self.updated_at = datetime.now(timezone.utc)
    
    def suspend(self) -> None:
        """Suspend user account"""
        self.status = UserStatus.SUSPENDED
        self.updated_at = datetime.now(timezone.utc)
    
    def verify_email(self) -> None:
        """Mark email as verified"""
        self.is_verified = True
        if self.status == UserStatus.PENDING_VERIFICATION:
            self.status = UserStatus.ACTIVE
        self.updated_at = datetime.now(timezone.utc)
    
    def update_last_login(self) -> None:
        """Update last login timestamp"""
        self.last_login = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    @property
    def full_name(self) -> str:
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        return self.username
    
    @property
    def is_active(self) -> bool:
        """Check if user account is active"""
        return self.status == UserStatus.ACTIVE
    
    class Config:
        """Pydantic configuration"""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            set: lambda v: list(v)
        }


class Permission(BaseModel):
    """Permission domain entity"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique permission identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Permission name")
    resource: str = Field(..., min_length=1, max_length=100, description="Resource name")
    action: str = Field(..., min_length=1, max_length=50, description="Action name")
    description: Optional[str] = Field(None, max_length=500, description="Permission description")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    @validator('name')
    def validate_name(cls, v):
        """Validate permission name format"""
        return v.lower().replace(' ', '_')
    
    @validator('resource')
    def validate_resource(cls, v):
        """Validate resource name format"""
        return v.lower().replace(' ', '_')
    
    @validator('action')
    def validate_action(cls, v):
        """Validate action name format"""
        return v.lower().replace(' ', '_')
    
    @property
    def permission_string(self) -> str:
        """Get permission as resource:action string"""
        return f"{self.resource}:{self.action}"
    
    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Role(BaseModel):
    """Role domain entity"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique role identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Role name")
    description: Optional[str] = Field(None, max_length=500, description="Role description")
    is_system_role: bool = Field(default=False, description="System role flag")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Permission relationships (stored as permission IDs)
    permission_ids: Set[str] = Field(default_factory=set, description="Set of permission IDs")
    
    @validator('name')
    def validate_name(cls, v):
        """Validate role name format"""
        return v.lower().replace(' ', '_')
    
    def add_permission(self, permission_id: str) -> None:
        """Add permission to role"""
        self.permission_ids.add(permission_id)
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_permission(self, permission_id: str) -> None:
        """Remove permission from role"""
        self.permission_ids.discard(permission_id)
        self.updated_at = datetime.now(timezone.utc)
    
    def has_permission(self, permission_id: str) -> bool:
        """Check if role has specific permission"""
        return permission_id in self.permission_ids
    
    class Config:
        """Pydantic configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            set: lambda v: list(v)
        }


# Data Transfer Objects (DTOs) for API operations

class UserCreateDTO(BaseModel):
    """DTO for creating a new user"""
    
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    role_ids: Optional[Set[str]] = Field(default_factory=set)
    is_superuser: bool = Field(default=False)
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username format"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, underscores, and hyphens')
        return v.lower()
    
    @validator('email')
    def validate_email(cls, v):
        """Validate and normalize email"""
        return v.lower()


class UserUpdateDTO(BaseModel):
    """DTO for updating user information"""
    
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    status: Optional[UserStatus] = None
    is_superuser: Optional[bool] = None
    role_ids: Optional[Set[str]] = None
    
    @validator('email')
    def validate_email(cls, v):
        """Validate and normalize email"""
        return v.lower() if v else v


class PasswordUpdateDTO(BaseModel):
    """DTO for password updates"""
    
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """Validate that passwords match"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class RoleCreateDTO(BaseModel):
    """DTO for creating a new role"""
    
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    permission_ids: Optional[Set[str]] = Field(default_factory=set)
    
    @validator('name')
    def validate_name(cls, v):
        """Validate role name format"""
        return v.lower().replace(' ', '_')


class RoleUpdateDTO(BaseModel):
    """DTO for updating role information"""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    permission_ids: Optional[Set[str]] = None
    
    @validator('name')
    def validate_name(cls, v):
        """Validate role name format"""
        return v.lower().replace(' ', '_') if v else v


class PermissionCreateDTO(BaseModel):
    """DTO for creating a new permission"""
    
    name: str = Field(..., min_length=1, max_length=100)
    resource: str = Field(..., min_length=1, max_length=100)
    action: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    
    @validator('name', 'resource', 'action')
    def validate_names(cls, v):
        """Validate name formats"""
        return v.lower().replace(' ', '_')
