import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock
from pydantic import HttpUrl

from core.infrastructure.service_registry import ServiceRegistryImpl as ServiceRegistry, ServiceInfo


@pytest.fixture
def service_registry():
    return ServiceRegistry()


@pytest.fixture
def sample_service():
    return ServiceInfo(
        name="test-service",
        url="http://test-service:8000",
        version="1.0.0",
        api_key="test-api-key"
    )


@pytest.mark.asyncio
async def test_register_service(service_registry, sample_service):
    """Test registering a service"""
    result = await service_registry.register(sample_service)

    assert result is True
    assert "test-service" in service_registry.services
    assert service_registry.services["test-service"] == sample_service


@pytest.mark.asyncio
async def test_unregister_service(service_registry, sample_service):
    """Test unregistering a service"""
    await service_registry.register(sample_service)

    # Unregister existing service
    result = await service_registry.unregister("test-service")
    assert result is True
    assert "test-service" not in service_registry.services

    # Try to unregister non-existing service
    result = await service_registry.unregister("non-existing")
    assert result is False


@pytest.mark.asyncio
async def test_get_service(service_registry, sample_service):
    """Test getting a service by name"""
    await service_registry.register(sample_service)

    # Get existing service
    service = await service_registry.get_service("test-service")
    assert service == sample_service

    # Get non-existing service
    service = await service_registry.get_service("non-existing")
    assert service is None


@pytest.mark.asyncio
async def test_get_healthy_service(service_registry, sample_service):
    """Test getting a healthy service"""
    await service_registry.register(sample_service)

    # Get healthy service
    service = await service_registry.get_healthy_service("test-service")
    assert service == sample_service

    # Mark service as unhealthy
    service_registry.services["test-service"].is_healthy = False
    service = await service_registry.get_healthy_service("test-service")
    assert service is None


@pytest.mark.asyncio
async def test_list_services(service_registry, sample_service):
    """Test listing services"""
    await service_registry.register(sample_service)

    # Create another service that's unhealthy
    unhealthy_service = ServiceInfo(
        name="unhealthy-service",
        url=HttpUrl("http://unhealthy:8000"),
        version="1.0.0",
        is_healthy=False
    )
    await service_registry.register(unhealthy_service)

    # List all services
    services = await service_registry.list_services()
    assert len(services) == 2

    # List only healthy services
    healthy_services = await service_registry.list_services(only_healthy=True)
    assert len(healthy_services) == 1
    assert healthy_services[0].name == "test-service"


@pytest.mark.asyncio
async def test_call_service(service_registry, sample_service):
    """Test calling a service"""
    await service_registry.register(sample_service)

    with patch("httpx.AsyncClient.request") as mock_request:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_request.return_value = mock_response

        # Call service
        response = await service_registry.call_service(
            "test-service",
            "/test-endpoint",
            method="GET"
        )

        assert response is not None
        mock_request.assert_called_once_with(
            "GET",
            "http://test-service:8000/test-endpoint",
            headers={"X-API-Key": "test-api-key"}
        )


@pytest.mark.asyncio
async def test_call_service_with_unhealthy_service(service_registry, sample_service):
    """Test calling an unhealthy service"""
    await service_registry.register(sample_service)
    service_registry.services["test-service"].is_healthy = False

    response = await service_registry.call_service("test-service", "/test")
    assert response is None


@pytest.mark.asyncio
async def test_restart_service(service_registry, sample_service):
    """Test restarting a service"""
    await service_registry.register(sample_service)

    with patch("httpx.AsyncClient.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "restarting"}
        mock_response.headers = {"content-type": "application/json"}
        mock_post.return_value = mock_response

        result = await service_registry.restart_service("test-service")

        assert result["success"] is True
        assert result["service"] == "test-service"
        assert result["status"] == "restarting"
        assert service_registry.services["test-service"].is_healthy is False
        mock_post.assert_called_once_with(
            "http://test-service:8000/admin/restart",
            headers={"X-API-Key": "test-api-key"}
        )


@pytest.mark.asyncio
async def test_restart_nonexistent_service(service_registry):
    """Test restarting a non-existent service"""
    result = await service_registry.restart_service("non-existent")

    assert result["success"] is False
    assert "Service not found" in result["error"]


@pytest.mark.asyncio
async def test_shutdown_all(service_registry, sample_service):
    """Test shutting down all services"""
    await service_registry.register(sample_service)

    # Add another service
    service2 = ServiceInfo(
        name="service2",
        url=HttpUrl("http://service2:8000"),
        version="1.0.0"
    )
    await service_registry.register(service2)

    await service_registry.shutdown_all()

    # Check that all services are removed
    assert len(service_registry.services) == 0