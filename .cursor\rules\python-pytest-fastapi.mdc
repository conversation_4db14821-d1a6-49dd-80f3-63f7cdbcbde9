---
description: 
globs: 
alwaysApply: true
---
# 1 · Scope & Safety

* **Primary Mission:** Write, refactor, test, and debug code *within the provided repository only*.
  * Any requests or tasks that involve external systems (e.g., cloud infrastructure, secrets management) must be marked with a `TODO(out_of_scope): …` comment.
  * Always follow user instructions and avoid going out of scope.
  * After making changes in the codebase, reflect on potential impacts on related functions. If the impact is too broad, document it with a `TODO` in a markdown file for future reference.

* **No Secrets in Prompts:** Never include API keys or credentials in prompts or error logs.

## 2 · Python Style

* **Code Style:** Follow PEP 8 conventions.
* **Type Hints:** Always use type annotations (e.g., `def fetch_data(url: str) -> dict:`).
* **Naming:**
  * Functions: Verb-first, snake_case (e.g., `fetch_data`, `process_results`).
  * Variables: Descriptive, snake_case.
  * Booleans: Prefix with `is_`, `has_`, `should_` (e.g., `is_valid`, `has_permission`).
  * Classes: CamelCase (e.g., `DataProcessor`, `RequestHandler`).
  * Constants: UPPERCASE with underscores (e.g., `MAX_RETRIES`, `DEFAULT_TIMEOUT`).
  * Enums: CamelCase (e.g., `Color.RED`, `Color.GREEN`).
  * Exceptions: CamelCase (e.g., `ValueError`, `KeyError`).

* **Imports:** Group standard library, third-party, and local imports with a blank line between groups.
* **Documentation:**
  * Module docstrings at the top of each file.
  * Function/class docstrings using Google or NumPy style.
  * Inline comments only when the intent isn't obvious.

## 3 · Project Structure

* Flat-ish modules under `project/`; one responsibility per file.
* Structure larger packages as:
  * `src/` for source code.
  * `tests/` for tests.
  * `docs/` for documentation.
* Prefer pure functions; introduce classes only when state or polymorphism earns the overhead.
* Clear entrypoint with `if __name__ == "__main__":` for scripts.

## 4 · Testing & Quality

* Use pytest for all test cases.
* Run tests with `pytest` or `python -m pytest`.
* Name test files with `test_` prefix and test functions with `test_` prefix.
* Use fixtures for common setup.
* Aim for high test coverage on critical paths.
* Use `mypy` for static type checking.
* Employ `flake8` or `pylint` for linting.

## 6 · Version Control

* **PR Title Format:** [`feat:`, `fix:`, `refactor:`, `test:`, `docs:`] Brief description.