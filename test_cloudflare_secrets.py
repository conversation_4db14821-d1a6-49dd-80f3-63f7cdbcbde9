#!/usr/bin/env python3
"""
Test Cloudflare Secrets Store Connection
This script safely tests your Cloudflare connection using environment variables.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.config.secret_providers import SecretConfigProvider


async def test_cloudflare_connection():
    """Test Cloudflare Secrets Store connection safely"""
    print("🔍 Testing Cloudflare Secrets Store Connection")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except ImportError:
        print("⚠️  python-dotenv not available, using system environment")
    
    # Check if credentials are available
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')
    
    if not account_id:
        print("❌ CLOUDFLARE_ACCOUNT_ID not found in environment")
        return False
        
    if not api_token:
        print("❌ CLOUDFLARE_API_TOKEN not found in environment")
        return False
    
    print(f"✅ Account ID: {account_id[:8]}...{account_id[-8:]}")
    print(f"✅ API Token: {api_token[:8]}...{api_token[-8:]}")
    
    # Test auto-detection
    print("\n🔍 Testing Auto-Detection...")
    try:
        provider = SecretConfigProvider(provider_type="auto")
        info = provider.get_provider_info()
        
        print(f"✅ Provider Type: {info['provider_type']}")
        print(f"✅ Provider Class: {info['provider_class']}")
        print(f"✅ Fallback to Env: {info['fallback_to_env']}")
        print(f"✅ Health Status: {info['healthy']}")
        
    except Exception as e:
        print(f"❌ Auto-detection failed: {e}")
        return False
    
    # Test health check
    print("\n🔍 Testing Health Check...")
    try:
        healthy = await provider.health_check()
        if healthy:
            print("✅ Health check passed!")
        else:
            print("❌ Health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's a 404 error (Secrets Store not available)
        if "404" in str(e):
            print("\n💡 This might mean:")
            print("   1. Cloudflare Secrets Store is not available in your account")
            print("   2. The feature is still in beta and not enabled")
            print("   3. You need to create a secrets store first")
            print("\n🔄 Let's try alternative approaches...")
            return await test_alternative_approaches(provider)
        
        return False
    
    # Test basic operations
    print("\n🔍 Testing Basic Operations...")
    try:
        # Try to list secrets
        secrets = await provider.list_secrets()
        print(f"✅ Found {len(secrets)} secrets")
        
        if secrets:
            print("📋 Available secrets:")
            for secret in secrets[:5]:  # Show first 5
                print(f"   - {secret}")
        
    except Exception as e:
        print(f"❌ List secrets failed: {e}")
        return await test_alternative_approaches(provider)
    
    return True


async def test_alternative_approaches(provider):
    """Test alternative approaches when Secrets Store is not available"""
    print("\n🔄 Testing Alternative Approaches...")
    
    # Test if we can use regular Workers secrets instead
    print("\n💡 Suggestion: Use Workers KV or regular Workers secrets")
    print("   Cloudflare Secrets Store (Beta) might not be available yet.")
    print("   Consider using:")
    print("   1. Workers KV for key-value storage")
    print("   2. Workers environment variables/secrets")
    print("   3. Fallback to encrypted file storage for development")
    
    # Test fallback to environment variables
    print("\n🔍 Testing Environment Variable Fallback...")
    try:
        # Test if we can get secrets from environment
        test_secrets = {
            'DATABASE_PASSWORD': os.getenv('DATABASE_PASSWORD'),
            'JWT_SECRET_KEY': os.getenv('JWT_SECRET_KEY'),
            'API_KEY': os.getenv('API_KEY')
        }
        
        available_secrets = {k: v for k, v in test_secrets.items() if v}
        print(f"✅ Found {len(available_secrets)} secrets in environment variables")
        
        for key in available_secrets:
            print(f"   - {key}: {'*' * 8}")
            
        return len(available_secrets) > 0
        
    except Exception as e:
        print(f"❌ Environment fallback failed: {e}")
        return False


async def main():
    """Main test function"""
    print("🚀 Cloudflare Secrets Store Test")
    print("This test will safely check your Cloudflare connection")
    print("without exposing sensitive credentials.\n")
    
    try:
        success = await test_cloudflare_connection()
        
        if success:
            print("\n🎉 SUCCESS! Your Cloudflare integration is working!")
            print("\n📝 Next Steps:")
            print("   1. Your FastAPI app can now use Cloudflare secrets")
            print("   2. Use: provider = SecretConfigProvider()")
            print("   3. Get secrets: await provider.get_secret('key-name')")
        else:
            print("\n⚠️  Cloudflare Secrets Store not fully available")
            print("   Your app will fall back to environment variables")
            print("   This is normal for accounts without Secrets Store beta access")
            
    except KeyboardInterrupt:
        print("\n\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
