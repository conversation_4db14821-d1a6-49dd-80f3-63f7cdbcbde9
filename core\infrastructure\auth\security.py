"""
Security Implementations

This module provides concrete implementations of the ISecurityProvider interface
including JWT authentication and password hashing.

Migrated from: core.infrastructure.security
"""

import hashlib
import hmac
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, List

try:
    from core.domain.interfaces import ISecurityProvider, IUserStore, IRoleStore
    from core.domain.exceptions import AuthenticationError, AuthorizationError
    from core.domain.models import User, UserStatus
except ImportError:
    # Fallback for development
    class ISecurityProvider:
        pass
    class IUserStore:
        pass
    class IRoleStore:
        pass
    class AuthenticationError(Exception):
        pass
    class AuthorizationError(Exception):
        pass
    class User:
        pass
    class UserStatus:
        ACTIVE = "active"


class JWTSecurityProvider(ISecurityProvider):
    """JWT-based security provider implementation"""

    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self._jwt_available = False

        # Try to import JWT library
        try:
            from jose import jwt
            from jose import exceptions as jwt_exceptions
            self._jwt = jwt
            self._jwt_exceptions = jwt_exceptions
            self._jwt_available = True
            self._is_jose = True
        except ImportError:
            # Fallback to PyJWT
            try:
                import jwt
                self._jwt = jwt
                self._jwt_exceptions = jwt
                self._jwt_available = True
                self._is_jose = False
            except ImportError:
                pass

    def _ensure_jwt(self) -> None:
        """Ensure JWT library is available"""
        if not self._jwt_available:
            raise AuthenticationError("JWT library not available. Install with: pip install pyjwt")

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user with credentials"""
        username = credentials.get("username")
        password = credentials.get("password")

        if not username or not password:
            raise AuthenticationError("Username and password required")

        # TODO(out_of_scope): Implement actual user authentication
        # This is a placeholder implementation
        if username == "admin" and password == "admin":
            return {
                "user_id": "admin",
                "username": username,
                "roles": ["admin"],
                "permissions": ["*"],
            }

        return None

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action on resource"""
        permissions = user.get("permissions", [])
        roles = user.get("roles", [])

        # Check for wildcard permission
        if "*" in permissions:
            return True

        # Check for admin role
        if "admin" in roles:
            return True

        # Check specific permission
        permission = f"{resource}:{action}"
        return permission in permissions

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt or fallback to PBKDF2"""
        try:
            import bcrypt
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            import bcrypt
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
            return hmac.compare_digest(computed_hash, hashed)

    def create_token(self, payload: Dict[str, Any]) -> str:
        """Create JWT token"""
        self._ensure_jwt()

        now = datetime.now(timezone.utc)
        token_payload = {
            **payload,
            "iat": now,
            "exp": now + timedelta(minutes=self.access_token_expire_minutes),
            "type": "access"
        }

        return self._jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        self._ensure_jwt()

        try:
            payload = self._jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # Check token type
            if payload.get("type") != "access":
                raise AuthenticationError("Invalid token type")

            return payload
        except AuthenticationError:
            # Re-raise our own exceptions
            raise
        except Exception as e:
            # Handle both jose and PyJWT exceptions
            error_msg = str(e).lower()
            if "expired" in error_msg:
                raise AuthenticationError("Token has expired")
            else:
                raise AuthenticationError("Invalid token")


class DatabaseSecurityProvider(ISecurityProvider):
    """Database-backed security provider with JWT authentication and RBAC"""

    def __init__(
        self,
        user_store: IUserStore,
        role_store: IRoleStore,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        self.user_store = user_store
        self.role_store = role_store
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user with database-backed credentials"""
        # Simplified implementation for now
        username = credentials.get("username")
        password = credentials.get("password")

        if not username or not password:
            raise AuthenticationError("Username and password required")

        # TODO: Implement full database authentication
        return None

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action based on roles and permissions"""
        permissions = user.get("permissions", [])
        roles = user.get("roles", [])

        # Check for wildcard permission (superuser)
        if "*" in permissions:
            return True

        # Check for admin role
        if "admin" in roles:
            return True

        # Check specific permission
        permission = f"{resource}:{action}"
        return permission in permissions

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt or fallback to PBKDF2"""
        try:
            import bcrypt
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            import bcrypt
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
            return hmac.compare_digest(computed_hash, hashed)


__all__ = [
    "JWTSecurityProvider",
    "DatabaseSecurityProvider",
]
