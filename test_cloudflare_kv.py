#!/usr/bin/env python3
"""
Test Cloudflare Workers KV Secret Provider
This script tests the new CloudflareKVSecretProvider implementation.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.config.secret_providers import SecretConfigProvider, CloudflareKVSecretProvider


async def test_cloudflare_kv():
    """Test Cloudflare Workers KV as secret storage"""
    print("🔍 Testing Cloudflare Workers KV Secret Provider")
    print("=" * 50)

    # Load environment variables (force reload)
    try:
        from dotenv import load_dotenv
        # Clear cached environment
        if 'CLOUDFLARE_ACCOUNT_ID' in os.environ:
            del os.environ['CLOUDFLARE_ACCOUNT_ID']
        load_dotenv(override=True)
        print("✅ Environment variables loaded (forced reload)")
    except ImportError:
        print("⚠️  python-dotenv not available, using system environment")

    # Check credentials
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')

    if not account_id or not api_token:
        print("❌ Missing Cloudflare credentials")
        return False

    print(f"✅ Account ID: {account_id[:8]}...{account_id[-8:]}")
    print(f"✅ API Token: {api_token[:8]}...{api_token[-8:]}")

    # Test auto-detection (should now use KV)
    print("\n🔍 Testing Auto-Detection (should use KV now)...")
    try:
        provider = SecretConfigProvider(provider_type="auto")
        info = provider.get_provider_info()

        print(f"✅ Provider Type: {info['provider_type']}")
        print(f"✅ Provider Class: {info['provider_class']}")
        print(f"✅ Expected: CloudflareKVSecretProvider")

        if info['provider_class'] == 'CloudflareKVSecretProvider':
            print("🎉 SUCCESS: Auto-detection now uses KV!")
        else:
            print("⚠️  Still using old provider")

    except Exception as e:
        print(f"❌ Auto-detection failed: {e}")
        return False

    # Test direct KV provider
    print("\n🔍 Testing Direct KV Provider...")
    try:
        kv_provider = CloudflareKVSecretProvider(
            account_id=account_id,
            api_token=api_token,
            namespace_name="secrets-test"
        )

        print("✅ KV Provider created")

        # Test health check
        print("\n🔍 Testing KV Health Check...")
        healthy = await kv_provider.health_check()

        if healthy:
            print("✅ KV Health check passed!")

            # Test basic operations
            await test_kv_operations(kv_provider)

        else:
            print("❌ KV Health check failed")
            return False

    except Exception as e:
        print(f"❌ KV Provider test failed: {e}")
        return False

    return True


async def test_kv_operations(provider):
    """Test basic KV operations"""
    print("\n🔍 Testing KV Operations...")

    test_key = "test-secret-kv"
    test_value = "test-value-123-kv"

    try:
        # Test set secret
        print(f"📝 Setting secret: {test_key}")
        success = await provider.set_secret(test_key, test_value)

        if success:
            print("✅ Secret set successfully")

            # Test get secret
            print(f"📖 Getting secret: {test_key}")
            secret = await provider.get_secret(test_key)

            if secret and secret.value == test_value:
                print("✅ Secret retrieved successfully")
                print(f"   Value: {secret.value}")

                # Test list secrets
                print("📋 Listing secrets...")
                secrets = await provider.list_secrets()
                print(f"✅ Found {len(secrets)} secrets")

                if test_key in secrets:
                    print(f"✅ Test secret found in list")

                # Clean up
                print(f"🗑️  Deleting test secret: {test_key}")
                deleted = await provider.delete_secret(test_key)

                if deleted:
                    print("✅ Test secret deleted successfully")
                else:
                    print("⚠️  Failed to delete test secret")

            else:
                print("❌ Failed to retrieve secret or value mismatch")
                if secret:
                    print(f"   Expected: {test_value}")
                    print(f"   Got: {secret.value}")

        else:
            print("❌ Failed to set secret")

    except Exception as e:
        print(f"❌ KV operations failed: {e}")


async def test_migration_scenario():
    """Test migration from environment variables to KV"""
    print("\n🔄 Testing Migration Scenario...")

    # Simulate migrating existing secrets
    env_secrets = {
        'DATABASE_PASSWORD': os.getenv('DATABASE_PASSWORD'),
        'JWT_SECRET_KEY': os.getenv('JWT_SECRET_KEY'),
        'API_KEY': os.getenv('API_KEY')
    }

    available_secrets = {k: v for k, v in env_secrets.items() if v}

    if not available_secrets:
        print("⚠️  No environment secrets to migrate")
        return

    print(f"📦 Found {len(available_secrets)} secrets to migrate:")
    for key in available_secrets:
        print(f"   - {key}")

    try:
        provider = SecretConfigProvider(provider_type="auto")

        # Test that we can still get secrets (should fall back to env)
        for key, expected_value in available_secrets.items():
            secret = await provider.get_secret(key.lower().replace('_', '-'))
            if secret:
                print(f"✅ Can access {key} via provider")
            else:
                # Try original key format
                secret = await provider.get_secret(key)
                if secret:
                    print(f"✅ Can access {key} via provider (original format)")
                else:
                    print(f"⚠️  Cannot access {key} via provider")

    except Exception as e:
        print(f"❌ Migration test failed: {e}")


async def main():
    """Main test function"""
    print("🚀 Cloudflare Workers KV Secret Provider Test")
    print("This test will verify the new KV-based secret storage.\n")

    try:
        success = await test_cloudflare_kv()

        if success:
            await test_migration_scenario()

            print("\n🎉 SUCCESS! Cloudflare Workers KV integration is working!")
            print("\n📝 Benefits of KV over Secrets Store:")
            print("   ✅ Available now (no beta access needed)")
            print("   ✅ Global edge distribution")
            print("   ✅ Fast access (sub-millisecond)")
            print("   ✅ Encrypted at rest")
            print("   ✅ Easy to use and manage")

            print("\n🔧 Usage in your FastAPI app:")
            print("   provider = SecretConfigProvider()  # Auto-detects KV")
            print("   secret = await provider.get_secret('database-password')")

        else:
            print("\n⚠️  KV test failed - check your API token permissions")

    except KeyboardInterrupt:
        print("\n\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
