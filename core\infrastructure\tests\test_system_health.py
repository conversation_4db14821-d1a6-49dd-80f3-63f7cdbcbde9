"""
Test SystemHealthCheck implementation
"""

import pytest
from datetime import datetime

from core.infrastructure.health import SystemHealthCheck


@pytest.fixture
def health_checker():
    """Create a SystemHealthCheck instance for testing"""
    return SystemHealthCheck(app_name="Test App", version="1.0.0")


@pytest.mark.asyncio
async def test_check_health_returns_healthy_status(health_checker):
    """Test that health check returns healthy status"""
    result = await health_checker.check_health()

    assert result["status"] == "healthy"
    assert result["version"] == "1.0.0"
    assert result["service"] == "Test App"
    assert "timestamp" in result
    assert "uptime" in result
    assert isinstance(result["uptime"], float)
    assert result["uptime"] >= 0


@pytest.mark.asyncio
async def test_check_health_includes_subsystem_checks(health_checker):
    """Test that health check includes subsystem status"""
    result = await health_checker.check_health()

    assert "checks" in result
    checks = result["checks"]

    # Verify all expected subsystems are checked
    expected_checks = ["database", "cache", "memory", "disk"]
    for check in expected_checks:
        assert check in checks
        # Check that each subsystem check has the expected structure
        assert "status" in checks[check]
        assert "message" in checks[check]
        assert "timestamp" in checks[check]
        # Database and cache should be skipped (no URLs configured)
        # Memory and disk should be healthy (assuming normal system)
        if check in ["database", "cache"]:
            assert checks[check]["status"] == "skipped"
        else:  # memory, disk
            assert checks[check]["status"] in ["healthy", "unhealthy"]  # Could be either depending on system


@pytest.mark.asyncio
async def test_check_readiness(health_checker):
    """Test readiness check"""
    result = await health_checker.check_readiness()

    assert result["status"] == "ready"
    assert "timestamp" in result
    assert "service" in result


@pytest.mark.asyncio
async def test_check_liveness(health_checker):
    """Test liveness check"""
    result = await health_checker.check_liveness()

    assert result["status"] == "alive"
    assert "timestamp" in result
    assert "service" in result


def test_health_checker_properties(health_checker):
    """Test health checker properties"""
    assert health_checker.name == "Test App"


@pytest.mark.asyncio
async def test_multiple_health_checks_show_increasing_uptime(health_checker):
    """Test that multiple health checks show increasing uptime"""
    import asyncio

    result1 = await health_checker.check_health()
    uptime1 = result1["uptime"]

    # Wait a small amount of time
    await asyncio.sleep(0.01)

    result2 = await health_checker.check_health()
    uptime2 = result2["uptime"]

    assert uptime2 > uptime1


@pytest.mark.asyncio
async def test_timestamp_format(health_checker):
    """Test that timestamp is in correct ISO format"""
    result = await health_checker.check_health()
    timestamp = result["timestamp"]

    # Should be able to parse the timestamp
    parsed_time = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
    assert isinstance(parsed_time, datetime)
