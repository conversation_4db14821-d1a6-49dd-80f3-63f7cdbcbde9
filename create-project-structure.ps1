# create-project-structure.ps1
# FastAPI Core Framework Project Structure Creator
# Run: .\create-project-structure.ps1 -ProjectName "my-fastapi-project"

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectName = "fastapi-core-framework",
    
    [Parameter(Mandatory=$false)]
    [string]$Path = ".",
    
    [Parameter(Mandatory=$false)]
    [switch]$IncludeExampleCode = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$InitGit = $true
)

# Colors for output
$colors = @{
    Success = "Green"
    Info = "Cyan"
    Warning = "Yellow"
    Error = "Red"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Type = "Info"
    )
    Write-Host $Message -ForegroundColor $colors[$Type]
}

function New-DirectoryStructure {
    param([string]$BasePath)
    
    Write-ColorOutput "🚀 Creating FastAPI Core Framework Project Structure" "Info"
    Write-ColorOutput "=================================================" "Info"
    
    # Create base project directory
    $projectPath = Join-Path $Path $ProjectName
    if (Test-Path $projectPath) {
        Write-ColorOutput "⚠️  Directory $projectPath already exists!" "Warning"
        $response = Read-Host "Do you want to continue? (y/n)"
        if ($response -ne 'y') {
            Write-ColorOutput "Aborted." "Error"
            return
        }
    }
    
    # Define directory structure
    $directories = @(
        # Core directories
        "core",
        "core/tests",
        
        # Services
        "services",
        "services/translation",
        "services/translation/tests",
        "services/auth",
        "services/auth/tests",
        "services/notification",
        "services/notification/tests",
        
        # Monitor
        "monitor",
        "monitor/monitor_lib",
        "monitor/web_monitor",
        
        # Nginx
        "nginx",
        "nginx/conf.d",
        "nginx/ssl",
        
        # Scripts
        "scripts",
        
        # Kubernetes
        "k8s",
        "k8s/core",
        "k8s/services",
        "k8s/monitoring",
        
        # Helm
        "helm",
        "helm/fastapi-core",
        "helm/fastapi-core/templates",
        "helm/fastapi-core/charts",
        
        # Terraform
        "terraform",
        "terraform/modules",
        "terraform/modules/eks",
        "terraform/modules/gke",
        "terraform/modules/aks",
        "terraform/environments",
        "terraform/environments/dev",
        "terraform/environments/staging",
        "terraform/environments/prod",
        
        # GitHub
        ".github",
        ".github/workflows",
        ".github/ISSUE_TEMPLATE",
        
        # Documentation
        "docs",
        "docs/api",
        "docs/api/services",
        "docs/deployment",
        "docs/development",
        "docs/monitoring",
        
        # Tests
        "tests",
        "tests/integration",
        "tests/performance",
        
        # Migrations
        "migrations",
        "migrations/versions",
        
        # Monitoring configs
        "monitoring",
        "monitoring/prometheus",
        "monitoring/prometheus/rules",
        "monitoring/grafana",
        "monitoring/grafana/dashboards",
        "monitoring/grafana/provisioning",
        "monitoring/alerts"
    )
    
    # Create directories
    Write-ColorOutput "`n📁 Creating directories..." "Info"
    foreach ($dir in $directories) {
        $fullPath = Join-Path $projectPath $dir
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  ✓ $dir" -ForegroundColor DarkGray
    }
    
    # Create files with content
    Write-ColorOutput "`n📄 Creating files..." "Info"
    
    # Create .env.example
    $envExample = @'
# Core API Settings
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
CORE_API_KEY=your-core-api-key-here

# Database
DATABASE_URL=****************************************/core_db

# Redis
REDIS_URL=redis://redis:6379/0

# OpenAI (for translation service)
OPENAI_API_KEY=your-openai-api-key-here

# Service Discovery (optional)
SERVICE_DISCOVERY_URL=http://consul:8500

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
'@
    Set-Content -Path (Join-Path $projectPath ".env.example") -Value $envExample -Encoding UTF8
    Write-Host "  ✓ .env.example" -ForegroundColor DarkGray

    # Create .gitignore
    $gitignore = @'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
*.egg-info/
dist/
build/

# Environment
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Docker
docker-compose.override.yml

# SSL certificates
nginx/ssl/*.pem
nginx/ssl/*.key
nginx/ssl/*.crt

# Logs
logs/
*.log

# Database
*.db
*.sqlite
migrations/versions/*.pyc

# Monitoring
monitoring/prometheus/data/
monitoring/grafana/data/

# Terraform
terraform/.terraform/
terraform/*.tfstate
terraform/*.tfstate.backup
terraform/.terraform.lock.hcl

# Kubernetes
k8s/secrets-prod.yaml
k8s/configmap-prod.yaml

# Temp files
*.tmp
*.bak
.cache/
'@
    Set-Content -Path (Join-Path $projectPath ".gitignore") -Value $gitignore -Encoding UTF8
    Write-Host "  ✓ .gitignore" -ForegroundColor DarkGray

    # Create README.md
    $readme = @"
# $ProjectName

FastAPI Core Framework - A production-ready microservices framework built with Python and FastAPI.

## Features

- 🚀 Async-first design with FastAPI
- 🔌 Plugin architecture for easy service integration
- 🔒 Multi-layer security with API keys and JWT
- 📊 Built-in health monitoring and metrics
- 🐳 Docker and Kubernetes ready
- 📡 Terminal monitoring tool

## Quick Start

\`\`\`bash
# Setup
./scripts/setup.sh

# Start services
docker-compose up -d

# Run monitor
./scripts/monitor.sh --docker
\`\`\`

## Documentation

See the [docs](./docs) directory for detailed documentation.
"@
    Set-Content -Path (Join-Path $projectPath "README.md") -Value $readme -Encoding UTF8
    Write-Host "  ✓ README.md" -ForegroundColor DarkGray

    # Create Makefile
    $makefile = @'
.PHONY: help build up down logs monitor test lint clean

help:
	@echo "Available commands:"
	@echo "  make build    - Build all containers"
	@echo "  make up       - Start all services"
	@echo "  make down     - Stop all services"
	@echo "  make logs     - View logs"
	@echo "  make monitor  - Run terminal monitor"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make clean    - Clean up"

build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f

monitor:
	@./scripts/monitor.sh --docker

monitor-local:
	@python monitor/monitor.py --url $(CORE_API_URL) --api-key $(CORE_API_KEY)

test:
	@docker-compose run --rm core pytest
	@docker-compose run --rm translation-service pytest

lint:
	@docker-compose run --rm core flake8 .
	@docker-compose run --rm core black --check .

clean:
	@docker-compose down -v
	@find . -type d -name __pycache__ -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete

dev-setup:
	@./scripts/setup.sh
	@cp .env.example .env
	@echo "Please edit .env with your configuration"
'@
    Set-Content -Path (Join-Path $projectPath "Makefile") -Value $makefile -Encoding UTF8
    Write-Host "  ✓ Makefile" -ForegroundColor DarkGray

    # Create docker-compose.yml
    $dockerCompose = @'
version: '3.8'

services:
  core-api:
    build:
      context: ./core
      dockerfile: Dockerfile
    container_name: fastapi-core
    ports:
      - '8000:8000'
      - '9090:9090'
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-here}
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/core_db
    depends_on:
      - redis
      - postgres
    networks:
      - app-network

  translation-service:
    build:
      context: ./services/translation
      dockerfile: Dockerfile
    container_name: translation-service
    ports:
      - '8001:8001'
    environment:
      - CORE_API_URL=http://core-api:8000
      - CORE_API_KEY=${CORE_API_KEY:-your-core-api-key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - core-api
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    container_name: fastapi-redis
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    networks:
      - app-network

  postgres:
    image: postgres:15-alpine
    container_name: fastapi-postgres
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=core_db
    ports:
      - '5432:5432'
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: fastapi-nginx
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - core-api
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
'@
    Set-Content -Path (Join-Path $projectPath "docker-compose.yml") -Value $dockerCompose -Encoding UTF8
    Write-Host "  ✓ docker-compose.yml" -ForegroundColor DarkGray

    # Create core/requirements.txt
    $coreRequirements = @'
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.3
pydantic-settings==2.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
httpx==0.26.0
redis==5.0.1
asyncpg==0.29.0
sqlalchemy==2.0.25
alembic==1.13.1
prometheus-client==0.19.0
opentelemetry-api==1.22.0
opentelemetry-sdk==1.22.0
opentelemetry-instrumentation-fastapi==0.43b0
python-dotenv==1.0.0
'@
    Set-Content -Path (Join-Path $projectPath "core/requirements.txt") -Value $coreRequirements -Encoding UTF8
    Write-Host "  ✓ core/requirements.txt" -ForegroundColor DarkGray

    # Create core/Dockerfile
    $coreDockerfile = @'
FROM python:3.11-slim

WORKDIR /app

RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

RUN pip install rich httpx

COPY . .

RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000 9090

CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
'@
    Set-Content -Path (Join-Path $projectPath "core/Dockerfile") -Value $coreDockerfile -Encoding UTF8
    Write-Host "  ✓ core/Dockerfile" -ForegroundColor DarkGray

    # Create empty __init__.py files
    $initFiles = @(
        "core/__init__.py",
        "monitor/__init__.py",
        "monitor/monitor_lib/__init__.py",
        "services/__init__.py",
        "services/translation/__init__.py",
        "tests/__init__.py"
    )
    
    foreach ($file in $initFiles) {
        $filePath = Join-Path $projectPath $file
        Set-Content -Path $filePath -Value "" -Encoding UTF8
    }
    Write-Host "  ✓ Python __init__.py files" -ForegroundColor DarkGray

    # Create monitor/requirements.txt
    $monitorRequirements = @'
rich==13.7.0
httpx==0.26.0
'@
    Set-Content -Path (Join-Path $projectPath "monitor/requirements.txt") -Value $monitorRequirements -Encoding UTF8
    Write-Host "  ✓ monitor/requirements.txt" -ForegroundColor DarkGray

    # Create scripts/monitor.sh
    $monitorScript = @'
#!/bin/bash
# Monitor access script

CORE_URL="${CORE_API_URL:-http://localhost:8000}"
API_KEY="${CORE_API_KEY:-}"
CONTAINER_NAME="${CONTAINER_NAME:-fastapi-core}"

case $1 in
    --local)
        python monitor/monitor.py --url "$CORE_URL" --api-key "$API_KEY"
        ;;
    --docker)
        docker exec -it "$CONTAINER_NAME" python /usr/local/bin/monitor \
            --url http://localhost:8000 --api-key "$API_KEY"
        ;;
    --remote)
        shift
        python monitor/monitor.py --url "$1" --api-key "$API_KEY"
        ;;
    *)
        echo "Usage: $0 [--local|--docker|--remote <url>]"
        exit 1
        ;;
esac
'@
    Set-Content -Path (Join-Path $projectPath "scripts/monitor.sh") -Value $monitorScript -Encoding UTF8
    Write-Host "  ✓ scripts/monitor.sh" -ForegroundColor DarkGray

    # Create scripts/setup.sh
    $setupScript = @'
#!/bin/bash
set -e

echo "🚀 FastAPI Core Framework Setup"
echo "==============================="

# Check dependencies
command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed."; exit 1; }

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Build containers
docker-compose build

echo "✅ Setup complete!"
'@
    Set-Content -Path (Join-Path $projectPath "scripts/setup.sh") -Value $setupScript -Encoding UTF8
    Write-Host "  ✓ scripts/setup.sh" -ForegroundColor DarkGray

    # Create services/translation/requirements.txt
    $translationRequirements = @'
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.5.3
httpx==0.26.0
openai==1.10.0
python-multipart==0.0.6
python-dotenv==1.0.0
'@
    Set-Content -Path (Join-Path $projectPath "services/translation/requirements.txt") -Value $translationRequirements -Encoding UTF8
    Write-Host "  ✓ services/translation/requirements.txt" -ForegroundColor DarkGray

    # Create services/translation/Dockerfile
    $translationDockerfile = @'
FROM python:3.11-slim

WORKDIR /app

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8001

CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]
'@
    Set-Content -Path (Join-Path $projectPath "services/translation/Dockerfile") -Value $translationDockerfile -Encoding UTF8
    Write-Host "  ✓ services/translation/Dockerfile" -ForegroundColor DarkGray

    # Create nginx/nginx.conf
    $nginxConf = @'
events {
    worker_connections 1024;
}

http {
    upstream core_api {
        server core-api:8000;
    }

    server {
        listen 80;
        server_name localhost;

        location /api/v1/ {
            proxy_pass http://core_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
'@
    Set-Content -Path (Join-Path $projectPath "nginx/nginx.conf") -Value $nginxConf -Encoding UTF8
    Write-Host "  ✓ nginx/nginx.conf" -ForegroundColor DarkGray

    # Create GitHub Actions CI workflow
    $ciWorkflow = @'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r core/requirements.txt
        pip install pytest flake8 black
    
    - name: Run tests
      run: pytest
    
    - name: Run linting
      run: |
        flake8 .
        black --check .
'@
    Set-Content -Path (Join-Path $projectPath ".github/workflows/ci.yml") -Value $ciWorkflow -Encoding UTF8
    Write-Host "  ✓ .github/workflows/ci.yml" -ForegroundColor DarkGray

    # Create docs/README.md
    $docsReadme = @'
# FastAPI Core Framework Documentation

## Table of Contents

1. [Architecture](./architecture.md)
2. [API Documentation](./api/)
3. [Deployment Guides](./deployment/)
4. [Development](./development/)
5. [Monitoring](./monitoring/)

## Quick Links

- [Getting Started](./development/setup.md)
- [Core API Reference](./api/core-api.md)
- [Terminal Monitor Guide](./monitoring/terminal-monitor.md)
'@
    Set-Content -Path (Join-Path $projectPath "docs/README.md") -Value $docsReadme -Encoding UTF8
    Write-Host "  ✓ docs/README.md" -ForegroundColor DarkGray

    # Create k8s/namespace.yaml
    $k8sNamespace = @'
apiVersion: v1
kind: Namespace
metadata:
  name: fastapi-core
'@
    Set-Content -Path (Join-Path $projectPath "k8s/namespace.yaml") -Value $k8sNamespace -Encoding UTF8
    Write-Host "  ✓ k8s/namespace.yaml" -ForegroundColor DarkGray

    # Create tests/conftest.py
    $conftest = @'
import pytest
from httpx import AsyncClient

@pytest.fixture
async def client():
    from core.main import create_app
    app = create_app()
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
'@
    Set-Content -Path (Join-Path $projectPath "tests/conftest.py") -Value $conftest -Encoding UTF8
    Write-Host "  ✓ tests/conftest.py" -ForegroundColor DarkGray

    # Make shell scripts executable (on Unix-like systems)
    if ($IsLinux -or $IsMacOS) {
        Get-ChildItem -Path $projectPath -Filter "*.sh" -Recurse | ForEach-Object {
            chmod +x $_.FullName
        }
    }
    
    # Initialize Git repository if requested
    if ($InitGit) {
        Write-ColorOutput "`n🔧 Initializing Git repository..." "Info"
        Push-Location $projectPath
        git init | Out-Null
        git add . | Out-Null
        git commit -m "Initial commit: FastAPI Core Framework structure" | Out-Null
        Pop-Location
        Write-ColorOutput "  ✓ Git repository initialized" "Success"
    }
    
    # Create sample Python files if requested
    if ($IncludeExampleCode) {
        Write-ColorOutput "`n📝 Creating sample Python files..." "Info"
        
        # Create core/main.py
        $coreMainPy = @'
# core/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="FastAPI Core Framework")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "FastAPI Core Framework"}

@app.get("/health")
async def health():
    return {"status": "healthy"}
'@
        Set-Content -Path (Join-Path $projectPath "core/main.py") -Value $coreMainPy -Encoding UTF8
        Write-Host "  ✓ core/main.py" -ForegroundColor DarkGray
        
        # Create monitor/monitor.py placeholder
        $monitorPy = @'
#!/usr/bin/env python3
# monitor.py - Terminal monitoring tool placeholder

print("Terminal monitor - Please implement the full monitor.py from the artifacts")
'@
        Set-Content -Path (Join-Path $projectPath "monitor/monitor.py") -Value $monitorPy -Encoding UTF8
        Write-Host "  ✓ monitor/monitor.py" -ForegroundColor DarkGray
    }
    
    Write-ColorOutput "`n✅ Project structure created successfully!" "Success"
    Write-ColorOutput "`nProject location: $projectPath" "Info"
    Write-ColorOutput "`nNext steps:" "Info"
    Write-ColorOutput "  1. cd $ProjectName" "Info"
    Write-ColorOutput "  2. Copy your Python implementation files" "Info"
    Write-ColorOutput "  3. Edit .env with your configuration" "Info"
    Write-ColorOutput "  4. Run: docker-compose up -d" "Info"
    Write-ColorOutput "  5. Access API at: http://localhost:8000/docs" "Info"
    Write-ColorOutput "`nFor Windows users:" "Warning"
    Write-ColorOutput "  - Use Git Bash or WSL to run .sh scripts" "Warning"
    Write-ColorOutput "  - Or use 'make' commands if you have Make installed" "Warning"
}

# Main execution
try {
    New-DirectoryStructure -BasePath $Path
} catch {
    Write-ColorOutput "Error: $_" "Error"
    exit 1
}