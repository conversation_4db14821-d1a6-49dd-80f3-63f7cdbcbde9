#!/usr/bin/env python3
"""
Setup script for Redis Service Registry

This script helps you configure and test the Redis-backed service registry
with your Redis instance.
"""

import asyncio
import os
import getpass
from typing import Optional

from core.infrastructure.redis_service_registry import RedisServiceReg<PERSON>ry


def get_redis_credentials() -> str:
    """Get Redis credentials from user input"""
    print("🔧 Redis Service Registry Setup")
    print("=" * 40)
    print("Please provide your Redis connection details.")
    print()
    
    # Check for environment variable first
    redis_url = os.getenv("REDIS_URL")
    if redis_url:
        print(f"Found REDIS_URL in environment: {redis_url}")
        use_env = input("Use this Redis URL? [Y/n]: ").lower().strip()
        if use_env not in ['n', 'no']:
            return redis_url
    
    # Get Redis details manually
    print("Enter Redis connection details:")
    host = input("Redis host (default: localhost): ").strip() or "localhost"
    port = input("Redis port (default: 6379): ").strip() or "6379"
    
    # Check if password is needed
    password = getpass.getpass("Redis password (press Enter if none): ").strip()
    database = input("Redis database number (default: 0): ").strip() or "0"
    
    # Build Redis URL
    if password:
        redis_url = f"redis://:{password}@{host}:{port}/{database}"
    else:
        redis_url = f"redis://{host}:{port}/{database}"
    
    return redis_url


async def test_redis_connection(redis_url: str) -> bool:
    """Test Redis connection"""
    try:
        import redis.asyncio as aioredis
        
        print("\n🔍 Testing Redis connection...")
        redis_client = await aioredis.from_url(redis_url, encoding="utf-8", decode_responses=True)
        
        # Test basic operations
        await redis_client.set("test_connection", "ok", ex=5)
        value = await redis_client.get("test_connection")
        await redis_client.delete("test_connection")
        await redis_client.close()
        
        if value == "ok":
            print("   ✅ Redis connection successful")
            return True
        else:
            print("   ❌ Redis connection test failed")
            return False
            
    except ImportError:
        print("   ❌ Redis package not installed. Install with: pip install redis")
        return False
    except Exception as e:
        print(f"   ❌ Redis connection failed: {e}")
        return False


async def setup_service_registry(redis_url: str) -> Optional[RedisServiceRegistry]:
    """Setup and configure the service registry"""
    try:
        print("\n🚀 Setting up Redis Service Registry...")
        print("=" * 50)
        
        # Configuration options
        print("\n1. Configuration options:")
        key_prefix = input("   Key prefix (default: laneswap:service_registry:): ").strip() or "laneswap:service_registry:"
        
        ttl_input = input("   Service TTL in seconds (default: 300): ").strip()
        ttl = int(ttl_input) if ttl_input.isdigit() else 300
        
        health_interval_input = input("   Health check interval in seconds (default: 30): ").strip()
        health_interval = int(health_interval_input) if health_interval_input.isdigit() else 30
        
        # Create registry
        print("\n2. Creating service registry...")
        registry = RedisServiceRegistry(
            redis_url=redis_url,
            key_prefix=key_prefix,
            default_ttl=ttl,
            health_check_interval=health_interval
        )
        
        print("   ✅ Service registry created")
        print(f"      Redis URL: {redis_url}")
        print(f"      Key prefix: {key_prefix}")
        print(f"      Service TTL: {ttl} seconds")
        print(f"      Health check interval: {health_interval} seconds")
        
        return registry
        
    except Exception as e:
        print(f"   ❌ Failed to setup service registry: {e}")
        return None


async def demo_service_registry(registry: RedisServiceRegistry):
    """Demonstrate service registry functionality"""
    print("\n3. Demonstrating service registry functionality...")
    
    try:
        # Register demo services
        demo_services = [
            {
                "name": "demo-translation-service",
                "url": "http://localhost:8001",
                "version": "1.0.0",
                "metadata": {"type": "translation", "demo": True}
            },
            {
                "name": "demo-auth-service", 
                "url": "http://localhost:8002",
                "version": "1.0.0",
                "metadata": {"type": "authentication", "demo": True}
            }
        ]
        
        print("\n   Registering demo services...")
        for service_data in demo_services:
            result = await registry.register_service(
                name=service_data["name"],
                url=service_data["url"],
                version=service_data["version"],
                metadata=service_data["metadata"]
            )
            
            if result["success"]:
                print(f"   ✅ Registered: {service_data['name']}")
                print(f"      API Key: {result['api_key'][:20]}...")
            else:
                print(f"   ❌ Failed: {service_data['name']} - {result['error']}")
        
        # List services
        print("\n   Listing registered services...")
        services = await registry.list_services()
        for service in services:
            print(f"   • {service['name']} ({service['version']})")
            print(f"     URL: {service['url']}")
            print(f"     Healthy: {'✅' if service['is_healthy'] else '❌'}")
            print(f"     Registered: {service['registered_at']}")
        
        # Test heartbeat
        if services:
            service_name = services[0]["name"]
            print(f"\n   Testing heartbeat for {service_name}...")
            result = await registry.heartbeat(service_name)
            if result["success"]:
                print(f"   ✅ Heartbeat successful")
            else:
                print(f"   ❌ Heartbeat failed: {result['error']}")
        
        # Get statistics
        print("\n   Service registry statistics:")
        stats = await registry.get_service_stats()
        print(f"   • Total services: {stats['total_services']}")
        print(f"   • Healthy services: {stats['healthy_services']}")
        print(f"   • Health percentage: {stats['health_percentage']:.1f}%")
        
        # Start health monitoring
        print("\n   Starting health monitoring...")
        await registry.start_monitoring()
        print("   ✅ Health monitoring started")
        print("   ⏱️  Monitoring will run in background...")
        
        # Ask if user wants to clean up demo services
        print("\n   Demo complete!")
        cleanup = input("   Clean up demo services? [Y/n]: ").lower().strip()
        if cleanup not in ['n', 'no']:
            print("   Cleaning up demo services...")
            for service_data in demo_services:
                result = await registry.unregister_service(service_data["name"])
                if result["success"]:
                    print(f"   ✅ Removed: {service_data['name']}")
                else:
                    print(f"   ❌ Failed to remove: {service_data['name']}")
        
        await registry.stop_monitoring()
        
    except Exception as e:
        print(f"   ❌ Demo failed: {e}")


async def update_application_config(redis_url: str):
    """Help user update application configuration"""
    print("\n4. Application Configuration...")
    print("=" * 40)
    print("To use Redis Service Registry in your application:")
    print()
    print("1. Set environment variable:")
    print(f"   export REDIS_URL='{redis_url}'")
    print()
    print("2. Or update your .env file:")
    print(f"   REDIS_URL={redis_url}")
    print()
    print("3. The ApplicationFactory will automatically use Redis Service Registry")
    print("   when REDIS_URL is configured.")
    print()
    print("4. Your services will now use distributed service discovery!")
    print()
    
    # Offer to create .env file
    create_env = input("Create/update .env file with Redis URL? [Y/n]: ").lower().strip()
    if create_env not in ['n', 'no']:
        try:
            # Read existing .env file
            env_content = ""
            if os.path.exists(".env"):
                with open(".env", "r") as f:
                    env_content = f.read()
            
            # Update or add REDIS_URL
            lines = env_content.split('\n')
            redis_url_found = False
            
            for i, line in enumerate(lines):
                if line.startswith('REDIS_URL='):
                    lines[i] = f"REDIS_URL={redis_url}"
                    redis_url_found = True
                    break
            
            if not redis_url_found:
                lines.append(f"REDIS_URL={redis_url}")
            
            # Write back to .env
            with open(".env", "w") as f:
                f.write('\n'.join(lines))
            
            print("   ✅ .env file updated with Redis URL")
            
        except Exception as e:
            print(f"   ❌ Failed to update .env file: {e}")


async def main():
    """Main setup function"""
    try:
        # Get Redis credentials
        redis_url = get_redis_credentials()
        
        # Test connection
        if not await test_redis_connection(redis_url):
            print("\n❌ Cannot proceed - Redis connection failed")
            print("Please check your Redis credentials and try again.")
            return
        
        # Setup service registry
        registry = await setup_service_registry(redis_url)
        if not registry:
            print("\n❌ Cannot proceed - Service registry setup failed")
            return
        
        # Run demo
        run_demo = input("\nRun service registry demo? [Y/n]: ").lower().strip()
        if run_demo not in ['n', 'no']:
            await demo_service_registry(registry)
        
        # Update application config
        await update_application_config(redis_url)
        
        # Cleanup
        await registry.dispose()
        
        print("\n🎉 Redis Service Registry Setup Complete!")
        print("=" * 50)
        print("✅ Redis connection verified")
        print("✅ Service registry configured")
        print("✅ Application ready for distributed service discovery")
        print()
        print("Next steps:")
        print("1. Start your application with the new Redis configuration")
        print("2. Register your services using the service registry")
        print("3. Monitor service health and discovery in real-time")
        
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
