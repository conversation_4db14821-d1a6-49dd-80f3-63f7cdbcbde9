﻿#!/bin/bash
# Monitor access script

CORE_URL="${CORE_API_URL:-http://localhost:8000}"
API_KEY="${CORE_API_KEY:-}"
CONTAINER_NAME="${CONTAINER_NAME:-fastapi-core}"

case $1 in
    --local)
        python monitor/monitor.py --url "$CORE_URL" --api-key "$API_KEY"
        ;;
    --docker)
        docker exec -it "$CONTAINER_NAME" python /usr/local/bin/monitor \
            --url http://localhost:8000 --api-key "$API_KEY"
        ;;
    --remote)
        shift
        python monitor/monitor.py --url "$1" --api-key "$API_KEY"
        ;;
    *)
        echo "Usage: $0 [--local|--docker|--remote <url>]"
        exit 1
        ;;
esac
