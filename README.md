﻿# FastAPI Core Framework Documentation

## Overview

The FastAPI Core Framework is an advanced, production-ready microservices framework built with Python and FastAPI. It provides a scalable, secure, and modular architecture for building and managing API services.

## Key Features

- **Async-First Design**: Built on FastAPI with full async/await support
- **Plugin Architecture**: Easy service registration and discovery
- **Security Barrier**: Multi-layer security with API keys, JWT tokens, and rate limiting
- **Health Monitoring**: Automatic health checks for all registered services
- **Metrics Collection**: Built-in metrics and monitoring capabilities
- **Container-Ready**: Docker and docker-compose configurations included
- **Terminal Monitoring**: Real-time ASCII-based monitoring tool

## Architecture

```
┌─────────────────────────────────────────┐
│           NGINX (Reverse Proxy)         │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            CORE API FRAMEWORK           │
│  ┌─────────────────────────────────┐   │
│  │   Service Registry & Discovery   │   │
│  ├─────────────────────────────────┤   │
│  │      Security Barrier           │   │
│  ├─────────────────────────────────┤   │
│  │   Health Checker & Monitoring   │   │
│  └─────────────────────────────────┘   │
└────────────────┬────────────────────────┘
                 │
    ┌────────────┴────────────┐
    │                         │
┌───▼────┐            ┌──────▼──────┐
│Service │            │  Service    │
│   1    │            │     2       │
└────────┘            └─────────────┘
```

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <your-repo>
cd fastapi-core-framework

# Generate secure secrets
python scripts/generate_secrets.py --type all --output .env.secrets

# Create environment file
cp .env.example .env
# Edit .env with your configurations and generated secrets
```

### 2. Run with Docker Compose

```bash
# Start all services
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Access the Services

- Core API: http://localhost:8000
- Translation Service: http://localhost:8001
- API Documentation: http://localhost:8000/docs
- Metrics: http://localhost:9090

## API Usage

### Authentication

The framework uses API key authentication. Generate an API key for your service:

```python
# Example: Register a service and get API key
import httpx

response = httpx.post(
    "http://localhost:8000/api/v1/services/register",
    json={
        "name": "my-service",
        "url": "http://my-service:8080",
        "version": "1.0.0"
    },
    headers={"X-API-Key": "your-master-api-key"}
)

api_key = response.json()["api_key"]
```

### Core API Endpoints

#### Health Check
```bash
GET /api/v1/health
```

#### List Services
```bash
GET /api/v1/services
Headers: X-API-Key: your-api-key
```

#### Register Service
```bash
POST /api/v1/services/register
Headers: X-API-Key: your-api-key
Body: {
    "name": "service-name",
    "url": "http://service-url:port",
    "version": "1.0.0",
    "health_endpoint": "/health",
    "metadata": {}
}
```

#### Call Service
```bash
POST /api/v1/services/call
Headers: X-API-Key: your-api-key
Body: {
    "service": "translation-service",
    "endpoint": "/translate",
    "method": "POST",
    "data": {
        "text": "Hello world",
        "target_language": "es"
    }
}
```

#### Restart Service
```bash
POST /api/v1/services/{service_name}/restart
Headers: X-API-Key: your-api-key
```

## Creating a New Service

### 1. Service Template

```python
# services/my_service/main.py
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
import httpx

SERVICE_NAME = "my-service"
SERVICE_VERSION = "1.0.0"
CORE_API_URL = os.getenv("CORE_API_URL", "http://core-api:8000")
CORE_API_KEY = os.getenv("CORE_API_KEY")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Register with core on startup
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{CORE_API_URL}/api/v1/services/register",
            json={
                "name": SERVICE_NAME,
                "url": f"http://{SERVICE_NAME}:8002",
                "version": SERVICE_VERSION
            },
            headers={"X-API-Key": CORE_API_KEY}
        )
    yield
    # Cleanup on shutdown

app = FastAPI(title="My Service", lifespan=lifespan)

@app.get("/health")
async def health():
    return {"status": "healthy"}

# Add your endpoints here
```

### 2. Dockerfile

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8002"]
```

### 3. Add to docker-compose.yml

```yaml
my-service:
  build: ./services/my_service
  environment:
    - CORE_API_URL=http://core-api:8000
    - CORE_API_KEY=${CORE_API_KEY}
  depends_on:
    - core-api
  networks:
    - app-network
```

## Security Features

### 1. API Key Management
- Automatic API key generation for services
- Key validation on all protected endpoints
- Service-specific permissions

### 2. Rate Limiting
- Configurable rate limits per IP
- Burst handling
- Automatic cleanup of old requests

### 3. Security Headers
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Strict-Transport-Security

### 4. CORS Configuration
- Configurable allowed origins
- Credential support
- Method and header whitelisting

## Monitoring

### Terminal Monitor

Run the terminal monitoring tool:

```bash
# Install dependencies
pip install rich httpx

# Run monitor
python monitor.py --url http://localhost:8000 --api-key your-api-key

# Or use environment variables
export CORE_API_URL=http://localhost:8000
export CORE_API_KEY=your-api-key
python monitor.py
```

The monitor displays:
- Service status and health
- Response times
- System metrics
- ASCII visualization

### Health Checks

The framework automatically performs health checks every 30 seconds for all registered services. Access the health status:

```bash
GET /api/v1/health

Response:
{
    "status": "healthy|degraded|unhealthy",
    "services": {
        "total": 2,
        "healthy": 2,
        "unhealthy": 0
    },
    "details": [...]
}
```

## Best Practices

### 1. Service Design
- Keep services focused on single responsibilities
- Use async/await for all I/O operations
- Implement proper error handling
- Include comprehensive health checks

### 2. Security
- Never hardcode API keys
- Use environment variables for sensitive data
- Implement service-specific rate limiting
- Validate all input data

### 3. Performance
- Use connection pooling for HTTP clients
- Implement caching where appropriate
- Monitor response times
- Use background tasks for long operations

### 4. Deployment
- Use multi-stage Docker builds
- Run as non-root user
- Set resource limits
- Use health checks in Docker

## Scaling Strategies

### Horizontal Scaling
```yaml
# docker-compose.yml
translation-service:
  scale: 3  # Run 3 instances
  # ... rest of configuration
```

### Load Balancing
The framework supports multiple instances of services. The service registry can be extended to support load balancing strategies.

### Caching
Redis is included for caching. Example usage:

```python
import redis.asyncio as redis

# In your service
cache = redis.from_url("redis://redis:6379")
await cache.set("key", "value", ex=3600)  # 1 hour TTL
```

## Troubleshooting

### Common Issues

1. **Service Registration Fails**
   - Check CORE_API_KEY is set correctly
   - Verify core API is running
   - Check network connectivity

2. **Health Checks Failing**
   - Ensure health endpoint returns 200 status
   - Check service is accessible from core
   - Verify correct port configuration

3. **Rate Limiting**
   - Adjust RATE_LIMIT in settings
   - Implement service-specific limits
   - Use caching to reduce API calls

### Debug Mode

Enable debug mode for detailed logs:

```python
# .env
DEBUG=true
```

### Logs

View logs for specific services:

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f core-api
docker-compose logs -f translation-service
```

## Advanced Features

### 1. Service Mesh Integration
The framework can be integrated with service mesh solutions like Istio or Linkerd for advanced traffic management.

### 2. Distributed Tracing
OpenTelemetry support is included for distributed tracing:

```python
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

FastAPIInstrumentor.instrument_app(app)
```

### 3. Event-Driven Architecture
Extend the framework with message queues:

```python
# Add to your service
from aiokafka import AIOKafkaProducer

producer = AIOKafkaProducer(
    bootstrap_servers='kafka:9092'
)
await producer.start()
await producer.send("events", b"event-data")
```

## Future Improvements

### Planned Features
1. **GraphQL Gateway**: Unified GraphQL endpoint for all services
2. **Service Mesh**: Native service mesh support
3. **Circuit Breaker**: Automatic circuit breaking for failed services
4. **A/B Testing**: Built-in A/B testing capabilities
5. **Auto-scaling**: Kubernetes operator for auto-scaling

### Known Issues
1. **WebSocket Support**: Limited WebSocket support in service calls
2. **Large File Handling**: Streaming for large files needs improvement
3. **Service Dependencies**: Complex dependency graphs need better handling

### Contributing
1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Submit a pull request

## Performance Optimization

### 1. Database Connections
Use connection pooling:

```python
from sqlalchemy.ext.asyncio import create_async_engine

engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=0
)
```

### 2. Caching Strategy
Implement multi-level caching:
- Memory cache for hot data
- Redis for distributed cache
- HTTP caching headers

### 3. Async Best Practices
- Use `asyncio.gather()` for parallel operations
- Avoid blocking operations
- Use async context managers

## Production Deployment

### 1. Environment Variables

Generate secure secrets using the included script:

```bash
# Generate all required secrets
python scripts/generate_secrets.py --type all --output .env.secrets

# Generate specific secret types
python scripts/generate_secrets.py --type jwt --length 32
python scripts/generate_secrets.py --type api --prefix "prod_"

# Store in Cloudflare Secrets Store (recommended for production)
export CLOUDFLARE_ACCOUNT_ID="your-account-id"
export CLOUDFLARE_API_TOKEN="your-api-token"
python scripts/generate_secrets.py --type all --store cloudflare
```

Example production environment:
```bash
# Production .env
SECRET_KEY=<generated-strong-random-key>
JWT_SECRET_KEY=<generated-jwt-secret-key>
DEBUG=false
ALLOWED_HOSTS=["api.yourdomain.com"]
ALLOWED_ORIGINS=["https://yourdomain.com"]
```

### 2. SSL/TLS
Configure Nginx with SSL:

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    # ... rest of config
}
```

### 3. Monitoring Stack
Deploy with Prometheus and Grafana:

```yaml
# docker-compose.prod.yml
prometheus:
  image: prom/prometheus
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
```

## Summary

The FastAPI Core Framework provides a robust foundation for building microservices with Python. Its modular architecture, built-in security features, and comprehensive monitoring make it suitable for production deployments. The framework is designed to be extended and customized according to your specific needs.

For questions or support, please refer to the GitHub repository or open an issue.

## Service Management

### Service Restart Capability

The core system can restart any registered service that implements the restart endpoint. This is useful for:
- Applying configuration changes
- Recovering from error states
- Rolling updates

Services must implement the `/admin/restart` endpoint (or a custom endpoint specified during registration) that gracefully handles the restart request.

### Implementing Restart in Your Service

Use the service template to easily add restart capability:

```python
from services import create_service_app

# Create your service app with admin endpoints enabled
app = create_service_app(
    title="My Service",
    version="1.0.0",
    admin_enabled=True  # This enables the restart endpoint
)

# The rest of your service code...
```

The service template automatically implements a graceful restart that:
1. Responds to the restart request
2. Waits for the response to be sent
3. Terminates the process, allowing the container orchestrator to restart it