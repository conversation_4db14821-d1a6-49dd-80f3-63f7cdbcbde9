#!/usr/bin/env python3
"""
Migrate Secrets from Environment Variables to Cloudflare KV
"""

import asyncio
import os
from core.config.secret_providers import SecretConfigProvider

async def migrate_secrets():
    """Migrate existing environment secrets to Cloudflare KV"""
    print("🔄 Migrating Secrets to Cloudflare KV")
    print("=" * 40)
    
    # Load environment
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Initialize provider
    provider = SecretConfigProvider()
    
    # Check health
    healthy = await provider.health_check()
    if not healthy:
        print("❌ Secret provider is not healthy. Cannot migrate.")
        return False
    
    print("✅ Secret provider is healthy")
    
    # Define secrets to migrate
    secrets_to_migrate = {
        # Environment Variable -> KV Key
        'DATABASE_PASSWORD': 'database-password',
        'JWT_SECRET_KEY': 'jwt-secret-key', 
        'API_KEY': 'api-key',
        'REDIS_PASSWORD': 'redis-password',
        'ENCRYPTION_KEY': 'encryption-key',
        'SMTP_PASSWORD': 'smtp-password',
        'OAUTH_CLIENT_SECRET': 'oauth-client-secret',
        'WEBHOOK_SECRET': 'webhook-secret'
    }
    
    migrated = 0
    skipped = 0
    
    for env_var, kv_key in secrets_to_migrate.items():
        env_value = os.getenv(env_var)
        
        if env_value and env_value not in ['your-db-password', 'your-api-key', 'your-secret-here']:
            try:
                # Check if already exists in KV
                existing = await provider.get_secret(kv_key)
                
                if existing:
                    print(f"⚠️  {kv_key} already exists in KV, skipping")
                    skipped += 1
                else:
                    # Migrate to KV
                    success = await provider.set_secret(kv_key, env_value)
                    
                    if success:
                        print(f"✅ Migrated {env_var} -> {kv_key}")
                        migrated += 1
                    else:
                        print(f"❌ Failed to migrate {env_var}")
                        
            except Exception as e:
                print(f"❌ Error migrating {env_var}: {e}")
        else:
            print(f"⏭️  Skipping {env_var} (not set or placeholder value)")
            skipped += 1
    
    print(f"\n📊 Migration Summary:")
    print(f"   ✅ Migrated: {migrated}")
    print(f"   ⏭️  Skipped: {skipped}")
    
    # List all secrets in KV
    all_secrets = await provider.list_secrets()
    print(f"\n📋 Total secrets in KV: {len(all_secrets)}")
    for secret in all_secrets:
        print(f"   - {secret}")
    
    return migrated > 0

async def verify_migration():
    """Verify that migrated secrets work"""
    print("\n🔍 Verifying Migration...")
    
    provider = SecretConfigProvider()
    
    test_keys = ['database-password', 'jwt-secret-key', 'api-key']
    
    for key in test_keys:
        try:
            value = await provider.get_secret(key)
            if value:
                print(f"✅ {key}: {value[:8]}...")
            else:
                print(f"❌ {key}: Not found")
        except Exception as e:
            print(f"❌ {key}: Error - {e}")

async def main():
    print("🚀 Secret Migration Tool")
    print("This will migrate your environment variables to Cloudflare KV\n")
    
    # Confirm migration
    confirm = input("Do you want to proceed with migration? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Migration cancelled.")
        return
    
    success = await migrate_secrets()
    
    if success:
        await verify_migration()
        
        print("\n🎉 Migration completed successfully!")
        print("\n📝 Next steps:")
        print("   1. Test your FastAPI application")
        print("   2. Update production environment")
        print("   3. Remove secrets from .env file (keep as backup)")
        print("   4. Deploy with confidence!")
        
    else:
        print("\n⚠️  No secrets were migrated")

if __name__ == "__main__":
    asyncio.run(main())
