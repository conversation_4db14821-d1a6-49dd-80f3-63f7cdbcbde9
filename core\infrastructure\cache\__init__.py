"""
Caching Systems Sub-Module

This module provides a unified caching interface with multiple implementations:

Core Features:
- Redis cache with fallback mechanisms and Redis Cloud optimization
- In-memory cache implementations with TTL support
- Hybrid fallback systems for Redis limits
- Queue systems for cache operations
- Automatic failover and recovery

Public API:
- ICache: Interface for all cache implementations
- RedisCache: Redis-based cache with fallback to memory
- MemoryCache: Pure in-memory cache implementation
- CacheEntry: Data structure for cache entries
- FallbackStrategy: Strategies for handling Redis failures
- RedisLimits: Configuration for Redis Cloud limits

Migrated from: core.infrastructure.cache, redis_cache, redis_fallback_manager
"""

from typing import Optional, Any

# Core interfaces
try:
    from ...domain.interfaces import ICache
except ImportError:
    # Fallback for development
    from abc import ABC, abstractmethod

    class ICache(ABC):
        """Cache interface placeholder"""

        @abstractmethod
        async def get(self, key: str) -> Optional[Any]:
            """Get value from cache"""
            pass

        @abstractmethod
        async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
            """Set value in cache"""
            pass

        @abstractmethod
        async def delete(self, key: str) -> None:
            """Delete value from cache"""
            pass

# Import modular implementations first, then fallback to legacy
try:
    from .memory import MemoryCache, CacheEntry
except ImportError:
    # Fallback to parent directory for backward compatibility
    try:
        from ..cache import MemoryCache, CacheEntry
    except ImportError:
        MemoryCache = None
        CacheEntry = None

try:
    from .redis import RedisCache
except ImportError:
    # Fallback to legacy Redis cache
    try:
        from ..cache import RedisCache
    except ImportError:
        RedisCache = None

try:
    from .fallback import FallbackStrategy, RedisLimits
except ImportError:
    # Fallback for development
    FallbackStrategy = None
    RedisLimits = None

def create_cache(
    cache_type: str = "memory",
    redis_url: Optional[str] = None,
    key_prefix: str = "cache:",
    max_size: int = 1000,
    default_ttl: int = 3600,
    **kwargs
) -> Any:  # Return Any to avoid type issues during development
    """Factory function to create cache instances

    Args:
        cache_type: Type of cache ("redis", "memory", "hybrid")
        redis_url: Redis connection URL (required for redis/hybrid)
        key_prefix: Prefix for cache keys
        max_size: Maximum cache size for memory cache
        default_ttl: Default TTL in seconds
        **kwargs: Additional configuration options

    Returns:
        Configured cache instance

    Raises:
        ValueError: If invalid cache type or missing required parameters
        RuntimeError: If required cache implementation is not available
    """
    if cache_type == "memory":
        if MemoryCache is None:
            # Try direct import as fallback
            try:
                from ..cache import MemoryCache as DirectMemoryCache
                return DirectMemoryCache(max_size=max_size, default_ttl=default_ttl)
            except ImportError:
                raise RuntimeError("MemoryCache not available")
        return MemoryCache(max_size=max_size, default_ttl=default_ttl)

    elif cache_type in ("redis", "hybrid"):
        if RedisCache is None:
            raise RuntimeError("RedisCache not available")
        if redis_url is None:
            raise ValueError("RedisCache requires redis_url parameter")

        # Configure Redis limits for Redis Cloud free tier
        if RedisLimits is not None and FallbackStrategy is not None:
            redis_limits = RedisLimits(
                max_connections=kwargs.get('max_connections', 25),
                max_ops_per_second=kwargs.get('max_ops_per_second', 80),
                max_memory_mb=kwargs.get('max_memory_mb', 28)
            )

            return RedisCache(
                redis_url=redis_url,
                key_prefix=key_prefix,
                fallback_strategy=FallbackStrategy.HYBRID,
                memory_cache_size=max_size,
                memory_cache_ttl=default_ttl,
                redis_limits=redis_limits
            )
        else:
            # Fallback to basic Redis cache if advanced features not available
            return RedisCache(
                redis_url=redis_url,
                key_prefix=key_prefix
            )

    else:
        raise ValueError(f"Unknown cache type: {cache_type}")


# Future implementations (placeholders for now)
EnhancedRedisCache = None
EnhancedMemoryCache = None
CacheFallbackManager = None


__all__ = [
    # Core interface
    "ICache",

    # Cache implementations
    "RedisCache",
    "MemoryCache",

    # Data structures
    "CacheEntry",

    # Configuration
    "FallbackStrategy",
    "RedisLimits",

    # Factory function
    "create_cache",

    # Future implementations (if available)
    "EnhancedRedisCache",
    "EnhancedMemoryCache",
    "CacheFallbackManager",
]
