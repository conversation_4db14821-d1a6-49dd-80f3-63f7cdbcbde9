"""
Supabase Authentication Provider

This module provides Supabase-native authentication implementations,
replacing custom JWT auth with Supabase's built-in auth system.

Migrated from: core.infrastructure.supabase_providers
"""

import logging
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timezone

try:
    from core.domain.interfaces import ISecurityProvider
    from core.domain.models import AuthToken
except ImportError:
    # Fallback for development
    class ISecurityProvider:
        pass
    class AuthToken:
        pass
from .client import UnifiedSupabaseClient

logger = logging.getLogger(__name__)


class SupabaseAuthProvider(ISecurityProvider):
    """
    Supabase-native security provider using Supabase Auth

    Replaces JWT-based authentication with Supabase's built-in auth system.
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client

    async def authenticate_user(self, username: str, password: str) -> Optional[AuthToken]:
        """Authenticate user with Supabase Auth"""
        try:
            # Supabase uses email for authentication
            result = await self.supabase.sign_in(username, password)

            if result["success"] and result.get("session"):
                session = result["session"]
                user_data = result["user"]

                return AuthToken(
                    access_token=session.access_token,
                    refresh_token=session.refresh_token,
                    token_type="bearer",
                    expires_in=session.expires_in,
                    user_id=user_data.id,
                    username=user_data.email,
                    roles=[]  # Will be populated from user profile
                )

            return None

        except Exception as e:
            logger.error(f"Authentication failed for {username}: {e}")
            return None

    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token using Supabase Auth"""
        try:
            user_data = await self.supabase.verify_token(token)

            if user_data:
                # Get additional user profile data
                profile = await self.supabase.get_user_profile(user_data["id"])

                return {
                    "user_id": user_data["id"],
                    "username": user_data["email"],
                    "email": user_data["email"],
                    "roles": profile.get("roles", []) if profile else [],
                    "is_superuser": profile.get("is_superuser", False) if profile else False,
                    "metadata": user_data.get("user_metadata", {})
                }

            return None

        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None

    async def refresh_token(self, refresh_token: str) -> Optional[AuthToken]:
        """Refresh access token using refresh token"""
        try:
            # Supabase handles token refresh automatically
            # This is a placeholder for manual refresh if needed
            logger.info("Token refresh requested - Supabase handles this automatically")
            return None

        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return None

    async def revoke_token(self, token: str) -> bool:
        """Revoke/invalidate token"""
        try:
            # Sign out to revoke token
            await self.supabase.sign_out()
            return True

        except Exception as e:
            logger.error(f"Token revocation failed: {e}")
            return False

    async def create_user_session(self, user_id: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Create user session (handled by Supabase Auth)"""
        try:
            # Supabase manages sessions automatically
            logger.info(f"Session creation requested for user {user_id}")
            return user_id  # Return user_id as session identifier

        except Exception as e:
            logger.error(f"Session creation failed: {e}")
            return None

    async def validate_session(self, session_id: str) -> bool:
        """Validate user session"""
        try:
            # Check if user is authenticated
            user = self.supabase._client.auth.get_user()
            return user.user is not None

        except Exception as e:
            logger.error(f"Session validation failed: {e}")
            return False

    async def get_user_permissions(self, user_id: str) -> List[str]:
        """Get user permissions from profile"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                # If superuser, return all permissions
                if profile.get("is_superuser", False):
                    return ["*"]

                # Get permissions from roles (would need role-permission mapping)
                # This is a simplified implementation
                roles = profile.get("metadata", {}).get("role_ids", [])
                permissions = []

                # Map roles to permissions (this would be in a separate table)
                role_permission_map = {
                    "admin": ["users:read", "users:write", "users:delete"],
                    "user": ["users:read"],
                    "moderator": ["users:read", "users:write"]
                }

                for role in roles:
                    permissions.extend(role_permission_map.get(role, []))

                return list(set(permissions))  # Remove duplicates

            return []

        except Exception as e:
            logger.error(f"Failed to get permissions for user {user_id}: {e}")
            return []

    async def check_permission(self, user_id: str, permission: str) -> bool:
        """Check if user has specific permission"""
        try:
            permissions = await self.get_user_permissions(user_id)

            # Check for wildcard permission
            if "*" in permissions:
                return True

            # Check for exact permission match
            return permission in permissions

        except Exception as e:
            logger.error(f"Permission check failed for user {user_id}: {e}")
            return False

    async def get_user_roles(self, user_id: str) -> List[str]:
        """Get user roles from profile"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                return profile.get("metadata", {}).get("role_ids", [])

            return []

        except Exception as e:
            logger.error(f"Failed to get roles for user {user_id}: {e}")
            return []

    async def assign_role(self, user_id: str, role: str) -> bool:
        """Assign role to user"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                current_roles = set(profile.get("metadata", {}).get("role_ids", []))
                current_roles.add(role)

                # Update user profile with new roles
                update_data = {
                    "metadata": {
                        **profile.get("metadata", {}),
                        "role_ids": list(current_roles)
                    },
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }

                response = self.supabase.table("user_profiles", use_service_key=True).update(
                    update_data
                ).eq("auth_user_id", user_id).execute()

                return bool(response.data)

            return False

        except Exception as e:
            logger.error(f"Failed to assign role {role} to user {user_id}: {e}")
            return False

    async def remove_role(self, user_id: str, role: str) -> bool:
        """Remove role from user"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                current_roles = set(profile.get("metadata", {}).get("role_ids", []))
                current_roles.discard(role)

                # Update user profile with updated roles
                update_data = {
                    "metadata": {
                        **profile.get("metadata", {}),
                        "role_ids": list(current_roles)
                    },
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }

                response = self.supabase.table("user_profiles", use_service_key=True).update(
                    update_data
                ).eq("auth_user_id", user_id).execute()

                return bool(response.data)

            return False

        except Exception as e:
            logger.error(f"Failed to remove role {role} from user {user_id}: {e}")
            return False


# Alias for backward compatibility
SupabaseSecurityProvider = SupabaseAuthProvider
