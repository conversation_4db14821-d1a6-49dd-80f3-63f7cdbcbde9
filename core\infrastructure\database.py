"""
Database Utilities and Initialization

This module provides utilities for database initialization, migration,
and seeding with initial data including admin users and default roles.
"""

import asyncio
import uuid
from datetime import datetime, timezone
from typing import Optional

from core.infrastructure.user_store import DatabaseUserStore, DatabaseRoleStore
from core.infrastructure.security import DatabaseSecurityProvider
from core.domain.models import UserStatus


class DatabaseInitializer:
    """Database initialization and seeding utility"""

    def __init__(self, database_url: str):
        self.database_url = database_url
        self.user_store = DatabaseUserStore(database_url)
        self.role_store = DatabaseRoleStore(database_url)

    async def initialize_database(self, drop_existing: bool = False) -> None:
        """Initialize database tables"""
        if drop_existing:
            print("🗑️  Dropping existing tables...")
            await self.user_store.drop_tables()

        print("🏗️  Creating database tables...")
        await self.user_store.create_tables()
        print("✅ Database tables created successfully")

    async def seed_default_data(self, admin_password: str = "admin123") -> dict:
        """Seed database with default roles and admin user"""
        print("🌱 Seeding default data...")

        # Create security provider for password hashing
        security = DatabaseSecurityProvider(
            user_store=self.user_store,
            role_store=self.role_store,
            secret_key="temp-key-for-seeding"
        )

        # Create default permissions
        permissions_data = [
            {"name": "users_read", "resource": "users", "action": "read", "description": "Read user data"},
            {"name": "users_write", "resource": "users", "action": "write", "description": "Create and update users"},
            {"name": "users_delete", "resource": "users", "action": "delete", "description": "Delete users"},
            {"name": "roles_read", "resource": "roles", "action": "read", "description": "Read role data"},
            {"name": "roles_write", "resource": "roles", "action": "write", "description": "Create and update roles"},
            {"name": "roles_delete", "resource": "roles", "action": "delete", "description": "Delete roles"},
            {"name": "services_read", "resource": "services", "action": "read", "description": "Read service data"},
            {"name": "services_write", "resource": "services", "action": "write", "description": "Register and update services"},
            {"name": "services_delete", "resource": "services", "action": "delete", "description": "Unregister services"},
            {"name": "admin_all", "resource": "*", "action": "*", "description": "Full administrative access"},
        ]

        # Create default roles
        admin_role = await self.role_store.create_role({
            "name": "admin",
            "description": "System administrator with full access",
            "is_system_role": True
        })

        user_role = await self.role_store.create_role({
            "name": "user",
            "description": "Standard user with basic access",
            "is_system_role": True
        })

        service_role = await self.role_store.create_role({
            "name": "service",
            "description": "Service account with API access",
            "is_system_role": True
        })

        # Add permissions to admin role
        await self.role_store.add_permission_to_role(admin_role.id, "*:*")

        # Add basic permissions to user role
        await self.role_store.add_permission_to_role(user_role.id, "users:read")
        await self.role_store.add_permission_to_role(user_role.id, "services:read")

        # Add service permissions to service role
        await self.role_store.add_permission_to_role(service_role.id, "services:read")
        await self.role_store.add_permission_to_role(service_role.id, "services:write")

        # Create admin user
        admin_user = await security.create_user({
            "username": "admin",
            "email": "<EMAIL>",
            "password": admin_password,
            "first_name": "System",
            "last_name": "Administrator",
            "is_superuser": True,
            "is_verified": True,
            "status": UserStatus.ACTIVE.value,
            "role_ids": {admin_role.id}
        })

        # Create a regular user for testing
        test_user = await security.create_user({
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123",
            "first_name": "Test",
            "last_name": "User",
            "is_superuser": False,
            "is_verified": True,
            "status": UserStatus.ACTIVE.value,
            "role_ids": {user_role.id}
        })

        print(f"✅ Created admin user: {admin_user.username} (password: {admin_password})")
        print(f"✅ Created test user: {test_user.username} (password: testpass123)")
        print(f"✅ Created {len(permissions_data)} default permissions")
        print(f"✅ Created 3 default roles: admin, user, service")

        return {
            "admin_user": admin_user,
            "test_user": test_user,
            "admin_role": admin_role,
            "user_role": user_role,
            "service_role": service_role
        }

    async def create_admin_user(self, username: str, email: str, password: str,
                               first_name: Optional[str] = None, last_name: Optional[str] = None) -> dict:
        """Create a new admin user"""
        # Create security provider for password hashing
        security = DatabaseSecurityProvider(
            user_store=self.user_store,
            role_store=self.role_store,
            secret_key="temp-key-for-seeding"
        )

        # Get admin role
        admin_role = await self.role_store.get_role_by_name("admin")
        if not admin_role:
            raise ValueError("Admin role not found. Please run seed_default_data first.")

        # Create admin user
        admin_user = await security.create_user({
            "username": username,
            "email": email,
            "password": password,
            "first_name": first_name,
            "last_name": last_name,
            "is_superuser": True,
            "is_verified": True,
            "status": UserStatus.ACTIVE.value,
            "role_ids": {admin_role.id}
        })

        print(f"✅ Created admin user: {admin_user.username}")
        return {"admin_user": admin_user}

    async def verify_setup(self) -> dict:
        """Verify database setup and return status"""
        try:
            # Check if tables exist by trying to list users
            users = await self.user_store.list_users(limit=1)
            roles = await self.role_store.list_roles(limit=1)

            # Count total users and roles
            all_users = await self.user_store.list_users(limit=1000)
            all_roles = await self.role_store.list_roles(limit=1000)

            # Check for admin user
            admin_user = await self.user_store.get_user_by_username("admin")

            return {
                "database_initialized": True,
                "total_users": len(all_users),
                "total_roles": len(all_roles),
                "admin_user_exists": admin_user is not None,
                "admin_user_id": admin_user.id if admin_user else None
            }
        except Exception as e:
            return {
                "database_initialized": False,
                "error": str(e)
            }


async def init_database_cli(database_url: Optional[str] = None, admin_password: str = "admin123",
                           drop_existing: bool = False, use_supabase: bool = True) -> None:
    """CLI function to initialize database with Supabase integration"""
    print("🚀 Starting database initialization...")

    # Auto-detect database URL if not provided
    if not database_url:
        from core.config.settings import get_settings
        settings = get_settings()
        database_url = settings.get_database_url()

        if settings.is_supabase_enabled():
            print(f"✅ Using Supabase database: {settings.supabase.supabase_url}")
            print(f"   Dashboard: {settings.get_supabase_dashboard_url()}")
        else:
            print(f"📊 Using database: {database_url}")

    initializer = DatabaseInitializer(database_url)

    # Initialize database
    await initializer.initialize_database(drop_existing=drop_existing)

    # Seed default data
    await initializer.seed_default_data(admin_password=admin_password)

    # Verify setup
    status = await initializer.verify_setup()
    print("\n📊 Database Status:")
    print(f"   Users: {status['total_users']}")
    print(f"   Roles: {status['total_roles']}")
    print(f"   Admin user: {'✅' if status['admin_user_exists'] else '❌'}")

    print("\n🎉 Database initialization complete!")
    print(f"   Admin login: admin / {admin_password}")
    print(f"   Test login: testuser / testpass123")

    # Show Supabase-specific info if applicable
    if database_url and "supabase.co" in database_url:
        from core.config.settings import get_settings
        settings = get_settings()
        dashboard_url = settings.get_supabase_dashboard_url()
        if dashboard_url:
            print(f"\n🎯 Supabase Dashboard: {dashboard_url}")
            print("   You can view and manage your data in the Supabase dashboard!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("Usage: python database.py <database_url> [admin_password] [--drop]")
        print("Example: python database.py postgresql://user:pass@localhost/db admin123 --drop")
        sys.exit(1)

    database_url = sys.argv[1]
    admin_password = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith('--') else "admin123"
    drop_existing = "--drop" in sys.argv

    asyncio.run(init_database_cli(database_url, admin_password, drop_existing))
