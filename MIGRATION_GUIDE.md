# Infrastructure Migration Guide

## Quick Start

The infrastructure has been reorganized into a modular structure. **Your existing code will continue to work** due to backward compatibility, but you can migrate to the new structure for better organization.

## Import Migration

### Before (Legacy - Still Works)
```python
from core.infrastructure.unified_supabase import UnifiedSupabaseClient
from core.infrastructure.supabase_realtime import SupabaseRealtimeService
from core.infrastructure.supabase_providers import SupabaseSecurityProvider
from core.infrastructure.cache import RedisCache, MemoryCache
```

### After (Preferred)
```python
from core.infrastructure.supabase import (
    UnifiedSupabaseClient,
    SupabaseRealtimeService,
    SupabaseAuthProvider,  # Renamed from SupabaseSecurityProvider
)
from core.infrastructure.cache import RedisCache, MemoryCache
```

## Key Changes

### 1. Supabase Services Consolidated
- **Old**: Multiple separate files (`unified_supabase.py`, `supabase_realtime.py`, etc.)
- **New**: Single `supabase/` module with organized sub-services

### 2. Cleaner Import Paths
- **Old**: `from core.infrastructure.supabase_realtime import ...`
- **New**: `from core.infrastructure.supabase.realtime import ...`

### 3. Better Organization
- **Auth**: `core.infrastructure.supabase.auth`
- **Database**: `core.infrastructure.supabase.database`
- **Storage**: `core.infrastructure.supabase.storage`
- **Realtime**: `core.infrastructure.supabase.realtime`

## Migration Steps

### Step 1: Update Imports (Optional)
Replace old imports with new ones in your code:

```python
# Old
from core.infrastructure.unified_supabase import UnifiedSupabaseClient

# New
from core.infrastructure.supabase import UnifiedSupabaseClient
```

### Step 2: Use New Service Structure (Recommended)
```python
# Initialize Supabase client
from core.infrastructure.supabase import UnifiedSupabaseClient

client = UnifiedSupabaseClient(settings)
await client.initialize()

# Use specific services
from core.infrastructure.supabase.realtime import SupabaseRealtimeService
from core.infrastructure.supabase.storage import SupabaseStorage

realtime = SupabaseRealtimeService(client)
storage = SupabaseStorage(client)
```

### Step 3: Update Tests (If Any)
Update test imports to use the new structure:

```python
# Old
from core.infrastructure.supabase_realtime import RealtimeEventType

# New  
from core.infrastructure.supabase.realtime import RealtimeEventType
```

## What's Still the Same

1. **All APIs**: No breaking changes to existing APIs
2. **Functionality**: All features work exactly the same
3. **Configuration**: Same settings and initialization
4. **Backward Compatibility**: Old imports still work

## What's Better

1. **Organization**: Clear separation of concerns
2. **Discoverability**: Easier to find specific functionality
3. **Maintainability**: Better code organization
4. **Type Safety**: Improved type hints
5. **Testing**: Better test organization

## Troubleshooting

### Import Errors
If you get import errors, check:

1. **Use legacy imports**: Old imports still work
2. **Check spelling**: New module names might be slightly different
3. **Update gradually**: You don't need to migrate everything at once

### Example Fix
```python
# If this fails:
from core.infrastructure.supabase.auth import SupabaseAuthProvider

# Use this instead:
from core.infrastructure import SupabaseAuthProvider
```

## Testing Your Migration

Run the reorganization tests to verify everything works:

```bash
python core/infrastructure/tests/test_reorganization.py
```

Expected output:
```
✅ test_main_infrastructure_imports
✅ test_supabase_module_imports
✅ test_supabase_client_instantiation
✅ test_realtime_service_imports
✅ test_realtime_service_instantiation
✅ test_directory_structure_exists
✅ test_supabase_files_exist
✅ test_legacy_file_migration_status
✅ test_backward_compatibility_imports
✅ test_service_status_methods

Test Results: 10 passed, 0 failed
```

## Need Help?

1. **Backward Compatibility**: Your existing code should work without changes
2. **Gradual Migration**: Migrate one file at a time
3. **Test First**: Run tests to ensure everything works
4. **Documentation**: Check `INFRASTRUCTURE_REORGANIZATION.md` for details

## Summary

- ✅ **No Breaking Changes**: Existing code continues to work
- ✅ **Better Organization**: New modular structure available
- ✅ **Gradual Migration**: Update at your own pace
- ✅ **Improved Maintainability**: Cleaner code organization
- ✅ **Full Test Coverage**: Comprehensive tests verify functionality
