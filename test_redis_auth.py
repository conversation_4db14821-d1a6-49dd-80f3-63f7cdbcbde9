#!/usr/bin/env python3
"""
Redis Authentication Test

This script helps test Redis connection with authentication.
"""

import asyncio
import os
import sys
import getpass
from datetime import datetime, timezone

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_redis_with_auth():
    """Test Redis connection with authentication"""
    
    print("🔐 Redis Authentication Test")
    print("=" * 50)
    
    # Get Redis connection details
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    print(f"Redis Host: {hostname}")
    print(f"Redis Port: {port}")
    print()
    
    # Check for password in environment first
    password = os.getenv("REDIS_PASSWORD")
    if not password:
        print("No REDIS_PASSWORD found in environment.")
        password = getpass.getpass("Please enter your Redis password: ")
    
    if not password:
        print("❌ No password provided. Cannot test authentication.")
        return False
    
    # Test different authentication methods
    auth_methods = [
        {
            "name": "Password only",
            "url": f"redis://:{password}@{hostname}:{port}"
        },
        {
            "name": "Default user with password",
            "url": f"redis://default:{password}@{hostname}:{port}"
        },
        {
            "name": "Password with database 0",
            "url": f"redis://:{password}@{hostname}:{port}/0"
        },
        {
            "name": "Default user with password and database 0",
            "url": f"redis://default:{password}@{hostname}:{port}/0"
        }
    ]
    
    successful_url = None
    
    for method in auth_methods:
        print(f"\n🧪 Testing: {method['name']}")
        print(f"   URL: redis://***:***@{hostname}:{port}...")
        
        try:
            import redis.asyncio as aioredis
            
            redis_client = await aioredis.from_url(
                method['url'],
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=10,
                socket_connect_timeout=10
            )
            
            # Test ping
            pong = await redis_client.ping()
            print(f"   ✅ Authentication successful: {pong}")
            
            # Test basic operations
            test_key = f"auth_test_{int(datetime.now().timestamp())}"
            await redis_client.set(test_key, "auth_working", ex=30)
            value = await redis_client.get(test_key)
            
            if value == "auth_working":
                print(f"   ✅ Basic operations working")
                successful_url = method['url']
                
                # Get server info
                info = await redis_client.info("server")
                redis_version = info.get("redis_version", "unknown")
                print(f"   ✅ Redis version: {redis_version}")
                
                # Cleanup
                await redis_client.delete(test_key)
                await redis_client.close()
                break
            else:
                print(f"   ❌ Basic operations failed")
                await redis_client.close()
                
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    if successful_url:
        print(f"\n🎉 Authentication successful!")
        print(f"Working URL format: redis://***:***@{hostname}:{port}...")
        
        # Update .env file
        print(f"\n📝 Updating .env file...")
        try:
            # Read existing .env file
            env_content = ""
            if os.path.exists(".env"):
                with open(".env", "r") as f:
                    env_content = f.read()
            
            # Update or add REDIS_URL and REDIS_PASSWORD
            lines = env_content.split('\n')
            redis_url_found = False
            redis_password_found = False
            
            for i, line in enumerate(lines):
                if line.startswith('REDIS_URL='):
                    lines[i] = f"REDIS_URL={successful_url}"
                    redis_url_found = True
                elif line.startswith('REDIS_PASSWORD='):
                    lines[i] = f"REDIS_PASSWORD={password}"
                    redis_password_found = True
            
            if not redis_url_found:
                lines.append(f"REDIS_URL={successful_url}")
            
            if not redis_password_found:
                lines.append(f"REDIS_PASSWORD={password}")
            
            # Write back to .env
            with open(".env", "w") as f:
                f.write('\n'.join(lines))
            
            print("   ✅ .env file updated with working Redis configuration")
            
        except Exception as e:
            print(f"   ❌ Failed to update .env file: {e}")
        
        return True
    else:
        print(f"\n❌ All authentication methods failed.")
        print(f"Please check:")
        print(f"   • Your Redis password is correct")
        print(f"   • Your IP address is whitelisted in Redis Cloud")
        print(f"   • The Redis instance is active and accessible")
        return False


async def test_redis_info():
    """Test Redis connection and get detailed info"""
    print(f"\n📊 Redis Instance Information")
    print("=" * 50)
    
    # Try to connect with environment variables
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        print("❌ No REDIS_URL found in environment")
        return False
    
    try:
        import redis.asyncio as aioredis
        
        redis_client = await aioredis.from_url(redis_url)
        
        # Get comprehensive info
        info = await redis_client.info()
        
        print(f"Redis Version: {info.get('redis_version', 'unknown')}")
        print(f"Redis Mode: {info.get('redis_mode', 'unknown')}")
        print(f"OS: {info.get('os', 'unknown')}")
        print(f"Architecture: {info.get('arch_bits', 'unknown')} bit")
        print(f"Memory Used: {info.get('used_memory_human', 'unknown')}")
        print(f"Memory Peak: {info.get('used_memory_peak_human', 'unknown')}")
        print(f"Connected Clients: {info.get('connected_clients', 'unknown')}")
        print(f"Total Commands Processed: {info.get('total_commands_processed', 'unknown')}")
        print(f"Keyspace Hits: {info.get('keyspace_hits', 'unknown')}")
        print(f"Keyspace Misses: {info.get('keyspace_misses', 'unknown')}")
        
        # Test database info
        print(f"\nDatabase Information:")
        for key, value in info.items():
            if key.startswith('db'):
                print(f"   {key}: {value}")
        
        await redis_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to get Redis info: {e}")
        return False


async def main():
    """Main function"""
    print("🚀 Redis Authentication and Setup Tool")
    print("=" * 60)
    print(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
    print()
    
    # Test authentication
    auth_success = await test_redis_with_auth()
    
    if auth_success:
        # Test detailed info
        await test_redis_info()
        
        print(f"\n✅ Redis setup complete!")
        print(f"You can now run the main Redis tests:")
        print(f"   python test_redis_setup.py")
    else:
        print(f"\n❌ Redis authentication failed.")
        print(f"Please check your Redis Cloud dashboard for:")
        print(f"   • The correct password")
        print(f"   • IP whitelist settings")
        print(f"   • Instance status")
    
    return auth_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
