# Secret Management System

The FastAPI Core Framework includes a comprehensive secret management system that supports multiple providers with intelligent auto-detection and seamless upgrades. This system is designed to be production-ready while providing a smooth development experience.

## Overview

The secret management system provides:

- **🚀 Cloudflare Workers KV**: Primary provider with global edge distribution (available now)
- **🔮 Cloudflare Secrets Store**: Future upgrade with enhanced features (auto-detection)
- **🏛️ Multiple Provider Support**: <PERSON><PERSON>C<PERSON><PERSON> Vault, AWS Secrets Manager, Azure Key Vault
- **🔄 Auto-Detection**: Automatically selects the best available provider based on environment
- **🛡️ Fallback Mechanisms**: Environment variables as fallback when providers are unavailable
- **⚡ Caching**: Configurable TTL-based caching for performance
- **📊 Versioning**: Secret versioning support where available
- **🏷️ Metadata**: Rich metadata including tags, expiration, and creation timestamps
- **🛠️ Development Support**: Encrypted file-based secrets for local development

## Quick Start with Cloudflare

### 🚀 **Cloudflare Workers KV** (Recommended - Available Now)

1. **Get your Cloudflare credentials:**
   - Account ID: Found in your Cloudflare dashboard
   - API Token: Create one with "Workers KV Storage:Edit" permissions

2. **Set environment variables:**
   ```bash
   export CLOUDFLARE_ACCOUNT_ID=your-account-id
   export CLOUDFLARE_API_TOKEN=your-api-token
   export CLOUDFLARE_KV_NAMESPACE=secrets  # Optional
   ```

3. **Test the connection:**
   ```bash
   python test_cloudflare_kv.py
   # Or use the setup script
   python setup_secret_management.py
   ```

4. **Start using secrets:**
   ```python
   from core.config.secret_providers import SecretConfigProvider

   provider = SecretConfigProvider()  # Auto-detects Cloudflare KV
   await provider.set_secret("database-password", "your-secure-password")
   password = await provider.get_secret("database-password")  # Returns string
   ```

### 🔮 **Future: Cloudflare Secrets Store** (Beta)

The system will **automatically upgrade** to Cloudflare Secrets Store when it becomes available in your account, providing enhanced security features and richer metadata support.

## Supported Providers

### 1. Cloudflare Workers KV (Primary - Available Now)

**Features:**

- **🌍 Global edge distribution**: 300+ locations worldwide
- **⚡ High performance**: Sub-millisecond access times
- **🔒 Secure**: Encrypted at rest and in transit
- **💰 Cost effective**: No additional charges for KV usage
- **📈 Scalable**: Handles millions of requests per second
- **🔄 Auto-upgrade**: Seamlessly switches to Secrets Store when available

**Configuration:**

```bash
# Environment variables
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_KV_NAMESPACE=secrets  # Optional, defaults to "secrets"
```

**Dependencies:**

```bash
pip install httpx  # Already included in core requirements
```

### 2. Cloudflare Secrets Store (Future - Beta)

**Features:**

- **🔄 Automatic detection**: Seamlessly upgrades from KV when available
- **🔐 Enhanced security**: Additional enterprise security features
- **📊 Rich metadata**: Advanced versioning and audit trails
- **🛡️ RBAC support**: Fine-grained access control and permissions
- **📝 Audit logging**: Complete audit trail of secret access

**Configuration:**

```bash
# Same as KV - automatic detection
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_STORE_ID=your-store-id  # Optional
```

### 2. HashiCorp Vault

**Features:**
- KV v1 and v2 engine support
- Token and AppRole authentication
- Secret versioning (KV v2)
- Metadata and tags support

**Configuration:**
```bash
# Environment variables
VAULT_ADDR=https://vault.example.com
VAULT_TOKEN=your-vault-token

# OR for AppRole authentication
VAULT_ROLE_ID=your-role-id
VAULT_SECRET_ID=your-secret-id

# Optional settings
VAULT_MOUNT_POINT=secret
VAULT_KV_VERSION=2
```

**Dependencies:**
```bash
pip install hvac
```

### 2. AWS Secrets Manager

**Features:**
- IAM-based authentication
- Automatic secret rotation
- Cross-region support
- Rich metadata and tagging

**Configuration:**
```bash
# Environment variables
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# OR use IAM roles, profiles, etc.
```

**Dependencies:**
```bash
pip install boto3
```

### 3. Azure Key Vault

**Features:**
- Managed identity support
- Certificate and key management
- Access policies and RBAC
- Secret versioning

**Configuration:**
```bash
# Environment variables
AZURE_KEY_VAULT_URL=https://myvault.vault.azure.net/
```

**Dependencies:**
```bash
pip install azure-keyvault-secrets azure-identity
```

### 4. Encrypted File Provider (Development)

**Features:**
- Local development support
- Fernet encryption
- Secret versioning
- Automatic key generation

**Configuration:**
```bash
# Environment variables
SECRETS_FILE_PATH=secrets.enc
SECRETS_ENCRYPTION_KEY=base64-encoded-key  # Optional, auto-generated
SUPPRESS_DEV_SECRET_WARNING=1  # Optional
```

**Dependencies:**
```bash
pip install cryptography
```

## Usage

### Basic Usage

```python
from core.config.secret_providers import SecretConfigProvider

# Auto-detect provider
provider = SecretConfigProvider(provider_type="auto")

# Get a secret
secret_value = await provider.get_secret("database-password")

# Set a secret
await provider.set_secret("api-key", "secret-value", tags={"env": "prod"})

# List secrets
secrets = await provider.list_secrets()

# Delete a secret
await provider.delete_secret("old-secret")
```

### Specific Provider

```python
# Use specific provider
provider = SecretConfigProvider(
    provider_type="vault",
    vault_url="https://vault.example.com",
    vault_token="your-token"
)
```

### Integration with Settings

```python
from core.config import Settings

settings = Settings()

# Configure secret management
settings.secrets.secret_provider = "vault"
settings.secrets.vault_url = "https://vault.example.com"
settings.secrets.secret_cache_ttl = 600
```

## Auto-Detection Logic

The system automatically detects the best available provider in this order:

1. **🚀 Cloudflare Workers KV** - if `CLOUDFLARE_ACCOUNT_ID` and `CLOUDFLARE_API_TOKEN` are set (primary)
2. **🔮 Cloudflare Secrets Store** - fallback when KV fails, future primary when available
3. **🏛️ HashiCorp Vault** - if `VAULT_ADDR` and authentication are configured
4. **☁️ AWS Secrets Manager** - if `AWS_REGION` and credentials are available
5. **🔑 Azure Key Vault** - if `AZURE_KEY_VAULT_URL` is configured
6. **📁 Encrypted File** - fallback for development

### Intelligent Upgrade Path

The system provides a **seamless upgrade path**:

- **Today**: Uses Cloudflare Workers KV (production-ready, globally distributed)
- **Future**: Automatically detects and upgrades to Cloudflare Secrets Store when available
- **Always**: Falls back to environment variables for maximum compatibility

## Security Best Practices

### Production Deployment

1. **Use External Providers**: Never use file-based secrets in production
2. **Rotate Secrets Regularly**: Implement secret rotation policies
3. **Limit Access**: Use RBAC and least-privilege principles
4. **Monitor Access**: Enable audit logging for secret access
5. **Encrypt in Transit**: Ensure all communication is over HTTPS/TLS

### Development

1. **Use File Provider**: Encrypted file provider for local development
2. **Separate Environments**: Different secrets for dev/staging/prod
3. **Version Control**: Never commit secrets to version control
4. **Environment Variables**: Use `.env` files for local configuration

## Configuration Examples

### Production with Vault

```yaml
# config.yaml
secrets:
  secret_provider: vault
  vault_url: https://vault.company.com
  vault_mount_point: myapp
  vault_kv_version: 2
  secret_cache_ttl: 300
  fallback_to_env: false
```

### Development with File Provider

```yaml
# config.dev.yaml
secrets:
  secret_provider: file
  secrets_file_path: dev-secrets.enc
  secret_cache_ttl: 60
  fallback_to_env: true
```

## Setup and Testing

### Setup Script

Run the interactive setup script to configure and test providers:

```bash
python setup_secret_management.py
```

This script will:
- Guide you through provider configuration
- Test connectivity and authentication
- Perform basic secret operations
- Generate environment variable examples

### Manual Testing

```python
# Test provider health
healthy = await provider.health_check()
print(f"Provider healthy: {healthy}")

# Get provider information
info = provider.get_provider_info()
print(f"Provider: {info['provider_class']}")
print(f"Healthy: {info['healthy']}")
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify credentials and permissions
   - Check network connectivity
   - Validate provider URLs

2. **Missing Dependencies**
   - Install required packages for your provider
   - Check import errors in logs

3. **Permission Denied**
   - Verify RBAC policies
   - Check secret access permissions
   - Validate authentication tokens

4. **Network Issues**
   - Check firewall rules
   - Verify DNS resolution
   - Test connectivity to provider endpoints

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('SecretConfigProvider').setLevel(logging.DEBUG)
logging.getLogger('VaultSecretProvider').setLevel(logging.DEBUG)
```

## Migration Guide

### From Environment Variables

1. Identify secrets currently in environment variables
2. Create secrets in your chosen provider
3. Update application configuration
4. Test secret retrieval
5. Remove environment variables

### Between Providers

1. Export secrets from current provider
2. Configure new provider
3. Import secrets to new provider
4. Update configuration
5. Test and validate
6. Decommission old provider

## Performance Considerations

- **Caching**: Adjust cache TTL based on your needs
- **Connection Pooling**: Providers use connection pooling where available
- **Batch Operations**: Use list operations for bulk secret management
- **Regional Deployment**: Choose provider regions close to your application

## Monitoring and Alerting

- Monitor secret access patterns
- Set up alerts for authentication failures
- Track secret rotation schedules
- Monitor provider health and availability
