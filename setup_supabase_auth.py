#!/usr/bin/env python3
"""
Setup script for Supabase authentication system

This script helps you configure and test the database authentication system
with your Supabase project.
"""

import asyncio
import os
import getpass
from core.infrastructure.database import DatabaseInitializer


def get_supabase_credentials():
    """Get Supabase credentials from user input"""
    print("🔐 Supabase Database Setup")
    print("=" * 40)
    print("Please provide your Supabase database credentials.")
    print("You can find these in your Supabase project settings > Database")
    print()
    
    # Get database password
    password = os.getenv("SUPABASE_DB_PASSWORD")
    if not password:
        password = getpass.getpass("Enter your Supabase database password: ")
    
    # Supabase connection details for jinda_789-database-lanswap project
    supabase_host = "db.copabcxpvtiltgxuuemn.supabase.co"
    supabase_db = "postgres"
    supabase_user = "postgres"
    
    database_url = f"postgresql+asyncpg://{supabase_user}:{password}@{supabase_host}:5432/{supabase_db}"
    
    return database_url


async def setup_authentication_system():
    """Setup the complete authentication system"""
    try:
        # Get database credentials
        database_url = get_supabase_credentials()
        
        print("\n🚀 Setting up authentication system...")
        print("=" * 50)
        
        # Initialize database
        print("\n1. Connecting to Supabase...")
        initializer = DatabaseInitializer(database_url)
        
        # Test connection first
        try:
            status = await initializer.verify_setup()
            print("   ✅ Database connection successful")
        except Exception as e:
            print(f"   ❌ Database connection failed: {e}")
            print("   Please check your credentials and try again.")
            return
        
        # Ask if user wants to reset database
        print("\n2. Database initialization...")
        reset_db = input("   Do you want to reset the database (drop existing tables)? [y/N]: ").lower().strip()
        drop_existing = reset_db in ['y', 'yes']
        
        if drop_existing:
            print("   ⚠️  This will delete all existing user data!")
            confirm = input("   Are you sure? Type 'yes' to confirm: ").lower().strip()
            if confirm != 'yes':
                print("   Cancelled.")
                return
        
        # Initialize database tables
        await initializer.initialize_database(drop_existing=drop_existing)
        
        # Get admin password
        print("\n3. Admin user setup...")
        admin_password = getpass.getpass("   Enter password for admin user (default: admin123): ") or "admin123"
        
        # Seed default data
        print("   Creating default roles and users...")
        seed_result = await initializer.seed_default_data(admin_password=admin_password)
        
        # Verify setup
        print("\n4. Verifying setup...")
        status = await initializer.verify_setup()
        
        print("\n✅ Authentication system setup complete!")
        print("=" * 50)
        print(f"📊 Database Status:")
        print(f"   • Total users: {status['total_users']}")
        print(f"   • Total roles: {status['total_roles']}")
        print(f"   • Admin user: {'✅' if status['admin_user_exists'] else '❌'}")
        
        print(f"\n🔑 Login Credentials:")
        print(f"   • Admin: admin / {admin_password}")
        print(f"   • Test User: testuser / testpass123")
        
        print(f"\n🌐 Database Connection:")
        print(f"   • Host: db.copabcxpvtiltgxuuemn.supabase.co")
        print(f"   • Database: postgres")
        print(f"   • Tables: users, roles, permissions, user_roles, role_permissions")
        
        print(f"\n📝 Next Steps:")
        print(f"   1. Set SUPABASE_DB_PASSWORD in your environment")
        print(f"   2. Update your application configuration to use DatabaseSecurityProvider")
        print(f"   3. Test authentication with the provided credentials")
        
        # Ask if user wants to run the test
        print(f"\n🧪 Would you like to run the authentication test now?")
        run_test = input("   Run test? [Y/n]: ").lower().strip()
        if run_test not in ['n', 'no']:
            print("\n" + "=" * 50)
            print("Running authentication test...")
            
            # Set environment variable for test
            os.environ["SUPABASE_DB_PASSWORD"] = database_url.split(':')[2].split('@')[0]
            
            # Import and run test
            from test_database_auth import test_database_authentication
            await test_database_authentication()
        
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("Please check your credentials and try again.")


async def quick_test():
    """Quick test of existing setup"""
    try:
        database_url = get_supabase_credentials()
        initializer = DatabaseInitializer(database_url)
        
        print("\n🔍 Testing existing setup...")
        status = await initializer.verify_setup()
        
        if status['database_initialized']:
            print("✅ Database is properly initialized")
            print(f"   • Users: {status['total_users']}")
            print(f"   • Roles: {status['total_roles']}")
            print(f"   • Admin user: {'✅' if status['admin_user_exists'] else '❌'}")
            
            if status['admin_user_exists']:
                print("\n🎉 Authentication system is ready!")
                print("   You can now use DatabaseSecurityProvider in your application")
            else:
                print("\n⚠️  No admin user found. Run full setup to create one.")
        else:
            print("❌ Database not initialized")
            print("   Run full setup to initialize the authentication system")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")


def main():
    """Main function"""
    print("🔐 Supabase Authentication Setup")
    print("=" * 40)
    print("Choose an option:")
    print("1. Full setup (initialize database and create users)")
    print("2. Quick test (check existing setup)")
    print("3. Exit")
    
    choice = input("\nEnter your choice [1-3]: ").strip()
    
    if choice == "1":
        asyncio.run(setup_authentication_system())
    elif choice == "2":
        asyncio.run(quick_test())
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice. Please run the script again.")


if __name__ == "__main__":
    main()
