"""
Advanced Dependency Injection Container
Implements singleton, factory, and transient lifetimes with async support
"""

import asyncio
import inspect
from abc import ABC, abstractmethod
from enum import Enum
from typing import (
    Any, Callable, Dict, Generic, Optional, TypeVar, Type, 
    Union, get_type_hints, Protocol
)
from contextlib import asynccontextmanager
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')


class Lifetime(Enum):
    """Service lifetime management"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class IServiceProvider(Protocol):
    """Service provider interface"""
    
    def get_service(self, service_type: Type[T]) -> T:
        """Get service by type"""
        ...
    
    async def get_service_async(self, service_type: Type[T]) -> T:
        """Get service by type asynchronously"""
        ...


class ServiceDescriptor:
    """Describes how to create a service"""
    
    def __init__(
        self,
        service_type: Type,
        implementation_type: Optional[Type] = None,
        factory: Optional[Callable] = None,
        instance: Optional[Any] = None,
        lifetime: Lifetime = Lifetime.TRANSIENT,
        dependencies: Optional[Dict[str, Type]] = None
    ):
        self.service_type = service_type
        self.implementation_type = implementation_type or service_type
        self.factory = factory
        self.instance = instance
        self.lifetime = lifetime
        self.dependencies = dependencies or {}
        self._singleton_instance: Optional[Any] = None
        self._creation_lock = asyncio.Lock()
    
    async def create_instance(self, container: 'Container') -> Any:
        """Create service instance with dependency resolution"""
        if self.lifetime == Lifetime.SINGLETON and self._singleton_instance is not None:
            return self._singleton_instance
        
        async with self._creation_lock:
            # Double-check pattern for singleton
            if self.lifetime == Lifetime.SINGLETON and self._singleton_instance is not None:
                return self._singleton_instance
            
            instance = await self._create_new_instance(container)
            
            if self.lifetime == Lifetime.SINGLETON:
                self._singleton_instance = instance
            
            return instance
    
    async def _create_new_instance(self, container: 'Container') -> Any:
        """Create new instance with dependency injection"""
        if self.instance is not None:
            return self.instance
        
        if self.factory is not None:
            return await self._invoke_factory(self.factory, container)
        
        # Constructor injection
        constructor = self.implementation_type.__init__
        sig = inspect.signature(constructor)
        
        kwargs = {}
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            
            param_type = param.annotation
            if param_type == inspect.Parameter.empty:
                # Try to get from type hints
                type_hints = get_type_hints(self.implementation_type)
                param_type = type_hints.get(param_name)
            
            if param_type and param_type != inspect.Parameter.empty:
                dependency = await container.get_service_async(param_type)
                kwargs[param_name] = dependency
        
        return self.implementation_type(**kwargs)
    
    async def _invoke_factory(self, factory: Callable, container: 'Container') -> Any:
        """Invoke factory with dependency injection"""
        sig = inspect.signature(factory)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            param_type = param.annotation
            if param_type and param_type != inspect.Parameter.empty:
                dependency = await container.get_service_async(param_type)
                kwargs[param_name] = dependency
        
        result = factory(**kwargs)
        if asyncio.iscoroutine(result):
            result = await result
        
        return result


class Container(IServiceProvider):
    """Dependency injection container with async support"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._scoped_instances: Dict[Type, Any] = {}
        self._disposed = False
    
    def register_singleton(
        self, 
        service_type: Type[T], 
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None,
        instance: Optional[T] = None
    ) -> 'Container':
        """Register a singleton service"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=Lifetime.SINGLETON
        )
        self._services[service_type] = descriptor
        return self
    
    def register_transient(
        self, 
        service_type: Type[T], 
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None
    ) -> 'Container':
        """Register a transient service"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            lifetime=Lifetime.TRANSIENT
        )
        self._services[service_type] = descriptor
        return self
    
    def register_scoped(
        self, 
        service_type: Type[T], 
        implementation_type: Optional[Type[T]] = None,
        factory: Optional[Callable[..., T]] = None
    ) -> 'Container':
        """Register a scoped service"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            lifetime=Lifetime.SCOPED
        )
        self._services[service_type] = descriptor
        return self
    
    def get_service(self, service_type: Type[T]) -> T:
        """Get service synchronously (blocking)"""
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self.get_service_async(service_type))
    
    async def get_service_async(self, service_type: Type[T]) -> T:
        """Get service asynchronously"""
        if self._disposed:
            raise RuntimeError("Container has been disposed")
        
        if service_type not in self._services:
            raise KeyError(f"Service {service_type} not registered")
        
        descriptor = self._services[service_type]
        
        if descriptor.lifetime == Lifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            instance = await descriptor.create_instance(self)
            self._scoped_instances[service_type] = instance
            return instance
        
        return await descriptor.create_instance(self)
    
    def get_required_service(self, service_type: Type[T]) -> T:
        """Get required service (throws if not found)"""
        return self.get_service(service_type)
    
    async def get_required_service_async(self, service_type: Type[T]) -> T:
        """Get required service asynchronously (throws if not found)"""
        return await self.get_service_async(service_type)
    
    @asynccontextmanager
    async def create_scope(self):
        """Create a service scope for scoped services"""
        old_scoped = self._scoped_instances.copy()
        self._scoped_instances.clear()
        
        try:
            yield self
        finally:
            # Dispose scoped services
            for instance in self._scoped_instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        if asyncio.iscoroutinefunction(instance.dispose):
                            await instance.dispose()
                        else:
                            instance.dispose()
                    except Exception as e:
                        logger.warning(f"Error disposing scoped service: {e}")
            
            # Restore previous scope
            self._scoped_instances = old_scoped
    
    async def dispose(self):
        """Dispose the container and all singleton services"""
        if self._disposed:
            return
        
        self._disposed = True
        
        # Dispose singleton services
        for descriptor in self._services.values():
            if (descriptor.lifetime == Lifetime.SINGLETON and 
                descriptor._singleton_instance is not None):
                instance = descriptor._singleton_instance
                if hasattr(instance, 'dispose'):
                    try:
                        if asyncio.iscoroutinefunction(instance.dispose):
                            await instance.dispose()
                        else:
                            instance.dispose()
                    except Exception as e:
                        logger.warning(f"Error disposing singleton service: {e}")
        
        # Dispose scoped services
        for instance in self._scoped_instances.values():
            if hasattr(instance, 'dispose'):
                try:
                    if asyncio.iscoroutinefunction(instance.dispose):
                        await instance.dispose()
                    else:
                        instance.dispose()
                except Exception as e:
                    logger.warning(f"Error disposing scoped service: {e}")
        
        self._services.clear()
        self._scoped_instances.clear()


# Global container instance
_container: Optional[Container] = None


def get_container() -> Container:
    """Get the global container instance"""
    global _container
    if _container is None:
        _container = Container()
    return _container


def reset_container():
    """Reset the global container"""
    global _container
    if _container:
        asyncio.create_task(_container.dispose())
    _container = None 