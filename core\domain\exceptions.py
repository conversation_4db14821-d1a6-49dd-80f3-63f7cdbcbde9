"""
Domain Exceptions

This module defines custom exceptions for the domain layer.
These exceptions represent business rule violations and domain-specific errors.
"""

from typing import Any, Dict, Optional


class DomainException(Exception):
    """Base domain exception"""

    def __init__(self, message: str, code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}

    def __str__(self) -> str:
        return f"{self.code}: {self.message}"

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message='{self.message}', code='{self.code}', details={self.details})"


class ValidationError(DomainException):
    """Validation error exception"""

    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field = field
        self.value = value
        if field:
            self.details["field"] = field
        if value is not None:
            self.details["value"] = value


class ServiceUnavailableError(DomainException):
    """Service unavailable exception"""

    def __init__(self, service_name: str, reason: Optional[str] = None):
        message = f"Service '{service_name}' is unavailable"
        if reason:
            message += f": {reason}"
        super().__init__(message, "SERVICE_UNAVAILABLE")
        self.service_name = service_name
        self.reason = reason
        self.details["service_name"] = service_name
        if reason:
            self.details["reason"] = reason


class CircuitBreakerError(DomainException):
    """Circuit breaker exception"""

    def __init__(self, service_name: str, state: str):
        message = f"Circuit breaker for service '{service_name}' is {state}"
        super().__init__(message, "CIRCUIT_BREAKER_OPEN")
        self.service_name = service_name
        self.state = state
        self.details.update({
            "service_name": service_name,
            "state": state
        })


class ConfigurationError(DomainException):
    """Configuration error exception"""

    def __init__(self, setting_name: str, reason: Optional[str] = None):
        message = f"Configuration error for setting '{setting_name}'"
        if reason:
            message += f": {reason}"
        super().__init__(message, "CONFIGURATION_ERROR")
        self.setting_name = setting_name
        self.reason = reason
        self.details["setting_name"] = setting_name
        if reason:
            self.details["reason"] = reason


class PluginError(DomainException):
    """Plugin error exception"""

    def __init__(self, plugin_name: str, operation: str, reason: Optional[str] = None):
        message = f"Plugin '{plugin_name}' failed during {operation}"
        if reason:
            message += f": {reason}"
        super().__init__(message, "PLUGIN_ERROR")
        self.plugin_name = plugin_name
        self.operation = operation
        self.reason = reason
        self.details.update({
            "plugin_name": plugin_name,
            "operation": operation
        })
        if reason:
            self.details["reason"] = reason


class AuthenticationError(DomainException):
    """Authentication error exception"""

    def __init__(self, reason: Optional[str] = None):
        message = "Authentication failed"
        if reason:
            message += f": {reason}"
        super().__init__(message, "AUTHENTICATION_ERROR")
        self.reason = reason
        if reason:
            self.details["reason"] = reason


class AuthorizationError(DomainException):
    """Authorization error exception"""

    def __init__(self, resource: str, action: str, reason: Optional[str] = None):
        message = f"Access denied for action '{action}' on resource '{resource}'"
        if reason:
            message += f": {reason}"
        super().__init__(message, "AUTHORIZATION_ERROR")
        self.resource = resource
        self.action = action
        self.reason = reason
        self.details.update({
            "resource": resource,
            "action": action
        })
        if reason:
            self.details["reason"] = reason


class BusinessRuleViolationError(DomainException):
    """Business rule violation exception"""

    def __init__(self, rule_name: str, context: Optional[Dict[str, Any]] = None):
        message = f"Business rule violation: {rule_name}"
        super().__init__(message, "BUSINESS_RULE_VIOLATION")
        self.rule_name = rule_name
        self.context = context or {}
        self.details.update({
            "rule_name": rule_name,
            "context": self.context
        })


class CacheError(DomainException):
    """Cache operation error exception"""

    def __init__(self, operation: str, key: str, reason: Optional[str] = None):
        message = f"Cache {operation} failed for key '{key}'"
        if reason:
            message += f": {reason}"
        super().__init__(message, "CACHE_ERROR")
        self.operation = operation
        self.key = key
        self.reason = reason
        self.details.update({
            "operation": operation,
            "key": key
        })
        if reason:
            self.details["reason"] = reason


class RepositoryError(DomainException):
    """Repository operation error exception"""

    def __init__(self, operation: str, entity_type: str, reason: Optional[str] = None):
        message = f"Repository {operation} failed for {entity_type}"
        if reason:
            message += f": {reason}"
        super().__init__(message, "REPOSITORY_ERROR")
        self.operation = operation
        self.entity_type = entity_type
        self.reason = reason
        self.details.update({
            "operation": operation,
            "entity_type": entity_type
        })
        if reason:
            self.details["reason"] = reason


class ServiceRegistryError(DomainException):
    """Service registry operation error exception"""

    def __init__(self, reason: str):
        message = f"Service registry error: {reason}"
        super().__init__(message, "SERVICE_REGISTRY_ERROR")
        self.reason = reason
        self.details["reason"] = reason