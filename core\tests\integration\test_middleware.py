import pytest
import asyncio
from datetime import datetime, timedelta, timezone
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.responses import J<PERSON><PERSON>esponse

from core.presentation.middleware import (
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
    RequestLoggingMiddleware
)


@pytest.fixture
def app():
    return FastAPI()


@pytest.fixture
def mock_request():
    request = MagicMock(spec=Request)
    request.client = MagicMock()
    request.client.host = "127.0.0.1"
    request.method = "GET"
    request.url = MagicMock()
    request.url.path = "/test"
    return request


@pytest.fixture
def mock_response():
    response = MagicMock(spec=Response)
    response.headers = {}
    response.status_code = 200
    return response


@pytest.mark.asyncio
async def test_rate_limit_middleware_under_limit(app, mock_request):
    """Test rate limit middleware when under the limit"""
    # Setup middleware with 2 requests per minute
    middleware = RateLimitMiddleware(app, requests_per_minute=2)

    # Mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "ok"})

    # First request should pass
    response1 = await middleware.dispatch(mock_request, mock_call_next)
    assert response1.status_code == 200

    # Second request should also pass
    response2 = await middleware.dispatch(mock_request, mock_call_next)
    assert response2.status_code == 200

    # Check that requests were recorded
    assert len(middleware.requests["127.0.0.1"]) == 2


@pytest.mark.asyncio
async def test_rate_limit_middleware_exceeds_limit(app, mock_request):
    """Test rate limit middleware when exceeding the limit"""
    # Setup middleware with 2 requests per minute
    middleware = RateLimitMiddleware(app, requests_per_minute=2)

    # Mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "ok"})

    # Make two requests to reach the limit
    await middleware.dispatch(mock_request, mock_call_next)
    await middleware.dispatch(mock_request, mock_call_next)

    # Third request should be rate limited
    response3 = await middleware.dispatch(mock_request, mock_call_next)
    assert response3.status_code == 429
    assert "Rate limit exceeded" in response3.body.decode()


@pytest.mark.asyncio
async def test_rate_limit_middleware_cleanup(app, mock_request):
    """Test rate limit middleware cleanup of old requests"""
    # Setup middleware
    middleware = RateLimitMiddleware(app, requests_per_minute=10)

    # Add some old requests (over a minute ago)
    old_time = datetime.now(timezone.utc) - timedelta(minutes=2)
    middleware.requests["127.0.0.1"] = [old_time, old_time]

    # Mock call_next function
    async def mock_call_next(request):
        return JSONResponse(content={"status": "ok"})

    # Make a new request
    await middleware.dispatch(mock_request, mock_call_next)

    # Old requests should be cleaned up, only the new one remains
    assert len(middleware.requests["127.0.0.1"]) == 1


@pytest.mark.asyncio
async def test_security_headers_middleware(app, mock_request, mock_response):
    """Test security headers middleware adds the correct headers"""
    # Setup middleware
    middleware = SecurityHeadersMiddleware(app)

    # Mock call_next function
    async def mock_call_next(request):
        return mock_response

    # Process request
    response = await middleware.dispatch(mock_request, mock_call_next)

    # Check security headers were added
    assert response.headers["X-Content-Type-Options"] == "nosniff"
    assert response.headers["X-Frame-Options"] == "DENY"
    assert response.headers["X-XSS-Protection"] == "1; mode=block"
    assert "max-age=31536000" in response.headers["Strict-Transport-Security"]


@pytest.mark.asyncio
async def test_request_logging_middleware(app, mock_request, mock_response):
    """Test request logging middleware adds timing headers"""
    # Setup middleware
    middleware = RequestLoggingMiddleware(app)

    # Mock call_next function
    async def mock_call_next(request):
        return mock_response

    # Mock time.time to return consistent values
    with patch('time.time') as mock_time:
        mock_time.side_effect = [100.0, 100.5]  # Start time, end time

        # Process request
        response = await middleware.dispatch(mock_request, mock_call_next)

        # Check timing header was added
        assert "X-Process-Time" in response.headers
        assert float(response.headers["X-Process-Time"]) == 0.5