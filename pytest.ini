[tool:pytest]
testpaths = 
    core/tests
    core/application/tests
    core/domain/tests
    core/infrastructure/tests
    core/presentation/tests
    services/*/tests
    tests

python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Coverage settings
addopts = 
    --cov=core
    --cov=services
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml:coverage.xml
    --cov-fail-under=85
    --strict-markers
    --strict-config

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    system: System tests
    slow: Slow tests
    cli: CLI tests
