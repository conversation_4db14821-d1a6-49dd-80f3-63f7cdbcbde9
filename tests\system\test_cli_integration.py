#!/usr/bin/env python3
"""
CLI Integration Tests

This module contains integration tests for the CLI monitoring tool with real services.
Run this after starting your core API and translation service.
"""

import asyncio
import httpx
import json
import pytest


@pytest.mark.system
@pytest.mark.asyncio
async def test_real_services():
    """Test the CLI monitoring with your actual running services"""
    
    print("🚀 Testing CLI with Real Services")
    print("=" * 50)
    
    # Test if core API is running
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/api/v1/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Core API is running: {health_data.get('status', 'unknown')}")
                print(f"   Version: {health_data.get('version', 'unknown')}")
                print(f"   Uptime: {health_data.get('uptime', 0):.1f}s")
            else:
                print(f"❌ Core API returned status {response.status_code}")
                return
    except Exception as e:
        print(f"❌ Core API is not running: {e}")
        print("   Please start the core API first: python -m core.main")
        return
    
    # Test service registration
    print("\n📋 Checking registered services...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/api/v1/services")
            if response.status_code == 200:
                services_data = response.json()
                services = services_data.get("services", [])
                print(f"   Found {len(services)} registered services:")
                
                for service in services:
                    status = "✅ Healthy" if service.get("is_healthy", False) else "❌ Unhealthy"
                    service_type = service.get("metadata", {}).get("type", "service")
                    print(f"   - {service['name']} ({service_type}): {status}")
                    print(f"     URL: {service['url']}")
                    print(f"     Version: {service['version']}")
                    
                if not services:
                    print("   No services registered yet")
                    print("   Try starting the translation service: python services/translation/main.py")
            else:
                print(f"   ❌ Failed to get services: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error getting services: {e}")
    
    # Test metrics
    print("\n📊 Checking system metrics...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/api/v1/metrics")
            if response.status_code == 200:
                metrics_data = response.json()
                metrics = metrics_data.get("metrics", {})
                
                counters = metrics.get("counters", {})
                gauges = metrics.get("gauges", {})
                histograms = metrics.get("histograms", {})
                
                print(f"   Counters: {len(counters)} metrics")
                for key, value in counters.items():
                    print(f"     {key}: {value}")
                
                print(f"   Gauges: {len(gauges)} metrics")
                for key, value in gauges.items():
                    print(f"     {key}: {value}")
                
                print(f"   Histograms: {len(histograms)} metrics")
                for key, value in histograms.items():
                    if isinstance(value, list):
                        print(f"     {key}: {len(value)} samples")
                    else:
                        print(f"     {key}: {value}")
            else:
                print(f"   ❌ Failed to get metrics: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error getting metrics: {e}")


@pytest.mark.system
@pytest.mark.asyncio
async def test_service_registration():
    """Test service registration functionality"""
    print("\n🔧 Testing service registration...")
    try:
        test_service = {
            "name": "test-cli-service",
            "url": "http://test-cli-service:8999",
            "version": "1.0.0",
            "health_endpoint": "/health",
            "metadata": {
                "type": "test",
                "description": "CLI test service",
                "capabilities": ["testing", "monitoring"]
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/v1/services/register",
                json=test_service
            )
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Successfully registered test service")
                print(f"   Service: {result.get('service', 'unknown')}")
                print(f"   API Key: {result.get('api_key', 'none')}")
            else:
                print(f"   ❌ Failed to register service: {response.status_code}")
                print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error registering service: {e}")


@pytest.mark.system
@pytest.mark.asyncio
async def test_service_calls():
    """Test service calls functionality"""
    print("\n🔄 Testing service calls...")
    try:
        async with httpx.AsyncClient() as client:
            # First check if translation service is registered
            response = await client.get("http://localhost:8000/api/v1/services")
            services_data = response.json()
            services = services_data.get("services", [])
            
            translation_service = None
            for service in services:
                if "translation" in service["name"].lower():
                    translation_service = service
                    break
            
            if translation_service:
                print(f"   Found translation service: {translation_service['name']}")
                
                # Try to call the translation service
                call_data = {
                    "service_name": translation_service["name"],
                    "endpoint": "/translate",
                    "method": "POST",
                    "data": {
                        "text": "Hello from CLI test!",
                        "target_language": "es"
                    }
                }
                
                response = await client.post(
                    "http://localhost:8000/api/v1/services/call",
                    json=call_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Service call successful")
                    print(f"   Response: {json.dumps(result, indent=2)}")
                else:
                    print(f"   ❌ Service call failed: {response.status_code}")
                    print(f"   Response: {response.text}")
            else:
                print("   No translation service found")
                print("   Start translation service to test service calls")
    except Exception as e:
        print(f"   ❌ Error testing service calls: {e}")


def main():
    """Main entry point for manual testing"""
    async def run_all_tests():
        await test_real_services()
        await test_service_registration()
        await test_service_calls()
        
        print("\n🎉 CLI testing complete!")
        print("\nNext steps:")
        print("1. Use the CLI tool: python cli/service_monitor.py status")
        print("2. Start more services and watch them register")
        print("3. Use 'python cli/service_monitor.py watch' for real-time monitoring")
    
    asyncio.run(run_all_tests())


if __name__ == "__main__":
    main()
