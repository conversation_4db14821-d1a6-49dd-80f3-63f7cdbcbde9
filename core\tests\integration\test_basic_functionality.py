"""
Basic Functionality Tests

This module tests the basic functionality of the refactored core system
to ensure the main components work correctly.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from core.main import create_app
from core.config.settings import create_settings


class TestBasicFunctionality:
    """Test suite for basic functionality"""

    def test_app_creation_success(self):
        """Test that the app can be created successfully"""
        app = create_app()
        assert app is not None
        assert hasattr(app, 'title')
        assert hasattr(app, 'state')

    def test_settings_creation(self):
        """Test that settings can be created"""
        settings = create_settings()
        assert settings is not None
        assert hasattr(settings, 'APP_NAME')
        assert hasattr(settings, 'APP_VERSION')

    def test_memory_cache_direct_import(self):
        """Test direct import of MemoryCache"""
        try:
            from core.infrastructure.cache import MemoryCache
            
            # Create instance
            cache = MemoryCache(max_size=10, default_ttl=60)
            assert cache is not None
            assert cache.max_size == 10
            assert cache.default_ttl == 60
            
        except ImportError:
            pytest.skip("MemoryCache not available")

    @pytest.mark.asyncio
    async def test_memory_cache_operations(self):
        """Test basic memory cache operations"""
        try:
            from core.infrastructure.cache import MemoryCache
            
            cache = MemoryCache(max_size=10, default_ttl=60)
            
            # Test set and get
            await cache.set("test_key", "test_value")
            value = await cache.get("test_key")
            assert value == "test_value"
            
            # Test delete
            await cache.delete("test_key")
            value = await cache.get("test_key")
            assert value is None
            
        except ImportError:
            pytest.skip("MemoryCache not available")

    def test_service_registry_direct_import(self):
        """Test direct import of ServiceRegistry"""
        try:
            from core.infrastructure.service_registry import ServiceRegistryImpl
            
            registry = ServiceRegistryImpl()
            assert registry is not None
            
        except ImportError:
            pytest.skip("ServiceRegistryImpl not available")

    @pytest.mark.asyncio
    async def test_service_registry_operations(self):
        """Test basic service registry operations"""
        try:
            from core.infrastructure.service_registry import ServiceRegistryImpl
            
            registry = ServiceRegistryImpl()
            
            # Test service registration
            result = await registry.register_service(
                name="test_service",
                url="http://localhost:8000",
                version="1.0.0"
            )
            assert result["success"] is True
            assert "api_key" in result
            
            # Test service retrieval
            service = await registry.get_service("test_service")
            assert service is not None
            assert service["name"] == "test_service"
            
            # Test service listing
            services = await registry.list_services()
            assert "test_service" in services["services"]
            
        except ImportError:
            pytest.skip("ServiceRegistryImpl not available")

    def test_infrastructure_module_structure(self):
        """Test that infrastructure modules have proper structure"""
        # Test cache module
        try:
            import core.infrastructure.cache as cache_module
            assert hasattr(cache_module, '__all__')
        except ImportError:
            pytest.skip("Cache module not available")
        
        # Test registry module
        try:
            import core.infrastructure.registry as registry_module
            assert hasattr(registry_module, '__all__')
        except ImportError:
            pytest.skip("Registry module not available")

    def test_domain_interfaces_import(self):
        """Test that domain interfaces can be imported"""
        try:
            from core.domain.interfaces import ICache, IServiceRegistry
            assert ICache is not None
            assert IServiceRegistry is not None
        except ImportError:
            pytest.skip("Domain interfaces not available")

    def test_container_functionality(self):
        """Test dependency injection container"""
        try:
            from core.infrastructure.container import Container
            
            container = Container()
            assert container is not None
            
            # Test singleton registration
            container.register_singleton(str, instance="test_value")
            
            # Test service retrieval
            value = container.get_service(str)
            assert value == "test_value"
            
        except ImportError:
            pytest.skip("Container not available")

    def test_application_factory_initialization(self):
        """Test application factory initialization"""
        try:
            from core.application.factory import ApplicationFactory
            
            settings = create_settings()
            factory = ApplicationFactory(settings)
            
            assert factory.settings == settings
            assert factory.container is not None
            assert factory.plugin_manager is not None
            
        except ImportError:
            pytest.skip("ApplicationFactory not available")

    def test_logging_configuration(self):
        """Test that logging is properly configured"""
        import logging
        
        # Test that core logger exists
        logger = logging.getLogger('core')
        assert logger is not None
        
        # Test that main logger exists
        main_logger = logging.getLogger('core.main')
        assert main_logger is not None

    def test_error_handling_patterns(self):
        """Test basic error handling patterns"""
        # Test that RuntimeError is raised for invalid operations
        with pytest.raises(RuntimeError):
            raise RuntimeError("Test error")
        
        # Test that ValueError is raised for invalid parameters
        with pytest.raises(ValueError):
            raise ValueError("Test validation error")

    def test_type_annotations_present(self):
        """Test that key functions have type annotations"""
        from core.main import create_app
        import inspect
        
        sig = inspect.signature(create_app)
        # Should have return annotation
        assert sig.return_annotation is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
