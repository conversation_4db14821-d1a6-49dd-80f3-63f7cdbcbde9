#!/usr/bin/env python3
"""
Redis Connection Test - No Authentication

Test Redis connection without any authentication.
"""

import asyncio


async def test_redis_no_auth():
    """Test Redis without any authentication"""
    print("🔧 Redis Connection Test - No Authentication")
    print("=" * 60)
    
    try:
        # Test with synchronous redis - no auth
        print("1. Testing synchronous Redis (no auth)...")
        import redis
        
        r = redis.Redis(
            host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
            port=10401,
            decode_responses=True
        )
        
        # Test basic operations
        success = r.set('foo', 'bar')
        print(f"   Set operation result: {success}")
        
        result = r.get('foo')
        print(f"   Get operation result: {result}")
        
        if result == 'bar':
            print("   ✅ Synchronous Redis (no auth) working!")
            
            # Get Redis info
            info = r.info()
            print(f"   Redis version: {info.get('redis_version', 'unknown')}")
            
            # Cleanup
            r.delete('foo')
            r.close()
            return True
        else:
            print("   ❌ Synchronous Redis (no auth) failed")
            r.close()
            
    except Exception as e:
        print(f"   ❌ Synchronous Redis (no auth) failed: {e}")
    
    try:
        # Test with async redis - no auth
        print("\n2. Testing async Redis (no auth)...")
        import redis.asyncio as aioredis
        
        redis_client = await aioredis.from_url(
            "redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401",
            decode_responses=True
        )
        
        # Test ping
        pong = await redis_client.ping()
        print(f"   Ping result: {pong}")
        
        # Test set/get
        await redis_client.set('async_test', 'async_value', ex=30)
        value = await redis_client.get('async_test')
        print(f"   Set/Get result: {value}")
        
        if value == 'async_value':
            print("   ✅ Async Redis (no auth) working!")
            
            # Cleanup
            await redis_client.delete('async_test')
            await redis_client.close()
            return True
        else:
            print("   ❌ Async Redis (no auth) failed")
            await redis_client.close()
            
    except Exception as e:
        print(f"   ❌ Async Redis (no auth) failed: {e}")
    
    try:
        # Test with different connection parameters
        print("\n3. Testing with different connection parameters...")
        import redis
        
        # Try with socket_timeout
        r = redis.Redis(
            host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
            port=10401,
            decode_responses=True,
            socket_timeout=10,
            socket_connect_timeout=10,
            retry_on_timeout=True
        )
        
        # Test basic operations
        success = r.set('timeout_test', 'timeout_value', ex=30)
        print(f"   Set with timeout result: {success}")
        
        result = r.get('timeout_test')
        print(f"   Get with timeout result: {result}")
        
        if result == 'timeout_value':
            print("   ✅ Redis with timeout settings working!")
            
            # Cleanup
            r.delete('timeout_test')
            r.close()
            return True
        else:
            print("   ❌ Redis with timeout settings failed")
            r.close()
            
    except Exception as e:
        print(f"   ❌ Redis with timeout settings failed: {e}")
    
    return False


async def test_redis_ssl():
    """Test Redis with SSL"""
    print("\n4. Testing Redis with SSL...")
    
    try:
        import redis
        
        # Try SSL connection
        r = redis.Redis(
            host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
            port=10401,
            decode_responses=True,
            ssl=True,
            ssl_check_hostname=False,
            ssl_cert_reqs=None
        )
        
        # Test basic operations
        success = r.set('ssl_test', 'ssl_value', ex=30)
        print(f"   SSL Set result: {success}")
        
        result = r.get('ssl_test')
        print(f"   SSL Get result: {result}")
        
        if result == 'ssl_value':
            print("   ✅ Redis with SSL working!")
            
            # Cleanup
            r.delete('ssl_test')
            r.close()
            return True
        else:
            print("   ❌ Redis with SSL failed")
            r.close()
            
    except Exception as e:
        print(f"   ❌ Redis with SSL failed: {e}")
    
    return False


async def test_redis_different_ports():
    """Test Redis on different ports"""
    print("\n5. Testing different ports...")
    
    # Common Redis ports to try
    ports = [10401, 6379, 6380, 10400, 10402]
    
    for port in ports:
        try:
            print(f"   Trying port {port}...")
            import redis
            
            r = redis.Redis(
                host='memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com',
                port=port,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # Test basic operations
            success = r.set(f'port_test_{port}', f'port_value_{port}', ex=30)
            result = r.get(f'port_test_{port}')
            
            if result == f'port_value_{port}':
                print(f"   ✅ Port {port} working!")
                
                # Cleanup
                r.delete(f'port_test_{port}')
                r.close()
                return port
            else:
                r.close()
                
        except Exception as e:
            print(f"   ❌ Port {port} failed: {e}")
    
    return None


async def main():
    """Main test function"""
    print("🚀 Redis Connection Test Suite - No Authentication")
    print("=" * 70)
    
    # Test basic connection without auth
    basic_success = await test_redis_no_auth()
    
    if not basic_success:
        # Try SSL
        ssl_success = await test_redis_ssl()
        
        if not ssl_success:
            # Try different ports
            working_port = await test_redis_different_ports()
            
            if working_port:
                print(f"\n🎉 Found working port: {working_port}")
                basic_success = True
    
    if basic_success:
        print(f"\n🎉 Redis connection successful!")
        print(f"✅ Your Redis instance is accessible")
        print(f"✅ No authentication required")
        
        # Update .env file
        print(f"\n📝 Updating .env file...")
        try:
            with open(".env", "r") as f:
                content = f.read()
            
            # Update REDIS_URL
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('REDIS_URL='):
                    lines[i] = "REDIS_URL=redis://memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com:10401"
                    break
            
            with open(".env", "w") as f:
                f.write('\n'.join(lines))
            
            print("   ✅ .env file updated")
            
        except Exception as e:
            print(f"   ❌ Failed to update .env: {e}")
        
    else:
        print(f"\n❌ All Redis connection attempts failed")
        print(f"Please check:")
        print(f"   • Redis instance status in your dashboard")
        print(f"   • Network connectivity")
        print(f"   • Firewall settings")
        print(f"   • IP whitelist in Redis Cloud")
    
    return basic_success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
