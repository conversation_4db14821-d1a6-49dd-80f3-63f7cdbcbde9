"""
Infrastructure Layer

This layer contains:
- Dependency Injection Container
- Concrete implementations of domain interfaces
- External service adapters and connectors
- Infrastructure-specific implementations

Organized into sub-modules:
- supabase/: Unified Supabase ecosystem (auth, database, storage, realtime)
- auth/: Authentication & authorization providers
- database/: Database operations and stores
- cache/: Caching systems (Redis, memory, fallback)
- monitoring/: Health checks, metrics, and scoring
- registry/: Service discovery and registration
"""

from typing import Optional, Any

# Core container
from .container import Container

# Supabase ecosystem (preferred)
from .supabase import (
    UnifiedSupabaseClient,
    SupabaseClient,  # Alias
    SupabaseClientManager,  # Legacy alias
)

# Import Supabase services
try:
    from .supabase.auth import SupabaseAuthProvider
    from .supabase.database import SupabaseDatabase, SupabaseUserStore, SupabaseRoleStore
    from .supabase.storage import SupabaseStorage
    from .supabase.realtime import SupabaseRealtimeService, RealtimeEventType, PresenceState
except ImportError:
    # Services will be available after fixing import issues
    SupabaseAuthProvider = None
    SupabaseDatabase = None
    SupabaseUserStore = None
    SupabaseRoleStore = None
    SupabaseStorage = None
    SupabaseRealtimeService = None
    RealtimeEventType = None
    PresenceState = None

# Import modular cache system
try:
    from .cache import RedisCache, MemoryCache, create_cache
except ImportError:
    RedisCache = None
    MemoryCache = None
    create_cache = None

# Import modular monitoring system
try:
    from .monitoring import PrometheusMetricsCollector, HealthChecker, ScoringSystem
except ImportError:
    PrometheusMetricsCollector = None
    HealthChecker = None
    ScoringSystem = None

# Import modular auth system
try:
    from .auth import JWTSecurityProvider, DatabaseSecurityProvider
except ImportError:
    # Fallback to legacy imports for transition period
    try:
        from .security import JWTSecurityProvider, DatabaseSecurityProvider
    except ImportError:
        JWTSecurityProvider = None
        DatabaseSecurityProvider = None

# Import modular database system
try:
    from .database import DatabaseUserStore, DatabaseRoleStore, DatabaseInitializer
except ImportError:
    # Fallback to legacy imports for transition period
    try:
        from .user_store import DatabaseUserStore, DatabaseRoleStore
        from .database import DatabaseInitializer
    except ImportError:
        DatabaseUserStore = None
        DatabaseRoleStore = None
        DatabaseInitializer = None

# Import modular registry system
try:
    from .registry import ServiceRegistryImpl, RedisServiceRegistry, RedisServiceRegistryV2, create_service_registry
except ImportError:
    # Fallback to legacy imports for transition period
    try:
        from .service_registry import ServiceRegistryImpl
        from .redis_service_registry import RedisServiceRegistry
        RedisServiceRegistryV2 = None
        create_service_registry = None
    except ImportError:
        ServiceRegistryImpl = None
        RedisServiceRegistry = None
        RedisServiceRegistryV2 = None
        create_service_registry = None

# Legacy Supabase imports removed - use new modular structure instead
# Old imports like:
# - from core.infrastructure.unified_supabase import UnifiedSupabaseClient
# - from core.infrastructure.supabase_realtime import SupabaseRealtimeService
# - from core.infrastructure.supabase_providers import SupabaseSecurityProvider
#
# Should now use:
# - from core.infrastructure.supabase import UnifiedSupabaseClient
# - from core.infrastructure.supabase.realtime import SupabaseRealtimeService
# - from core.infrastructure.supabase.auth import SupabaseAuthProvider

__all__ = [
    # Core
    "Container",

    # Supabase ecosystem (preferred)
    "UnifiedSupabaseClient",
    "SupabaseClient",
    "SupabaseClientManager",

    # Supabase services (if available)
    "SupabaseAuthProvider",
    "SupabaseDatabase",
    "SupabaseUserStore",
    "SupabaseRoleStore",
    "SupabaseStorage",
    "SupabaseRealtimeService",
    "RealtimeEventType",
    "PresenceState",

    # Modular cache system
    "RedisCache",
    "MemoryCache",
    "create_cache",

    # Modular monitoring system
    "PrometheusMetricsCollector",
    "HealthChecker",
    "ScoringSystem",

    # Modular auth system
    "JWTSecurityProvider",
    "DatabaseSecurityProvider",

    # Modular database system
    "DatabaseUserStore",
    "DatabaseRoleStore",
    "DatabaseInitializer",

    # Modular registry system
    "ServiceRegistryImpl",
    "RedisServiceRegistry",
    "RedisServiceRegistryV2",
    "create_service_registry",
]