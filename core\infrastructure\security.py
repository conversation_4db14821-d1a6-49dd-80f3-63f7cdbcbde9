"""
Security Implementations

This module provides concrete implementations of the ISecurityProvider interface
including JWT authentication and password hashing.
"""

import hashlib
import hmac
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, List

from core.domain.interfaces import ISecurityProvider, IUserStore, IRoleStore
from core.domain.exceptions import AuthenticationError, AuthorizationError
from core.domain.models import User, UserStatus


class JWTSecurityProvider(ISecurityProvider):
    """JWT-based security provider implementation"""

    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self._jwt_available = False

        # Try to import JWT library
        try:
            from jose import jwt
            from jose import exceptions as jwt_exceptions
            self._jwt = jwt
            self._jwt_exceptions = jwt_exceptions
            self._jwt_available = True
            self._is_jose = True
        except ImportError:
            # Fallback to PyJWT
            try:
                import jwt
                self._jwt = jwt
                self._jwt_exceptions = jwt
                self._jwt_available = True
                self._is_jose = False
            except ImportError:
                pass

    def _ensure_jwt(self) -> None:
        """Ensure JWT library is available"""
        if not self._jwt_available:
            raise AuthenticationError("JWT library not available. Install with: pip install pyjwt")

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user with credentials"""
        username = credentials.get("username")
        password = credentials.get("password")

        if not username or not password:
            raise AuthenticationError("Username and password required")

        # TODO(out_of_scope): Implement actual user authentication
        # This is a placeholder implementation
        if username == "admin" and password == "admin":
            return {
                "user_id": "admin",
                "username": username,
                "roles": ["admin"],
                "permissions": ["*"],
            }

        return None

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action on resource"""
        permissions = user.get("permissions", [])
        roles = user.get("roles", [])

        # Check for wildcard permission
        if "*" in permissions:
            return True

        # Check for admin role
        if "admin" in roles:
            return True

        # Check specific permission
        permission = f"{resource}:{action}"
        return permission in permissions

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt or fallback to PBKDF2"""
        try:
            import bcrypt
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            import bcrypt
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
            return hmac.compare_digest(computed_hash, hashed)

    def create_token(self, payload: Dict[str, Any]) -> str:
        """Create JWT token"""
        self._ensure_jwt()

        now = datetime.now(timezone.utc)
        token_payload = {
            **payload,
            "iat": now,
            "exp": now + timedelta(minutes=self.access_token_expire_minutes),
            "type": "access"
        }

        return self._jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)

    def create_refresh_token(self, payload: Dict[str, Any]) -> str:
        """Create refresh JWT token"""
        self._ensure_jwt()

        now = datetime.now(timezone.utc)
        token_payload = {
            **payload,
            "iat": now,
            "exp": now + timedelta(days=self.refresh_token_expire_days),
            "type": "refresh"
        }

        return self._jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        self._ensure_jwt()

        try:
            payload = self._jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # Check token type
            if payload.get("type") != "access":
                raise AuthenticationError("Invalid token type")

            return payload
        except AuthenticationError:
            # Re-raise our own exceptions
            raise
        except Exception as e:
            # Handle both jose and PyJWT exceptions
            error_msg = str(e).lower()
            if "expired" in error_msg:
                raise AuthenticationError("Token has expired")
            else:
                raise AuthenticationError("Invalid token")

    def verify_refresh_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify refresh JWT token"""
        self._ensure_jwt()

        try:
            payload = self._jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # Check token type
            if payload.get("type") != "refresh":
                raise AuthenticationError("Invalid token type")

            return payload
        except AuthenticationError:
            # Re-raise our own exceptions
            raise
        except Exception as e:
            # Handle both jose and PyJWT exceptions
            error_msg = str(e).lower()
            if "expired" in error_msg:
                raise AuthenticationError("Refresh token has expired")
            else:
                raise AuthenticationError("Invalid refresh token")


class NoOpSecurityProvider(ISecurityProvider):
    """No-operation security provider for testing"""

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Always authenticate successfully"""
        return {"user_id": "test", "username": "test"}

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Always authorize successfully"""
        return True

    def hash_password(self, password: str) -> str:
        """Return password as-is (no hashing)"""
        return password

    def verify_password(self, password: str, hashed: str) -> bool:
        """Simple string comparison"""
        return password == hashed

    def create_token(self, payload: Dict[str, Any]) -> str:
        """Create a simple token"""
        import json
        return json.dumps(payload)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify simple token"""
        try:
            import json
            return json.loads(token)
        except Exception:
            return None


class BasicSecurityProvider(ISecurityProvider):
    """Basic security provider with simple username/password authentication"""

    def __init__(self, users: Optional[Dict[str, Dict[str, Any]]] = None):
        self.users = users or {
            "admin": {
                "password_hash": self.hash_password("admin"),
                "roles": ["admin"],
                "permissions": ["*"]
            }
        }

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate with username/password"""
        username = credentials.get("username")
        password = credentials.get("password")

        if not username or not password:
            return None

        user_data = self.users.get(username)
        if not user_data:
            return None

        if not self.verify_password(password, user_data["password_hash"]):
            return None

        return {
            "user_id": username,
            "username": username,
            "roles": user_data.get("roles", []),
            "permissions": user_data.get("permissions", []),
        }

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action"""
        permissions = user.get("permissions", [])
        roles = user.get("roles", [])

        # Check for wildcard permission
        if "*" in permissions:
            return True

        # Check for admin role
        if "admin" in roles:
            return True

        # Check specific permission
        permission = f"{resource}:{action}"
        return permission in permissions

    def hash_password(self, password: str) -> str:
        """Hash password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        computed_hash = hashlib.sha256(password.encode()).hexdigest()
        return hmac.compare_digest(computed_hash, hashed)


class DatabaseSecurityProvider(ISecurityProvider):
    """Database-backed security provider with JWT authentication and RBAC"""

    def __init__(
        self,
        user_store: IUserStore,
        role_store: IRoleStore,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        self.user_store = user_store
        self.role_store = role_store
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self._jwt_available = False

        # Try to import JWT library
        try:
            from jose import jwt
            from jose import exceptions as jwt_exceptions
            self._jwt = jwt
            self._jwt_exceptions = jwt_exceptions
            self._jwt_available = True
            self._is_jose = True
        except ImportError:
            # Fallback to PyJWT
            try:
                import jwt
                self._jwt = jwt
                self._jwt_exceptions = jwt
                self._jwt_available = True
                self._is_jose = False
            except ImportError:
                pass

    def _ensure_jwt(self) -> None:
        """Ensure JWT library is available"""
        if not self._jwt_available:
            raise AuthenticationError("JWT library not available. Install with: pip install pyjwt")

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user with database-backed credentials"""
        username = credentials.get("username")
        password = credentials.get("password")

        if not username or not password:
            raise AuthenticationError("Username and password required")

        # Get user from database
        user = await self.user_store.get_user_by_username(username)
        if not user:
            # Check by email as well
            user = await self.user_store.get_user_by_email(username)

        if not user:
            return None

        # Check if user account is active
        if user.status != UserStatus.ACTIVE:
            raise AuthenticationError(f"Account is {user.status.value}")

        # Verify password
        if not self.verify_password(password, user.password_hash):
            return None

        # Get user roles and permissions
        user_roles = await self.role_store.get_user_roles(user.id)
        permissions = set()
        role_names = []

        for role in user_roles:
            role_names.append(role.name)
            role_permissions = await self.role_store.get_role_permissions(role.id)
            permissions.update(role_permissions)

        # Add superuser permissions
        if user.is_superuser:
            permissions.add("*")
            if "admin" not in role_names:
                role_names.append("admin")

        # Update last login
        user.update_last_login()
        await self.user_store.update_user(user.id, {
            "last_login": user.last_login,
            "updated_at": user.updated_at
        })

        return {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_superuser": user.is_superuser,
            "is_verified": user.is_verified,
            "roles": role_names,
            "permissions": list(permissions),
            "last_login": user.last_login.isoformat() if user.last_login else None
        }

    async def authorize(self, user: Dict[str, Any], resource: str, action: str) -> bool:
        """Authorize user action based on roles and permissions"""
        permissions = user.get("permissions", [])
        roles = user.get("roles", [])

        # Check for wildcard permission (superuser)
        if "*" in permissions:
            return True

        # Check for admin role
        if "admin" in roles:
            return True

        # Check specific permission
        permission = f"{resource}:{action}"
        if permission in permissions:
            return True

        # Check for resource-level wildcard
        resource_wildcard = f"{resource}:*"
        if resource_wildcard in permissions:
            return True

        return False

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt or fallback to PBKDF2"""
        try:
            import bcrypt
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            import bcrypt
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except ImportError:
            # Fallback to PBKDF2
            salt = hashlib.sha256(self.secret_key.encode()).hexdigest()[:32]
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
            return hmac.compare_digest(computed_hash, hashed)

    def create_token(self, payload: Dict[str, Any]) -> str:
        """Create JWT token"""
        self._ensure_jwt()

        now = datetime.now(timezone.utc)
        token_payload = {
            **payload,
            "iat": now,
            "exp": now + timedelta(minutes=self.access_token_expire_minutes),
            "type": "access"
        }

        return self._jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)

    def create_refresh_token(self, payload: Dict[str, Any]) -> str:
        """Create refresh JWT token"""
        self._ensure_jwt()

        now = datetime.now(timezone.utc)
        token_payload = {
            **payload,
            "iat": now,
            "exp": now + timedelta(days=self.refresh_token_expire_days),
            "type": "refresh"
        }

        return self._jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        self._ensure_jwt()

        try:
            payload = self._jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # Check token type
            if payload.get("type") != "access":
                raise AuthenticationError("Invalid token type")

            return payload
        except AuthenticationError:
            # Re-raise our own exceptions
            raise
        except Exception as e:
            # Handle both jose and PyJWT exceptions
            error_msg = str(e).lower()
            if "expired" in error_msg:
                raise AuthenticationError("Token has expired")
            else:
                raise AuthenticationError("Invalid token")

    def verify_refresh_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify refresh JWT token"""
        self._ensure_jwt()

        try:
            payload = self._jwt.decode(token, self.secret_key, algorithms=[self.algorithm])

            # Check token type
            if payload.get("type") != "refresh":
                raise AuthenticationError("Invalid token type")

            return payload
        except AuthenticationError:
            # Re-raise our own exceptions
            raise
        except Exception as e:
            # Handle both jose and PyJWT exceptions
            error_msg = str(e).lower()
            if "expired" in error_msg:
                raise AuthenticationError("Refresh token has expired")
            else:
                raise AuthenticationError("Invalid refresh token")

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create a new user with hashed password"""
        # Hash the password
        if 'password' in user_data:
            user_data['password_hash'] = self.hash_password(user_data.pop('password'))

        # Create user in database
        return await self.user_store.create_user(user_data)

    async def update_user_password(self, user_id: str, new_password: str) -> bool:
        """Update user password with proper hashing"""
        password_hash = self.hash_password(new_password)
        return await self.user_store.update_password(user_id, password_hash)

    async def get_user_permissions(self, user_id: str) -> List[str]:
        """Get all permissions for a user"""
        user_roles = await self.role_store.get_user_roles(user_id)
        permissions = set()

        for role in user_roles:
            role_permissions = await self.role_store.get_role_permissions(role.id)
            permissions.update(role_permissions)

        # Check if user is superuser
        user = await self.user_store.get_user_by_id(user_id)
        if user and user.is_superuser:
            permissions.add("*")

        return list(permissions)