#!/usr/bin/env python3
"""
Redis Authentication Integration Tests

This module contains integration tests for Redis connection with authentication.
"""

import asyncio
import os
import pytest
from datetime import datetime


@pytest.mark.integration
@pytest.mark.asyncio
async def test_redis_with_auth():
    """Test Redis connection with authentication"""
    
    # Get Redis connection details
    hostname = "memcached-10401.c1.ap-southeast-1-1.ec2.redns.redis-cloud.com"
    port = 10401
    
    # Check for password in environment
    password = os.getenv("REDIS_PASSWORD")
    if not password:
        pytest.skip("No REDIS_PASSWORD found in environment")
    
    # Test different authentication methods
    auth_methods = [
        {
            "name": "Password only",
            "url": f"redis://:{password}@{hostname}:{port}"
        },
        {
            "name": "Default user with password",
            "url": f"redis://default:{password}@{hostname}:{port}"
        },
        {
            "name": "Password with database 0",
            "url": f"redis://:{password}@{hostname}:{port}/0"
        },
        {
            "name": "Default user with password and database 0",
            "url": f"redis://default:{password}@{hostname}:{port}/0"
        }
    ]
    
    successful_url = None
    
    for method in auth_methods:
        try:
            import redis.asyncio as aioredis
            
            redis_client = await aioredis.from_url(
                method['url'],
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=10,
                socket_connect_timeout=10
            )
            
            # Test ping
            pong = await redis_client.ping()
            assert pong is True
            
            # Test basic operations
            test_key = f"auth_test_{int(datetime.now().timestamp())}"
            await redis_client.set(test_key, "auth_working", ex=30)
            value = await redis_client.get(test_key)
            
            assert value == "auth_working"
            successful_url = method['url']
            
            # Get server info
            info = await redis_client.info("server")
            redis_version = info.get("redis_version", "unknown")
            assert redis_version != "unknown"
            
            # Cleanup
            await redis_client.delete(test_key)
            await redis_client.close()
            break
                
        except Exception:
            # Try next method
            continue
    
    assert successful_url is not None, "All authentication methods failed"


@pytest.mark.integration
@pytest.mark.asyncio
async def test_redis_info():
    """Test Redis connection and get detailed info"""
    
    # Try to connect with environment variables
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        pytest.skip("No REDIS_URL found in environment")
    
    import redis.asyncio as aioredis
    
    redis_client = await aioredis.from_url(redis_url)
    
    try:
        # Get comprehensive info
        info = await redis_client.info()
        
        # Verify basic info is available
        assert 'redis_version' in info
        assert 'redis_mode' in info
        assert 'used_memory_human' in info
        assert 'connected_clients' in info
        
        # Verify we can get numeric values
        assert isinstance(info.get('connected_clients', 0), int)
        assert isinstance(info.get('total_commands_processed', 0), int)
        
    finally:
        await redis_client.close()


@pytest.mark.integration
@pytest.mark.asyncio
async def test_redis_basic_operations():
    """Test basic Redis operations"""
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        pytest.skip("No REDIS_URL found in environment")
    
    import redis.asyncio as aioredis
    
    redis_client = await aioredis.from_url(redis_url)
    
    try:
        # Test string operations
        test_key = f"test_basic_{int(datetime.now().timestamp())}"
        await redis_client.set(test_key, "test_value", ex=60)
        value = await redis_client.get(test_key)
        assert value == "test_value"
        
        # Test TTL
        ttl = await redis_client.ttl(test_key)
        assert ttl > 0
        
        # Test delete
        deleted = await redis_client.delete(test_key)
        assert deleted == 1
        
        # Verify deletion
        value = await redis_client.get(test_key)
        assert value is None
        
    finally:
        await redis_client.close()


def main():
    """Main function for manual testing"""
    async def run_tests():
        print("🚀 Redis Authentication and Setup Tool")
        print("=" * 60)
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
        
        try:
            # Test authentication
            await test_redis_with_auth()
            print("✅ Redis authentication test passed")
            
            # Test detailed info
            await test_redis_info()
            print("✅ Redis info test passed")
            
            # Test basic operations
            await test_redis_basic_operations()
            print("✅ Redis basic operations test passed")
            
            print(f"\n✅ Redis setup complete!")
            print(f"You can now run the main Redis tests:")
            print(f"   python test_redis_setup.py")
            
            return True
            
        except Exception as e:
            print(f"❌ Redis tests failed: {e}")
            print(f"Please check your Redis Cloud dashboard for:")
            print(f"   • The correct password")
            print(f"   • IP whitelist settings")
            print(f"   • Instance status")
            return False
    
    return asyncio.run(run_tests())


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
