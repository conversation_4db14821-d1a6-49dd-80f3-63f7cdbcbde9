"""
Service Registry & Discovery Sub-Module

This module provides a unified service registry interface with multiple implementations:

Core Features:
- Distributed service discovery and registration
- Redis-based registry with fallback mechanisms
- Health monitoring and automatic service cleanup
- API key generation and management
- Service metadata and versioning support

Public API:
- IServiceRegistry: Interface for all service registry implementations
- ServiceRegistryImpl: In-memory service registry (development)
- RedisServiceRegistry: Redis-based distributed registry
- RedisServiceRegistryV2: Enhanced Redis registry with fallback
- ServiceInfo: Data structure for service information
- create_service_registry: Factory function for creating registries

Migrated from: core.infrastructure.service_registry, redis_service_registry*
"""

from typing import Optional, Dict, Any

# Core interfaces
try:
    from ...domain.interfaces import IServiceRegistry
except ImportError:
    # Fallback for development
    from abc import ABC, abstractmethod

    class IServiceRegistry(ABC):
        """Service registry interface placeholder"""

        @abstractmethod
        async def register_service(
            self,
            name: str,
            url: str,
            version: str,
            health_endpoint: str = "/health",
            metadata: Optional[Dict[str, Any]] = None
        ) -> Dict[str, Any]:
            """Register a service"""
            pass

        @abstractmethod
        async def get_service(self, name: str) -> Optional[Dict[str, Any]]:
            """Get service information"""
            pass

        @abstractmethod
        async def list_services(self) -> Dict[str, Any]:
            """List all registered services"""
            pass

# Import modular implementations first, then fallback to legacy
try:
    from .service_registry import ServiceRegistryImpl
except ImportError:
    try:
        from ..service_registry import ServiceRegistryImpl
    except ImportError:
        ServiceRegistryImpl = None

try:
    from .redis_registry import RedisServiceRegistry
except ImportError:
    try:
        from ..redis_service_registry import RedisServiceRegistry
    except ImportError:
        RedisServiceRegistry = None

try:
    from .redis_registry_v2 import RedisServiceRegistryV2, ServiceInfo
except ImportError:
    try:
        from ..redis_service_registry_v2 import RedisServiceRegistryV2, ServiceInfo
    except ImportError:
        RedisServiceRegistryV2 = None
        ServiceInfo = None

try:
    from ..cache.fallback import FallbackStrategy, RedisLimits
except ImportError:
    try:
        from ..redis_fallback_manager import FallbackStrategy, RedisLimits
    except ImportError:
        FallbackStrategy = None
        RedisLimits = None


def create_service_registry(
    registry_type: str = "memory",
    redis_url: Optional[str] = None,
    key_prefix: str = "service_registry:",
    default_ttl: int = 300,
    health_check_interval: int = 30,
    **kwargs
) -> Any:  # Return Any to avoid type issues during development
    """Factory function to create service registry instances

    Args:
        registry_type: Type of registry ("memory", "redis", "redis_v2")
        redis_url: Redis connection URL (required for redis types)
        key_prefix: Prefix for registry keys
        default_ttl: Default TTL for service registrations (seconds)
        health_check_interval: Health check interval (seconds)
        **kwargs: Additional configuration options

    Returns:
        Configured service registry instance

    Raises:
        ValueError: If invalid registry type or missing required parameters
        RuntimeError: If required registry implementation is not available
    """
    if registry_type == "memory":
        if ServiceRegistryImpl is None:
            raise RuntimeError("ServiceRegistryImpl not available")
        return ServiceRegistryImpl()

    elif registry_type == "redis":
        if RedisServiceRegistry is None:
            raise RuntimeError("RedisServiceRegistry not available")
        if redis_url is None:
            raise ValueError("Redis registry requires redis_url parameter")

        return RedisServiceRegistry(
            redis_url=redis_url,
            key_prefix=key_prefix,
            default_ttl=default_ttl,
            health_check_interval=health_check_interval
        )

    elif registry_type == "redis_v2":
        if RedisServiceRegistryV2 is None:
            raise RuntimeError("RedisServiceRegistryV2 not available")
        if redis_url is None:
            raise ValueError("Redis registry requires redis_url parameter")

        # Configure Redis limits for Redis Cloud free tier
        if RedisLimits is not None and FallbackStrategy is not None:
            redis_limits = RedisLimits(
                max_connections=kwargs.get('max_connections', 20),
                max_ops_per_second=kwargs.get('max_ops_per_second', 60),
                max_memory_mb=kwargs.get('max_memory_mb', 25)
            )

            return RedisServiceRegistryV2(
                redis_url=redis_url,
                key_prefix=key_prefix,
                default_ttl=default_ttl,
                health_check_interval=health_check_interval,
                fallback_strategy=FallbackStrategy.HYBRID,
                redis_limits=redis_limits
            )
        else:
            # Fallback to basic Redis registry if advanced features not available
            return RedisServiceRegistry(
                redis_url=redis_url,
                key_prefix=key_prefix,
                default_ttl=default_ttl,
                health_check_interval=health_check_interval
            )

    else:
        raise ValueError(f"Unknown registry type: {registry_type}")


__all__ = [
    # Core interface
    "IServiceRegistry",

    # Registry implementations
    "ServiceRegistryImpl",
    "RedisServiceRegistry",
    "RedisServiceRegistryV2",

    # Data structures
    "ServiceInfo",

    # Configuration
    "FallbackStrategy",
    "RedisLimits",

    # Factory function
    "create_service_registry",
]
