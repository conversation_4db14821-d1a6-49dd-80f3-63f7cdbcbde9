import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import FastAPI
from fastapi.testclient import TestClient

from core.main import create_app


@pytest.fixture
def app():
    """Create test app with mocked dependencies"""
    # Create app using the actual factory
    app = create_app()

    # Mock the state objects that would be created during lifespan
    app.state.service_registry = MagicMock()
    app.state.health_checker = MagicMock()
    app.state.metrics = MagicMock()

    yield app


@pytest.fixture
def client(app):
    """Create test client"""
    with TestClient(app) as client:
        yield client


def test_app_creation(app):
    """Test that the app is created with the correct settings"""
    from core.config.settings import create_settings

    settings = create_settings()

    # Check app settings
    assert app.title == settings.app.app_name
    assert app.version == settings.app.app_version

    # Check that app is properly created
    assert app is not None
    assert isinstance(app, FastAPI)


def test_app_routes(app):
    """Test that the app has the expected routes"""
    routes = [route.path for route in app.routes]

    # Check core API routes are included (note: there might be double prefix due to router setup)
    health_routes = [r for r in routes if "health" in r]
    metrics_routes = [r for r in routes if "metrics" in r]
    services_routes = [r for r in routes if "services" in r]

    assert len(health_routes) > 0, f"No health routes found in {routes}"
    assert len(metrics_routes) > 0, f"No metrics routes found in {routes}"
    assert len(services_routes) > 0, f"No services routes found in {routes}"


def test_app_has_state_objects(app):
    """Test that the app has the required state objects"""
    # Check that state objects are available (mocked in fixture)
    assert hasattr(app.state, "service_registry")
    assert hasattr(app.state, "health_checker")
    assert hasattr(app.state, "metrics")


def test_root_endpoint(client):
    """Test that the app redirects root to docs"""
    response = client.get("/")
    assert response.status_code == 404  # Default behavior is 404 for root


def test_openapi_docs_available(client):
    """Test that OpenAPI docs are available"""
    response = client.get("/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]