"""
Core test configuration and fixtures
"""
import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config.settings import create_settings
from core.infrastructure.service_registry import ServiceRegistryImpl
from core.infrastructure.monitoring.health import SystemHealthCheck
from core.infrastructure.monitoring.metrics import InMemoryMetricsCollector


@pytest.fixture
def settings():
    """Test settings"""
    return create_settings()


@pytest.fixture
def service_registry():
    """Test service registry"""
    return ServiceRegistryImpl()


@pytest.fixture
def health_checker():
    """Test health checker"""
    return SystemHealthCheck(app_name="Test App", version="1.0.0")


@pytest.fixture
def metrics_collector():
    """Test metrics collector"""
    return InMemoryMetricsCollector()


@pytest.fixture
def mock_container():
    """Mock dependency injection container"""
    container = MagicMock()

    async def mock_get_service(interface):
        return MagicMock()

    container.get_service_async = mock_get_service
    return container
