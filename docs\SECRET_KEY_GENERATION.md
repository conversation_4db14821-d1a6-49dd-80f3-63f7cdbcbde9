# Secret Key Generation Guide

This guide explains how to use the `generate_secrets.py` script to create cryptographic secrets for your FastAPI Core Framework application.

## Overview

The secret generation script provides a secure way to generate various types of cryptographic keys and secrets needed for your application, including:

- JWT secret keys
- Application secret keys
- API keys
- Encryption keys
- Database passwords
- Random tokens
- And more...

## Quick Start

### Generate a Single JWT Secret

```bash
python scripts/generate_secrets.py --type jwt --length 32
```

### Generate All Application Secrets

```bash
python scripts/generate_secrets.py --type all --format env --output .env.secrets
```

### Interactive Mode

```bash
python scripts/generate_secrets.py --interactive
```

## Usage Examples

### 1. Generate Specific Secret Types

```bash
# JWT secret key
python scripts/generate_secrets.py --type jwt --length 32

# Application secret key
python scripts/generate_secrets.py --type app --length 32

# API key with prefix
python scripts/generate_secrets.py --type api --prefix "api_" --length 24

# Encryption key (Fernet compatible)
python scripts/generate_secrets.py --type encryption

# Database password
python scripts/generate_secrets.py --type database --length 16

# Random hex token
python scripts/generate_secrets.py --type token --length 32
```

### 2. Output Formats

```bash
# Environment file format (default)
python scripts/generate_secrets.py --type all --format env

# JSON format
python scripts/generate_secrets.py --type all --format json

# YAML format
python scripts/generate_secrets.py --type all --format yaml

# Table format (for viewing)
python scripts/generate_secrets.py --type all --format table
```

### 3. Save to File

```bash
# Save to specific file
python scripts/generate_secrets.py --type all --output .env.secrets

# Save without comments
python scripts/generate_secrets.py --type all --no-comments --output .env.production
```

### 4. Store in Secret Provider

```bash
# Store in Cloudflare Secrets Store
export CLOUDFLARE_ACCOUNT_ID="your-account-id"
export CLOUDFLARE_API_TOKEN="your-api-token"
python scripts/generate_secrets.py --type all --store cloudflare

# Store in encrypted file (development)
python scripts/generate_secrets.py --type all --store file

# Auto-detect provider
python scripts/generate_secrets.py --type all --store auto
```

## Secret Types Generated

When using `--type all`, the script generates:

### Core Application Secrets
- `SECRET_KEY`: Main application secret
- `JWT_SECRET_KEY`: JWT signing key
- `JWT_REFRESH_SECRET_KEY`: JWT refresh token key

### API Keys
- `CORE_API_KEY`: Core service API key
- `ADMIN_API_KEY`: Admin API key
- `SERVICE_API_KEY`: Service-to-service API key

### Database & Cache
- `DATABASE_PASSWORD`: Database password
- `REDIS_PASSWORD`: Redis password

### Encryption Keys
- `ENCRYPTION_KEY`: Fernet encryption key
- `FILE_ENCRYPTION_KEY`: File encryption key

### Security Tokens
- `SESSION_SECRET`: Session management secret
- `CSRF_SECRET`: CSRF protection token
- `WEBHOOK_SECRET`: Webhook signing secret

### Salts and Hashes
- `PASSWORD_SALT`: Password hashing salt
- `HASH_SALT`: General purpose salt

### Monitoring
- `METRICS_TOKEN`: Metrics access token
- `HEALTH_CHECK_TOKEN`: Health check token

### External Services (Placeholders)
- `OPENAI_API_KEY`: OpenAI API key placeholder
- `CLOUDFLARE_API_TOKEN`: Cloudflare token placeholder
- `SUPABASE_SERVICE_KEY`: Supabase key placeholder

## Security Best Practices

### 🔒 Important Security Guidelines

1. **Never commit secrets to version control**
   - Add `.env.secrets` to your `.gitignore`
   - Use different secrets for each environment

2. **Use secure secret management in production**
   - Cloudflare Secrets Store (recommended)
   - Azure Key Vault
   - AWS Secrets Manager
   - HashiCorp Vault

3. **Rotate secrets regularly**
   - Generate new secrets periodically
   - Update all dependent services

4. **Use environment-specific secrets**
   - Different secrets for dev/staging/prod
   - Never use development secrets in production

### Environment Setup

For production use with Cloudflare Secrets Store:

```bash
# Set Cloudflare credentials
export CLOUDFLARE_ACCOUNT_ID="your-account-id"
export CLOUDFLARE_API_TOKEN="your-api-token"

# Generate and store secrets
python scripts/generate_secrets.py --type all --store cloudflare
```

## Integration with Your Application

### Using Generated Secrets

1. **Environment Variables**: Load from `.env.secrets` file
2. **Secret Provider**: Access via your configured secret management system
3. **Configuration**: Update your `core/config/settings.py` to use the new secrets

### Example Environment File

```env
# Generated secrets for FastAPI Core Framework
# Generated on: 2024-01-01T12:00:00.000000
# WARNING: Keep these secrets secure and never commit to version control!

# Core Application Secrets
SECRET_KEY=your-generated-secret-key
JWT_SECRET_KEY=your-generated-jwt-key
JWT_REFRESH_SECRET_KEY=your-generated-refresh-key

# API Keys
CORE_API_KEY=core_your-generated-api-key
ADMIN_API_KEY=admin_your-generated-admin-key
SERVICE_API_KEY=svc_your-generated-service-key

# Database & Cache
DATABASE_PASSWORD=your-generated-db-password
REDIS_PASSWORD=your-generated-redis-password

# Encryption Keys
ENCRYPTION_KEY=your-generated-encryption-key
FILE_ENCRYPTION_KEY=your-generated-file-key
```

## Troubleshooting

### Common Issues

1. **Import errors**: Install dependencies with `pip install -r requirements.txt`
2. **Permission denied**: Make script executable with `chmod +x scripts/generate_secrets.py`
3. **Cloudflare connection failed**: Check your account ID and API token
4. **File not found**: Run from project root directory

### Getting Help

```bash
# Show help
python scripts/generate_secrets.py --help

# Interactive mode for guided generation
python scripts/generate_secrets.py --interactive
```

## Advanced Usage

### Custom Secret Generation

The script can be extended to generate custom secret types by modifying the `SecretGenerator` class in `scripts/generate_secrets.py`.

### Integration with CI/CD

```bash
# Generate secrets in CI/CD pipeline
python scripts/generate_secrets.py --type all --format json --output secrets.json

# Store in secret management system
python scripts/generate_secrets.py --type all --store auto
```

### Batch Operations

```bash
# Generate multiple secret types
for type in jwt app api encryption; do
    python scripts/generate_secrets.py --type $type --output "${type}_secret.env"
done
```

## Related Documentation

- [Secret Management Guide](SECRET_MANAGEMENT.md)
- [Cloudflare Secrets Store Setup](README_CLOUDFLARE_SECRETS.md)
- [Security Configuration](../core/config/README.md)
