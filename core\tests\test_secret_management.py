"""
Tests for Secret Management System

This module contains comprehensive tests for all secret management providers
and the unified SecretConfigProvider.
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from core.config.secret_providers import (
    SecretConfigProvider,
    EncryptedFileSecretProvider,
    SecretValue,
    SecretMetadata
)


class TestEncryptedFileSecretProvider:
    """Test the encrypted file secret provider"""

    @pytest.fixture
    def temp_secrets_file(self):
        """Create a temporary secrets file"""
        with tempfile.NamedTemporaryFile(suffix='.enc', delete=False) as f:
            yield f.name
        # Cleanup
        try:
            os.unlink(f.name)
            key_file = f.name.replace('.enc', '.key')
            if os.path.exists(key_file):
                os.unlink(key_file)
        except:
            pass

    @pytest.mark.asyncio
    async def test_file_provider_basic_operations(self, temp_secrets_file):
        """Test basic secret operations with file provider"""
        # Suppress development warning for tests
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = EncryptedFileSecretProvider(file_path=temp_secrets_file)

            # Test health check
            assert await provider.health_check() is True

            # Test setting a secret
            test_key = "test-secret"
            test_value = "test-value-123"

            result = await provider.set_secret(test_key, test_value)
            assert result is True

            # Test getting the secret
            secret = await provider.get_secret(test_key)
            assert secret is not None
            assert secret.value == test_value
            assert secret.metadata.key == test_key

            # Test listing secrets
            secrets = await provider.list_secrets()
            assert test_key in secrets

            # Test deleting the secret
            result = await provider.delete_secret(test_key)
            assert result is True

            # Verify deletion
            secret = await provider.get_secret(test_key)
            assert secret is None

    @pytest.mark.asyncio
    async def test_file_provider_with_metadata(self, temp_secrets_file):
        """Test file provider with metadata"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = EncryptedFileSecretProvider(file_path=temp_secrets_file)

            test_key = "test-secret-with-metadata"
            test_value = "test-value-456"
            metadata = SecretMetadata(
                key=test_key,
                tags={"environment": "test", "service": "core"}
            )

            # Set secret with metadata
            result = await provider.set_secret(test_key, test_value, metadata)
            assert result is True

            # Get secret and verify metadata
            secret = await provider.get_secret(test_key)
            assert secret is not None
            assert secret.value == test_value
            assert secret.metadata.tags is not None
            assert secret.metadata.tags["environment"] == "test"
            assert secret.metadata.tags["service"] == "core"

    @pytest.mark.asyncio
    async def test_file_provider_versioning(self, temp_secrets_file):
        """Test secret versioning in file provider"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = EncryptedFileSecretProvider(file_path=temp_secrets_file)

            test_key = "versioned-secret"

            # Set initial version
            await provider.set_secret(test_key, "value-v1")
            secret_v1 = await provider.get_secret(test_key)
            assert secret_v1 is not None
            assert secret_v1.value == "value-v1"
            assert secret_v1.metadata.version == "1"

            # Update to new version
            await provider.set_secret(test_key, "value-v2")
            secret_v2 = await provider.get_secret(test_key)
            assert secret_v2 is not None
            assert secret_v2.value == "value-v2"
            assert secret_v2.metadata.version == "2"

            # Get specific version
            secret_v1_retrieved = await provider.get_secret(test_key, version="1")
            assert secret_v1_retrieved is not None
            assert secret_v1_retrieved.value == "value-v1"


class TestSecretConfigProvider:
    """Test the unified secret configuration provider"""

    @pytest.fixture
    def mock_env_provider(self):
        """Mock environment provider"""
        with patch('core.config.providers.EnvironmentConfigProvider') as mock:
            mock_instance = Mock()
            mock.return_value = mock_instance
            yield mock_instance

    @pytest.mark.asyncio
    async def test_auto_detection_file_fallback(self, mock_env_provider):
        """Test auto-detection falls back to file provider"""
        with patch.dict(os.environ, {}, clear=True):
            with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
                provider = SecretConfigProvider(provider_type="auto")

                info = provider.get_provider_info()
                assert info['provider_type'] == 'auto'
                assert 'EncryptedFileSecretProvider' in info['provider_class']

    @pytest.mark.asyncio
    async def test_vault_auto_detection(self, mock_env_provider):
        """Test auto-detection of Vault provider"""
        vault_env = {
            'VAULT_ADDR': 'https://vault.example.com',
            'VAULT_TOKEN': 'test-token',
            'SUPPRESS_DEV_SECRET_WARNING': '1'
        }

        with patch.dict(os.environ, vault_env):
            with patch('core.config.secret_providers.VaultSecretProvider') as mock_vault:
                mock_vault_instance = AsyncMock()
                mock_vault.return_value = mock_vault_instance
                mock_vault_instance.health_check.return_value = True

                provider = SecretConfigProvider(provider_type="auto")

                # Verify Vault provider was created
                mock_vault.assert_called_once()
                call_args = mock_vault.call_args
                assert call_args[1]['vault_url'] == 'https://vault.example.com'
                assert call_args[1]['vault_token'] == 'test-token'

    @pytest.mark.asyncio
    async def test_aws_auto_detection(self, mock_env_provider):
        """Test auto-detection of AWS provider"""
        aws_env = {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test-key',
            'AWS_SECRET_ACCESS_KEY': 'test-secret',
            'SUPPRESS_DEV_SECRET_WARNING': '1'
        }

        with patch.dict(os.environ, aws_env):
            with patch('core.config.providers.AWSSecretsManagerProvider') as mock_aws:
                mock_aws_instance = AsyncMock()
                mock_aws.return_value = mock_aws_instance
                mock_aws_instance.health_check.return_value = True

                provider = SecretConfigProvider(provider_type="auto")

                # Verify AWS provider was created
                mock_aws.assert_called_once()
                call_args = mock_aws.call_args
                assert call_args[1]['region_name'] == 'us-west-2'
                assert call_args[1]['aws_access_key_id'] == 'test-key'

    @pytest.mark.asyncio
    async def test_secret_retrieval_with_fallback(self, mock_env_provider):
        """Test secret retrieval with environment fallback"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            # Mock the secret provider to fail
            with patch('core.config.secret_providers.EncryptedFileSecretProvider') as mock_file:
                mock_file_instance = AsyncMock()
                mock_file.return_value = mock_file_instance
                mock_file_instance.get_secret.return_value = None

                # Setup environment fallback
                mock_env_provider.get.return_value = "env-secret-value"

                provider = SecretConfigProvider(provider_type="file", fallback_to_env=True)

                # Test secret retrieval
                value = await provider.get_secret("test-key", "default")

                # Should get value from environment
                assert value == "env-secret-value"
                mock_env_provider.get.assert_called_once_with("test-key")

    @pytest.mark.asyncio
    async def test_secret_operations(self):
        """Test secret operations through unified provider"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = SecretConfigProvider(provider_type="file")

            # Test setting a secret
            result = await provider.set_secret("test-key", "test-value", tags={"env": "test"})
            assert result is True

            # Test getting the secret
            value = await provider.get_secret("test-key")
            assert value == "test-value"

            # Test listing secrets
            secrets = await provider.list_secrets()
            assert "test-key" in secrets

            # Test health check
            healthy = await provider.health_check()
            assert healthy is True

            # Test deleting the secret
            result = await provider.delete_secret("test-key")
            assert result is True

    def test_provider_info(self):
        """Test provider information retrieval"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = SecretConfigProvider(provider_type="file")

            info = provider.get_provider_info()
            assert info['provider_type'] == 'file'
            assert 'EncryptedFileSecretProvider' in info['provider_class']
            assert info['fallback_to_env'] is True
            # In async test context, health check returns string instead of bool
            assert info['healthy'] == "unknown (call health_check() separately)" or isinstance(info['healthy'], bool)


class TestSecretMetadata:
    """Test secret metadata functionality"""

    def test_secret_metadata_creation(self):
        """Test creating secret metadata"""
        metadata = SecretMetadata(
            key="test-key",
            version="1",
            tags={"environment": "test"}
        )

        assert metadata.key == "test-key"
        assert metadata.version == "1"
        assert metadata.tags is not None
        assert metadata.tags["environment"] == "test"
        assert metadata.rotation_enabled is False

    def test_secret_value_expiration(self):
        """Test secret value expiration checking"""
        from datetime import datetime, timedelta, timezone

        # Non-expired secret
        metadata = SecretMetadata(
            key="test-key",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        secret = SecretValue(value="test", metadata=metadata)
        assert not secret.is_expired()

        # Expired secret
        metadata_expired = SecretMetadata(
            key="test-key",
            expires_at=datetime.now(timezone.utc) - timedelta(hours=1)
        )
        secret_expired = SecretValue(value="test", metadata=metadata_expired)
        assert secret_expired.is_expired()

        # Secret without expiration
        metadata_no_exp = SecretMetadata(key="test-key")
        secret_no_exp = SecretValue(value="test", metadata=metadata_no_exp)
        assert not secret_no_exp.is_expired()


@pytest.mark.integration
class TestSecretManagementIntegration:
    """Integration tests for secret management"""

    @pytest.mark.asyncio
    async def test_end_to_end_secret_lifecycle(self):
        """Test complete secret lifecycle"""
        with patch.dict(os.environ, {'SUPPRESS_DEV_SECRET_WARNING': '1'}):
            provider = SecretConfigProvider(provider_type="file")

            # Create a secret
            secret_key = "integration-test-secret"
            secret_value = "integration-test-value"

            # Set secret
            success = await provider.set_secret(
                secret_key,
                secret_value,
                tags={"test": "integration", "lifecycle": "complete"}
            )
            assert success

            # Retrieve secret
            retrieved_value = await provider.get_secret(secret_key)
            assert retrieved_value == secret_value

            # List secrets (should include our secret)
            secrets = await provider.list_secrets()
            assert secret_key in secrets

            # Update secret
            new_value = "updated-integration-value"
            success = await provider.set_secret(secret_key, new_value)
            assert success

            # Verify update
            updated_value = await provider.get_secret(secret_key)
            assert updated_value == new_value

            # Clean up
            success = await provider.delete_secret(secret_key)
            assert success

            # Verify deletion
            deleted_value = await provider.get_secret(secret_key, "not-found")
            assert deleted_value == "not-found"
