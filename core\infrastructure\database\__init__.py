"""
Database Operations Sub-Module

This module provides database operations and stores:
- User and role data stores
- Database schema migrations
- Database models and schemas
- Generic database operations

Migrated from: core.infrastructure.database, user_store
"""

from typing import Optional

# Import modular implementations first, then fallback to legacy
try:
    from .stores import DatabaseUserStore, DatabaseRoleStore
except ImportError:
    try:
        from ..user_store import DatabaseUserStore, DatabaseRoleStore
    except ImportError:
        DatabaseUserStore = None
        DatabaseRoleStore = None

try:
    from .migrations import DatabaseMigrator
except ImportError:
    DatabaseMigrator = None

try:
    from .models import UserEntity, RoleEntity, PermissionEntity
except ImportError:
    UserEntity = None
    RoleEntity = None
    PermissionEntity = None

# Import database initializer from parent
try:
    from ..database import DatabaseInitializer
except ImportError:
    DatabaseInitializer = None

__all__ = [
    # Stores
    "DatabaseUserStore",
    "DatabaseRoleStore",

    # Migrations
    "DatabaseMigrator",

    # Models
    "UserEntity",
    "RoleEntity",
    "PermissionEntity",

    # Utilities
    "DatabaseInitializer",
]
