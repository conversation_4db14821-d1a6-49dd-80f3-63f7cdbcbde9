﻿# docker-compose.yml
version: '3.8'

services:
  # Core API Framework
  core-api:
    build:
      context: ./core
      dockerfile: Dockerfile
    container_name: laneswap_2-core
    ports:
      - "8000:8000"
      - "9090:9090" # Metrics port
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-here}
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/core_db
    depends_on:
      - redis
      - postgres
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/api/v1/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Translation Service
  translation-service:
    build:
      context: ./services/translation
      dockerfile: Dockerfile
    container_name: translation-service
    ports:
      - "8001:8001"
    environment:
      - CORE_API_URL=http://core-api:8000
      - CORE_API_KEY=${CORE_API_KEY:-your-core-api-key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - core-api
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8001/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: laneswap_2-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  # PostgreSQL for persistent storage
  postgres:
    image: postgres:15-alpine
    container_name: laneswap_2-postgres
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=core_db
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network

  # Nginx as reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: laneswap_2-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - core-api
    networks:
      - app-network

  ssh-monitor:
    image: linuxserver/openssh-server
    container_name: laneswap_2-ssh-monitor
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=UTC
      - PASSWORD_ACCESS=true
      - USER_PASSWORD=secure-password
      - USER_NAME=monitor
    volumes:
      - ./monitor:/config/monitor
    ports:
      - "2222:2222"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
