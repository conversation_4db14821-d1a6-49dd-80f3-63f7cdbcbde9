"""
Integration tests for FastAPI Core Framework

These tests verify real component interactions with minimal mocking.
Only external dependencies (HTTP calls, time) are mocked.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone

from core.infrastructure.service_registry import ServiceRegistryImpl, ServiceInfo
from core.infrastructure.health import SystemHealthCheck
from core.infrastructure.metrics import InMemoryMetricsCollector
from core.infrastructure.security import JWTSecurityProvider


@pytest.mark.integration
class TestServiceLifecycle:
    """Test complete service lifecycle with real component integration"""

    @pytest.fixture
    def components(self):
        """Setup real components for integration testing"""
        registry = ServiceRegistryImpl()
        health_checker = SystemHealthCheck()
        metrics = InMemoryMetricsCollector()
        security = JWTSecurityProvider(secret_key="test-secret-key")

        return {
            "registry": registry,
            "health_checker": health_checker,
            "metrics": metrics,
            "security": security
        }

    @pytest.mark.asyncio
    async def test_full_service_lifecycle(self, components):
        """Test complete service registration -> health check -> restart -> unregister flow"""
        registry = components["registry"]
        health_checker = components["health_checker"]

        # 1. Register a service
        service = ServiceInfo(
            name="integration-test-service",
            url="http://integration-test:8000",
            version="1.0.0",
            api_key="test-api-key"
        )

        # Test real registration
        result = await registry.register_service(
            name=service.name,
            url=str(service.url),
            version=service.version,
            health_endpoint=service.health_endpoint,
            metadata=service.metadata
        )
        assert result["success"] is True
        assert "integration-test-service" in registry.services

        # Get the generated API key for later use
        generated_api_key = result["api_key"]

        # 2. Check system health (SystemHealthCheck doesn't check individual services)
        health_result = await health_checker.check_health()
        assert health_result["status"] == "healthy"
        assert "uptime" in health_result
        assert "version" in health_result

        # 3. Test service restart with mocked HTTP
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "restarting"}
            mock_response.headers = {"content-type": "application/json"}
            mock_post.return_value = mock_response

            # Test real restart integration
            restart_result = await registry.restart_service("integration-test-service")
            assert restart_result["success"] is True
            assert restart_result["service"] == "integration-test-service"

            # Verify service state changes (restart marks service as unhealthy)
            assert restart_result["service"] == "integration-test-service"

        # 4. Test service call with mocked HTTP
        with patch("httpx.AsyncClient.request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"result": "success"}
            mock_response.headers = {"content-type": "application/json"}
            mock_request.return_value = mock_response

            # Mark service as healthy for call test
            registry.services["integration-test-service"].is_healthy = True

            # Test real service call integration
            call_response = await registry.call_service(
                "integration-test-service",
                "/api/test",
                method="GET"
            )

            assert call_response is not None
            assert call_response["status_code"] == 200

            # Verify real URL construction and header handling
            mock_request.assert_called_once_with(
                method="GET",
                url="http://integration-test:8000/api/test",
                json=None,
                headers={"X-API-Key": generated_api_key}
            )

        # 5. Test unregistration
        unregister_result = await registry.unregister_service("integration-test-service")
        assert unregister_result["success"] is True
        assert "integration-test-service" not in registry.services

        # Verify service is gone by listing services
        services = await registry.list_services()
        service_names = [s["name"] for s in services]
        assert "integration-test-service" not in service_names

    @pytest.mark.asyncio
    async def test_health_checker_registry_integration(self, components):
        """Test health checker and registry integration with multiple services"""
        registry = components["registry"]
        health_checker = components["health_checker"]

        # Register multiple services
        services = [
            ServiceInfo(name="service-1", url="http://service1:8000", version="1.0.0"),
            ServiceInfo(name="service-2", url="http://service2:8000", version="1.0.0"),
            ServiceInfo(name="service-3", url="http://service3:8000", version="1.0.0")
        ]

        for service in services:
            await registry.register_service(
                name=service.name,
                url=str(service.url),
                version=service.version,
                health_endpoint=service.health_endpoint,
                metadata=service.metadata
            )

        # Test system health check (SystemHealthCheck doesn't check individual services)
        health_result = await health_checker.check_health()
        assert health_result["status"] == "healthy"
        assert "uptime" in health_result
        assert "version" in health_result

        # Test readiness and liveness checks
        readiness_result = await health_checker.check_readiness()
        assert readiness_result["status"] == "ready"

        liveness_result = await health_checker.check_liveness()
        assert liveness_result["status"] == "alive"

        # Verify all services are registered
        registered_services = await registry.list_services()
        assert len(registered_services) == 3
        service_names = [s["name"] for s in registered_services]
        assert "service-1" in service_names
        assert "service-2" in service_names
        assert "service-3" in service_names

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, components):
        """Test concurrent service operations for race conditions"""
        registry = components["registry"]

        # Test concurrent registrations
        async def register_service(i):
            return await registry.register_service(
                name=f"concurrent-service-{i}",
                url=f"http://service{i}:8000",
                version="1.0.0"
            )

        # Register 10 services concurrently
        tasks = [register_service(i) for i in range(10)]
        results = await asyncio.gather(*tasks)

        # All should succeed
        assert all(result["success"] for result in results)
        assert len(registry.services) == 10

        # Test concurrent health checks (using system health check)
        health_checker = components["health_checker"]

        async def check_system_health(i):
            return await health_checker.check_health()

        # Check system health concurrently
        health_tasks = [check_system_health(i) for i in range(5)]
        health_results = await asyncio.gather(*health_tasks)

        # All should succeed
        assert len(health_results) == 5
        assert all(result["status"] == "healthy" for result in health_results)

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, components):
        """Test real error handling across components"""
        registry = components["registry"]
        health_checker = components["health_checker"]

        # Register a service
        result = await registry.register_service(
            name="error-test-service",
            url="http://error-test:8000",
            version="1.0.0"
        )
        assert result["success"] is True

        # Test system health check (always returns healthy for core system)
        health_result = await health_checker.check_health()
        assert health_result["status"] == "healthy"

        # Test restart with HTTP errors
        with patch("httpx.AsyncClient.post", side_effect=Exception("Restart failed")):
            restart_result = await registry.restart_service("error-test-service")

            assert restart_result["success"] is False
            assert "Restart failed" in restart_result["error"]

        # Test service call with HTTP errors (mark service as healthy first)
        registry.services["error-test-service"].is_healthy = True

        with patch("httpx.AsyncClient.request", side_effect=Exception("Call failed")):
            call_result = await registry.call_service("error-test-service", "/api/test")

            # Service call returns None when there's an exception
            assert call_result is None

    @pytest.mark.asyncio
    async def test_metrics_integration(self, components):
        """Test metrics collection integration with real operations"""
        registry = components["registry"]
        metrics = components["metrics"]

        # Perform real operations and track metrics
        # Track registration
        metrics.increment_counter("service_registrations")
        result = await registry.register_service(
            name="metrics-test-service",
            url="http://metrics-test:8000",
            version="1.0.0"
        )
        assert result["success"] is True

        # Track service calls
        with patch("httpx.AsyncClient.request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"result": "success"}
            mock_response.headers = {"content-type": "application/json"}
            mock_request.return_value = mock_response

            for i in range(5):
                metrics.increment_counter("service_calls")
                await registry.call_service("metrics-test-service", f"/api/endpoint{i}")

        # Track memory usage
        metrics.set_gauge("memory_usage_mb", 512)

        # Track response times
        for time_val in [0.1, 0.2, 0.15, 0.3, 0.25]:
            metrics.record_histogram("response_time_seconds", time_val)

        # Verify real metrics collection
        collected_metrics = await metrics.get_metrics()

        assert collected_metrics["counters"]["service_registrations"] == 1
        assert collected_metrics["counters"]["service_calls"] == 5
        assert collected_metrics["gauges"]["memory_usage_mb"] == 512
        assert collected_metrics["histograms"]["response_time_seconds"]["count"] == 5
        assert collected_metrics["histograms"]["response_time_seconds"]["sum"] == 1.0
        assert collected_metrics["histograms"]["response_time_seconds"]["avg"] == 0.2


@pytest.mark.integration
class TestSecurityIntegration:
    """Test security integration with real components"""

    @pytest.fixture
    def security_provider(self):
        return JWTSecurityProvider(secret_key="test-secret-key")

    @pytest.mark.asyncio
    async def test_authentication_flow(self, security_provider):
        """Test complete authentication flow"""
        # Test authentication with valid credentials
        credentials = {"username": "admin", "password": "admin"}
        user = await security_provider.authenticate(credentials)

        assert user is not None
        assert user["user_id"] == "admin"
        assert user["username"] == "admin"
        assert "admin" in user["roles"]

        # Test authorization
        is_authorized = await security_provider.authorize(user, "users", "read")
        assert is_authorized is True

    def test_jwt_token_lifecycle(self, security_provider):
        """Test JWT token creation and verification"""
        # Create token
        test_data = {"user": "test", "permissions": ["read", "write"]}
        token = security_provider.create_token(test_data)
        assert token is not None
        assert len(token) > 50  # JWT tokens are long

        # Verify token
        payload = security_provider.verify_token(token)
        assert payload is not None
        assert payload["user"] == "test"
        assert payload["permissions"] == ["read", "write"]
        assert payload["type"] == "access"

    def test_password_security(self, security_provider):
        """Test password hashing and verification"""
        password = "secure_password_123"

        # Hash password
        hashed = security_provider.hash_password(password)
        assert hashed != password
        assert len(hashed) > 20

        # Verify password
        assert security_provider.verify_password(password, hashed) is True
        assert security_provider.verify_password("wrong_password", hashed) is False


@pytest.mark.integration
class TestRealWorldScenarios:
    """Test realistic usage scenarios"""

    @pytest.mark.asyncio
    async def test_microservice_discovery_scenario(self):
        """Test a realistic microservice discovery scenario"""
        # Setup: Multiple services registering and discovering each other
        registry = ServiceRegistryImpl()
        health_checker = SystemHealthCheck()

        # Register multiple microservices
        services = [
            ServiceInfo(name="user-service", url="http://user-service:8000", version="1.0.0"),
            ServiceInfo(name="order-service", url="http://order-service:8000", version="1.0.0"),
            ServiceInfo(name="payment-service", url="http://payment-service:8000", version="1.0.0"),
            ServiceInfo(name="notification-service", url="http://notification-service:8000", version="1.0.0")
        ]

        for service in services:
            await registry.register_service(
                name=service.name,
                url=str(service.url),
                version=service.version,
                health_endpoint=service.health_endpoint,
                metadata=service.metadata
            )

        # Scenario: Order service needs to call user service and payment service
        with patch("httpx.AsyncClient.request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"user_id": 123, "balance": 1000}
            mock_request.return_value = mock_response

            # Order service discovers and calls user service
            user_response = await registry.call_service(
                "user-service",
                "/api/users/123",
                method="GET"
            )
            assert user_response is not None
            assert user_response["status_code"] == 200

            # Order service discovers and calls payment service
            payment_response = await registry.call_service(
                "payment-service",
                "/api/charge",
                method="POST",
                data={"amount": 100, "user_id": 123}
            )
            assert payment_response is not None
            assert payment_response["status_code"] == 200

        # Health check system
        health_result = await health_checker.check_health()
        assert health_result["status"] == "healthy"
        assert "uptime" in health_result
        assert "version" in health_result

        # Test service filtering (only healthy services)
        healthy_services = await registry.list_services(only_healthy=True)
        assert len(healthy_services) == 4

    @pytest.mark.asyncio
    async def test_service_failure_recovery_scenario(self):
        """Test service failure and recovery scenario"""
        registry = ServiceRegistryImpl()
        health_checker = SystemHealthCheck()

        # Register a service
        result = await registry.register_service(
            name="critical-service",
            url="http://critical-service:8000",
            version="1.0.0"
        )
        assert result["success"] is True

        # Test system health check
        health_result = await health_checker.check_health()
        assert health_result["status"] == "healthy"

        # Test service restart functionality
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "restarting"}
            mock_response.headers = {"content-type": "application/json"}
            mock_post.return_value = mock_response

            restart_result = await registry.restart_service("critical-service")
            assert restart_result["success"] is True

        # Test service call functionality (mark service as healthy first since restart marks it unhealthy)
        registry.services["critical-service"].is_healthy = True

        with patch("httpx.AsyncClient.request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"result": "success"}
            mock_response.headers = {"content-type": "application/json"}
            mock_request.return_value = mock_response

            call_response = await registry.call_service("critical-service", "/api/test")
            assert call_response is not None
            assert call_response["status_code"] == 200
            assert call_response["content"]["result"] == "success"

        # Test service listing
        services = await registry.list_services()
        assert len(services) == 1
        assert services[0]["name"] == "critical-service"

        # Test service unregistration
        unregister_result = await registry.unregister_service("critical-service")
        assert unregister_result["success"] is True

        # Verify service is removed
        services_after = await registry.list_services()
        assert len(services_after) == 0