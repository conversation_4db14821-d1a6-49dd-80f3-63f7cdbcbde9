#!/usr/bin/env python3
"""
Simple Supabase Integration Tests

This module contains basic tests for the simple Supabase integration.
"""

import asyncio
import os
import pytest
from datetime import datetime

from core.config.settings import SupabaseSettings
from core.infrastructure.simple_supabase import (
    SimpleSupabaseClient,
    get_database_url_for_sqlalchemy,
    print_setup_instructions
)


@pytest.mark.integration
@pytest.mark.asyncio
async def test_simple_supabase_without_credentials():
    """Test simple Supabase client without credentials (should work fine)"""
    settings = SupabaseSettings()  # No credentials
    
    client = SimpleSupabaseClient(settings)
    await client.initialize()
    
    # Should initialize successfully even without credentials
    assert client._initialized is True
    assert client.is_available is False
    
    # Health check should work
    health = await client.health_check()
    assert health["initialized"] is True
    assert health["supabase_available"] is False
    assert health["database_status"] == "using_regular_postgresql"
    
    await client.dispose()


@pytest.mark.integration
@pytest.mark.asyncio
async def test_simple_supabase_with_credentials():
    """Test simple Supabase client with credentials (if available)"""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_anon_key:
        pytest.skip("Supabase credentials not available")
    
    settings = SupabaseSettings(
        supabase_url=supabase_url,
        supabase_anon_key=supabase_anon_key
    )
    
    client = SimpleSupabaseClient(settings)
    await client.initialize()
    
    # Should initialize successfully with credentials
    assert client._initialized is True
    
    # Health check
    health = await client.health_check()
    assert health["initialized"] is True
    assert "timestamp" in health
    
    # Dashboard URL should be available
    dashboard_url = client.get_dashboard_url()
    assert dashboard_url is not None
    assert "supabase.com/dashboard" in dashboard_url
    
    await client.dispose()


@pytest.mark.integration
def test_database_url_selection():
    """Test database URL selection logic"""
    
    # Test 1: No Supabase, no fallback
    settings = SupabaseSettings()
    url = get_database_url_for_sqlalchemy(settings, None)
    assert "postgresql+asyncpg://postgres:password@localhost:5432/laneswap_dev" in url
    
    # Test 2: No Supabase, with fallback
    fallback_url = "postgresql+asyncpg://user:pass@localhost:5432/mydb"
    url = get_database_url_for_sqlalchemy(settings, fallback_url)
    assert url == fallback_url
    
    # Test 3: With Supabase URL
    supabase_db_url = "postgresql://postgres:<EMAIL>:5432/postgres"
    settings = SupabaseSettings(supabase_db_url=supabase_db_url)
    url = get_database_url_for_sqlalchemy(settings, fallback_url)
    assert url == supabase_db_url


@pytest.mark.integration
def test_supabase_settings():
    """Test Supabase settings configuration"""
    
    # Test with environment variables
    os.environ["SUPABASE_URL"] = "https://test.supabase.co"
    os.environ["SUPABASE_ANON_KEY"] = "test-key"
    os.environ["SUPABASE_DB_URL"] = "postgresql://<EMAIL>:5432/postgres"
    
    settings = SupabaseSettings()
    
    assert settings.supabase_url == "https://test.supabase.co"
    assert settings.supabase_anon_key == "test-key"
    assert settings.supabase_db_url == "postgresql://<EMAIL>:5432/postgres"
    
    # Clean up
    del os.environ["SUPABASE_URL"]
    del os.environ["SUPABASE_ANON_KEY"]
    del os.environ["SUPABASE_DB_URL"]


@pytest.mark.integration
def test_dashboard_url_generation():
    """Test dashboard URL generation"""
    settings = SupabaseSettings(
        supabase_url="https://abcdefghijklmnop.supabase.co"
    )
    
    client = SimpleSupabaseClient(settings)
    dashboard_url = client.get_dashboard_url()
    
    assert dashboard_url == "https://supabase.com/dashboard/project/abcdefghijklmnop"


def main():
    """Main function for manual testing"""
    async def run_tests():
        print("🚀 Simple Supabase Integration Tests")
        print("=" * 50)
        
        # Test without credentials
        print("\n1. Testing without Supabase credentials...")
        await test_simple_supabase_without_credentials()
        print("✅ Works fine without Supabase (uses regular PostgreSQL)")
        
        # Test database URL selection
        print("\n2. Testing database URL selection...")
        test_database_url_selection()
        print("✅ Database URL selection logic works")
        
        # Test settings
        print("\n3. Testing Supabase settings...")
        test_supabase_settings()
        print("✅ Settings configuration works")
        
        # Test dashboard URL
        print("\n4. Testing dashboard URL generation...")
        test_dashboard_url_generation()
        print("✅ Dashboard URL generation works")
        
        # Test with credentials if available
        print("\n5. Testing with Supabase credentials (if available)...")
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")
        
        if supabase_url and supabase_anon_key:
            await test_simple_supabase_with_credentials()
            print("✅ Supabase integration works with credentials")
        else:
            print("⚠️  Supabase credentials not found - skipping credential tests")
            print("\n📋 To test with Supabase, add these to your .env:")
            print("   SUPABASE_URL=https://your-project.supabase.co")
            print("   SUPABASE_ANON_KEY=your-anon-key")
            print("   SUPABASE_DB_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres")
        
        print("\n🎉 Simple Supabase integration tests completed!")
        print("\n💡 Benefits of this integration:")
        print("   ✅ Your existing code works unchanged")
        print("   ✅ Optional Supabase - falls back to regular PostgreSQL")
        print("   ✅ Visual database dashboard when using Supabase")
        print("   ✅ Automatic backups and scaling with Supabase")
        print("   ✅ Easy team collaboration")
        
        print("\n🔧 Setup Instructions:")
        print_setup_instructions()
    
    return asyncio.run(run_tests())


if __name__ == "__main__":
    main()
