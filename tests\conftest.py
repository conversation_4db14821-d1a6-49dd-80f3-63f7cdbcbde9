"""
System-wide test configuration
"""
import pytest
import asyncio
import sys
from pathlib import Path

# Add project root to path
_project_root = Path(__file__).parent
sys.path.insert(0, str(_project_root))

# Import core test fixtures
from core.tests.conftest import *


@pytest.fixture(scope="session")
def project_root():
    """Project root directory"""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def test_data_dir(project_root):
    """Test data directory"""
    return project_root / "tests" / "data"
