﻿import os
import signal
import sys
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>eader
from typing import Dict, Optional
import asyncio

# Service template router for common endpoints
service_template = APIRouter()

# API Key security
api_key_header = <PERSON>KeyHeader(name="X-API-Key", auto_error=False)


async def verify_api_key(api_key: str = Depends(api_key_header)):
    """Verify service API key"""
    expected_key = os.getenv("SERVICE_API_KEY")
    if not api_key or api_key != expected_key:
        raise HTTPException(
            status_code=401,
            detail="Invalid or missing API Key"
        )
    return {"authenticated": True}


@service_template.post("/admin/restart")
async def restart_service(request: Request, auth: Dict = Depends(verify_api_key)):
    """Restart the service gracefully"""
    # Get the process ID
    pid = os.getpid()
    
    # Schedule a restart after response is sent
    async def restart():
        # Wait a moment to ensure the response is sent
        await asyncio.sleep(1)
        # Send SIGTERM to self - this should trigger the service container to restart
        os.kill(pid, signal.SIGTERM)
    
    # Schedule the restart
    asyncio.create_task(restart())
    
    return {
        "status": "restarting",
        "pid": pid,
        "message": "Service restart initiated"
    }


def create_service_app(
    title: str,
    version: str,
    description: str = "FastAPI Service",
    admin_enabled: bool = True
) -> FastAPI:
    """Create a standard service application with common endpoints"""
    app = FastAPI(title=title, version=version, description=description)
    
    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy"}
    
    # Add admin endpoints if enabled
    if admin_enabled:
        app.include_router(service_template, tags=["admin"])
    
    return app
